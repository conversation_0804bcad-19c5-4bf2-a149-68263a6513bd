/**
 * Enhanced CAS Detector
 * 
 * Advanced component for detecting CAS content beyond traditional CAS Part resources.
 * Uses filename analysis, resource patterns, and content heuristics to identify
 * hair, clothing, and other CAS content that may not have explicit CAS Part resources.
 * 
 * Part of Enhanced Subcategory Detection phase.
 */

import type { ResourceEntry } from '@s4tk/models/types';
import { CASCategory, CASPartInfo, DEFAULT_CAS_PART_INFO } from './types';
import { HairStyleClassifier } from './HairStyleClassifier';

/**
 * Enhanced CAS content detection beyond traditional resource types
 */
export class EnhancedCASDetector {
    
    /**
     * CAS filename patterns for different categories
     */
    private static readonly CAS_FILENAME_PATTERNS = {
        [CASCategory.HAIR]: [
            'hair', 'hairstyle', 'locks', 'mane', 'coiffure'
        ],
        [CASCategory.CLOTHING]: [
            'clothing', 'outfit', 'dress', 'shirt', 'pants', 'skirt',
            'top', 'bottom', 'jacket', 'coat', 'sweater'
        ],
        [CASCategory.MAKEUP]: [
            'makeup', 'lipstick', 'eyeshadow', 'blush', 'eyeliner',
            'mascara', 'foundation', 'concealer'
        ],
        [CASCategory.ACCESSORIES]: [
            'accessory', 'jewelry', 'necklace', 'earrings', 'bracelet',
            'ring', 'glasses', 'hat', 'cap'
        ],
        [CASCategory.SKIN_DETAILS]: [
            'skin', 'tattoo', 'scar', 'freckles', 'mole', 'birthmark'
        ]
    };

    /**
     * Detects CAS content from filename and creates appropriate CAS part info
     * 
     * @param filename - The mod filename
     * @param resources - Available resources for additional analysis
     * @returns Array of detected CAS parts or empty array if none detected
     */
    public static detectCASFromFilename(filename: string, resources: ResourceEntry[]): CASPartInfo[] {
        const detectedParts: CASPartInfo[] = [];
        const lowerFilename = filename.toLowerCase();
        
        // Check each CAS category
        for (const [category, patterns] of Object.entries(this.CAS_FILENAME_PATTERNS)) {
            if (patterns.some(pattern => lowerFilename.includes(pattern))) {
                const casPartInfo = this.createCASPartFromFilename(
                    filename, 
                    category as CASCategory,
                    resources
                );
                detectedParts.push(casPartInfo);
                break; // Only assign to one primary category
            }
        }
        
        return detectedParts;
    }

    /**
     * Creates a CAS part info object from filename analysis
     */
    private static createCASPartFromFilename(
        filename: string,
        category: CASCategory,
        resources: ResourceEntry[]
    ): CASPartInfo {
        const casPartInfo: CASPartInfo = {
            ...DEFAULT_CAS_PART_INFO,
            category,
            subcategory: this.determineSubcategory(filename, category),
            description: this.generateDescription(filename, category)
        };

        // Set category-specific flags
        this.setCategoryFlags(casPartInfo, category);

        // Enhanced hair classification
        if (category === CASCategory.HAIR) {
            casPartInfo.hairDetails = HairStyleClassifier.classifyHairStyle(filename);
            casPartInfo.subcategory = this.generateHairSubcategory(casPartInfo.hairDetails);
            casPartInfo.description = HairStyleClassifier.getHairDescription(casPartInfo.hairDetails);
        }

        // Add detection metadata
        casPartInfo.tags = this.extractTags(filename);

        return casPartInfo;
    }

    /**
     * Determines subcategory based on filename and category
     */
    private static determineSubcategory(filename: string, category: CASCategory): string {
        const lowerFilename = filename.toLowerCase();

        switch (category) {
            case CASCategory.HAIR:
                return 'hair'; // Will be enhanced by hair classifier
                
            case CASCategory.CLOTHING:
                if (lowerFilename.includes('dress')) return 'dress';
                if (lowerFilename.includes('top') || lowerFilename.includes('shirt')) return 'tops';
                if (lowerFilename.includes('bottom') || lowerFilename.includes('pants')) return 'bottoms';
                if (lowerFilename.includes('shoes')) return 'shoes';
                return 'clothing';
                
            case CASCategory.MAKEUP:
                if (lowerFilename.includes('lipstick')) return 'lipstick';
                if (lowerFilename.includes('eyeshadow')) return 'eyeshadow';
                if (lowerFilename.includes('blush')) return 'blush';
                return 'makeup';
                
            case CASCategory.ACCESSORIES:
                if (lowerFilename.includes('glasses')) return 'glasses';
                if (lowerFilename.includes('jewelry')) return 'jewelry';
                if (lowerFilename.includes('hat')) return 'hat';
                return 'accessories';
                
            default:
                return category;
        }
    }

    /**
     * Sets category-specific boolean flags
     */
    private static setCategoryFlags(casPartInfo: CASPartInfo, category: CASCategory): void {
        // Reset all flags
        casPartInfo.isHair = false;
        casPartInfo.isClothing = false;
        casPartInfo.isMakeup = false;
        casPartInfo.isAccessory = false;

        // Set appropriate flag
        switch (category) {
            case CASCategory.HAIR:
                casPartInfo.isHair = true;
                break;
            case CASCategory.CLOTHING:
                casPartInfo.isClothing = true;
                break;
            case CASCategory.MAKEUP:
                casPartInfo.isMakeup = true;
                break;
            case CASCategory.ACCESSORIES:
                casPartInfo.isAccessory = true;
                break;
        }
    }

    /**
     * Generates enhanced subcategory for hair based on classification
     */
    private static generateHairSubcategory(hairDetails: any): string {
        if (!hairDetails) return 'hair';
        
        const parts: string[] = [];
        
        if (hairDetails.length && hairDetails.length !== 'unknown') {
            parts.push(hairDetails.length);
        }
        
        if (hairDetails.style && hairDetails.style.length > 0 && !hairDetails.style.includes('unknown')) {
            parts.push(hairDetails.style[0]); // Use primary style
        }
        
        return parts.length > 0 ? parts.join('_') + '_hair' : 'hair';
    }

    /**
     * Generates description based on filename and category
     */
    private static generateDescription(filename: string, category: CASCategory): string {
        const cleanName = filename
            .replace(/\.(package|ts4script)$/i, '')
            .replace(/_/g, ' ')
            .replace(/([A-Z])/g, ' $1')
            .trim();

        return `${category} content: ${cleanName}`;
    }

    /**
     * Extracts relevant tags from filename
     */
    private static extractTags(filename: string): string[] {
        const tags: string[] = [];
        const lowerFilename = filename.toLowerCase();

        // Age-related tags
        if (lowerFilename.includes('child')) tags.push('child');
        if (lowerFilename.includes('teen')) tags.push('teen');
        if (lowerFilename.includes('adult')) tags.push('adult');
        if (lowerFilename.includes('elder')) tags.push('elder');

        // Gender-related tags
        if (lowerFilename.includes('male') && !lowerFilename.includes('female')) tags.push('male');
        if (lowerFilename.includes('female') && !lowerFilename.includes('male')) tags.push('female');
        if (lowerFilename.includes('unisex')) tags.push('unisex');

        // Style tags
        if (lowerFilename.includes('formal')) tags.push('formal');
        if (lowerFilename.includes('casual')) tags.push('casual');
        if (lowerFilename.includes('athletic')) tags.push('athletic');

        return tags;
    }

    /**
     * Checks if a filename suggests CAS content
     */
    public static isCASFilename(filename: string): boolean {
        const lowerFilename = filename.toLowerCase();
        
        return Object.values(this.CAS_FILENAME_PATTERNS)
            .flat()
            .some(pattern => lowerFilename.includes(pattern));
    }

    /**
     * Gets the most likely CAS category from filename
     */
    public static getCASCategoryFromFilename(filename: string): CASCategory {
        const lowerFilename = filename.toLowerCase();
        
        for (const [category, patterns] of Object.entries(this.CAS_FILENAME_PATTERNS)) {
            if (patterns.some(pattern => lowerFilename.includes(pattern))) {
                return category as CASCategory;
            }
        }
        
        return CASCategory.UNKNOWN;
    }

    /**
     * Calculates confidence score for CAS detection
     */
    public static calculateCASConfidence(filename: string, resources: ResourceEntry[]): number {
        let confidence = 0;
        
        // Filename-based confidence
        if (this.isCASFilename(filename)) {
            confidence += 0.6;
        }
        
        // Resource-based confidence (if we have texture resources, likely CAS)
        const hasTextures = resources.some(r => 
            r.key.type === 0x00B2D882 || // DDS Image
            r.key.type === 0x2F7D0004    // DST Image
        );
        
        if (hasTextures) {
            confidence += 0.2;
        }
        
        // StringTable presence (common in CAS mods)
        const hasStringTable = resources.some(r => r.key.type === 0x220557DA);
        if (hasStringTable) {
            confidence += 0.1;
        }
        
        return Math.min(confidence, 1.0);
    }
}
