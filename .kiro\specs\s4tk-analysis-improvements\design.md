# Design Document

## Overview

This design document outlines the architecture and implementation approach for enhancing the existing PackageAnalysisService with comprehensive S4TK improvements. The design focuses on leveraging S4TK's specialized resource models, performance optimizations, and advanced analysis capabilities while maintaining backward compatibility and providing a clean upgrade path.

The solution introduces a layered architecture with specialized analyzers, async processing, comprehensive error handling, and extensible resource analysis patterns.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    PackageAnalysisService                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Quick Analysis │  │ Detailed Analysis│  │Batch Analysis│ │
│  │     (Async)     │  │     (Async)     │  │   (Async)    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Specialized Resource Analyzers              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SimData   │ │StringTable  │ │   Tuning    │ │  Image  │ │
│  │  Analyzer   │ │  Analyzer   │ │  Analyzer   │ │Analyzer │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    S4TK Integration Layer                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   Package   │ │  Resource   │ │   Hashing   │ │  Error  │ │
│  │   Manager   │ │  Parser     │ │   Service   │ │ Handler │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      S4TK Core Libraries                    │
│     @s4tk/models  @s4tk/hashing  @s4tk/xml-dom             │
│     @s4tk/compression  @s4tk/encoding  @s4tk/images        │
└─────────────────────────────────────────────────────────────┘
```

### Component Design

#### 1. Enhanced PackageAnalysisService

The main service will be enhanced with:
- **Async processing methods** for all analysis operations
- **Selective resource loading** based on analysis type and category
- **Specialized resource analysis** using S4TK models
- **Comprehensive error handling** with S4TK-specific context
- **Resource validation** and integrity checking

#### 2. Specialized Resource Analyzers

**SimDataAnalyzer**
- Parses SimData resources using `SimDataResource`
- Extracts instance and schema information
- Provides XML conversion and preview
- Analyzes data complexity and structure

**StringTableAnalyzer**
- Parses string tables using `StringTableResource`
- Detects custom vs EA strings
- Identifies duplicates and calculates statistics
- Provides locale detection and analysis

**TuningAnalyzer**
- Parses tuning files using `CombinedTuningResource`
- Uses XML DOM for structure analysis
- Identifies modifications and overrides
- Extracts dependencies and references

**ImageAnalyzer**
- Parses DDS images using `DdsImageResource`
- Extracts image metadata and properties
- Detects format and compression information
- Calculates optimization metrics

#### 3. S4TK Integration Services

**PackageManager**
- Handles S4TK Package operations with optimized loading
- Manages resource filtering and selective loading
- Provides async package operations
- Handles package merging and batch operations

**ResourceParser**
- Coordinates specialized resource parsing
- Manages resource type detection and routing
- Handles parsing errors and fallbacks
- Provides resource validation

**HashingService**
- Implements FNV hashing using S4TK utilities
- Provides resource key formatting
- Handles conflict detection hashing
- Manages TGI signature generation

**ErrorHandler**
- Provides S4TK-specific error handling
- Categorizes errors and provides suggestions
- Manages error logging and reporting
- Handles graceful fallbacks

## Components and Interfaces

### Core Interfaces

```typescript
// Enhanced analysis result interfaces
interface EnhancedQuickAnalysisResult extends QuickAnalysisResult {
    compressionStats: Record<string, number>;
    validationIssues: string[];
    s4tkVersion: string;
}

interface EnhancedDetailedAnalysisResult extends DetailedAnalysisResult {
    specializedResources: SpecializedResourceAnalysis[];
    resourceValidation: ResourceValidationResult;
    performanceMetrics: AnalysisPerformanceMetrics;
}

interface SpecializedResourceAnalysis {
    resourceKey: string;
    resourceType: string;
    analysisType: 'simdata' | 'stringtable' | 'tuning' | 'image' | 'generic';
    metadata: any;
    preview?: string;
    issues: string[];
}

interface ResourceValidationResult {
    isValid: boolean;
    totalResources: number;
    validResources: number;
    issues: ValidationIssue[];
}

interface ValidationIssue {
    resourceKey: string;
    severity: 'error' | 'warning' | 'info';
    message: string;
    suggestion?: string;
}

interface AnalysisPerformanceMetrics {
    totalTime: number;
    quickAnalysisTime: number;
    detailedAnalysisTime: number;
    resourceCount: number;
    memoryUsage: number;
}
```

### Service Interfaces

```typescript
interface ISpecializedResourceAnalyzer {
    canAnalyze(resourceType: number): boolean;
    analyze(entry: ResourceEntry): Promise<SpecializedResourceAnalysis>;
    validate(entry: ResourceEntry): ValidationResult;
}

interface IPackageManager {
    loadPackageSelective(buffer: Buffer, options: SelectiveLoadingOptions): Promise<Package>;
    extractResourcesAsync(buffer: Buffer, options: PackageFileReadingOptions): Promise<ResourceEntry[]>;
    validatePackage(buffer: Buffer): Promise<PackageValidationResult>;
}

interface IHashingService {
    generateResourceHash(resource: any): string;
    formatResourceKey(key: ResourceKey): string;
    generateTGISignature(type: number, group: number, instance: bigint): string;
}

interface IErrorHandler {
    handleS4TKError(error: any, context: string, filePath: string): ErrorInfo;
    categorizeError(error: any): ErrorCategory;
    getSuggestion(error: any): string;
}
```

## Data Models

### Enhanced Resource Models

```typescript
// SimData Analysis Model
interface SimDataAnalysis {
    instanceCount: number;
    schemaCount: number;
    xmlPreview: string;
    complexity: number;
    dataTypes: string[];
    relationships: ResourceRelationship[];
}

// String Table Analysis Model
interface StringTableAnalysis {
    entryCount: number;
    locale: string;
    hasCustomStrings: boolean;
    duplicateCount: number;
    averageStringLength: number;
    longestString: string;
    customStringPatterns: string[];
}

// Tuning Analysis Model
interface TuningAnalysis {
    tuningClass: string;
    instanceId: string;
    moduleId: string;
    tuningName: string;
    childCount: number;
    hasComments: boolean;
    modifiedElements: string[];
    dependencies: string[];
    complexity: number;
    xmlPreview: string;
}

// Image Analysis Model
interface ImageAnalysis {
    width: number;
    height: number;
    isShuffled: boolean;
    format: string;
    mipmapCount: number;
    fileSize: number;
    compressionRatio: number;
    colorDepth: number;
}
```

### Configuration Models

```typescript
interface AnalysisConfiguration {
    enableAsyncProcessing: boolean;
    enableSpecializedAnalysis: boolean;
    enableResourceValidation: boolean;
    enablePerformanceMetrics: boolean;
    maxConcurrentAnalysis: number;
    resourceAnalysisTimeout: number;
    enablePreviewGeneration: boolean;
    previewMaxLength: number;
}

interface SelectiveLoadingOptions extends PackageFileReadingOptions {
    analysisType: 'quick' | 'detailed' | 'conflict';
    category?: ModCategory;
    enableSpecializedParsing: boolean;
    enableValidation: boolean;
}
```

## Error Handling

### Error Classification System

```typescript
enum ErrorCategory {
    PACKAGE_LOADING = 'package_loading',
    RESOURCE_PARSING = 'resource_parsing',
    COMPRESSION = 'compression',
    VALIDATION = 'validation',
    PERFORMANCE = 'performance',
    NETWORK = 'network',
    UNKNOWN = 'unknown'
}

interface ErrorInfo {
    category: ErrorCategory;
    severity: 'critical' | 'error' | 'warning' | 'info';
    message: string;
    context: string;
    filePath: string;
    timestamp: string;
    suggestion?: string;
    recoverable: boolean;
    errorCode?: string;
}
```

### Error Handling Strategy

1. **Graceful Degradation**: When specialized analysis fails, fall back to generic analysis
2. **Detailed Context**: Provide comprehensive error information for debugging
3. **User-Friendly Messages**: Convert technical errors to actionable user messages
4. **Recovery Suggestions**: Provide specific suggestions based on error type
5. **Logging Strategy**: Comprehensive logging with different levels for different audiences

## Testing Strategy

### Unit Testing Approach

1. **Specialized Analyzer Tests**
   - Test each resource type analyzer independently
   - Mock S4TK dependencies for isolated testing
   - Test error scenarios and edge cases
   - Validate analysis results against known good data

2. **Integration Tests**
   - Test full analysis pipeline with real mod files
   - Validate async processing behavior
   - Test error handling and recovery
   - Performance regression testing

3. **Performance Tests**
   - Memory usage validation
   - Processing time benchmarks
   - Concurrent processing tests
   - Large file handling tests

### Test Data Strategy

1. **Sample Mod Collection**: Curated collection of different mod types
2. **Edge Case Files**: Corrupted, malformed, and unusual files
3. **Performance Test Files**: Large files and collections for stress testing
4. **Regression Test Suite**: Files that previously caused issues

## Performance Considerations

### Memory Optimization

1. **Selective Loading**: Only load resources needed for specific analysis
2. **Lazy Parsing**: Parse resources only when detailed analysis is requested
3. **Resource Pooling**: Reuse parsed resources across multiple analyses
4. **Garbage Collection**: Proper cleanup of large objects after analysis

### Processing Optimization

1. **Async Processing**: Non-blocking operations for UI responsiveness
2. **Batch Processing**: Efficient handling of multiple files
3. **Caching Strategy**: Cache analysis results for repeated operations
4. **Worker Threads**: CPU-intensive operations in separate threads

### Scalability Considerations

1. **Streaming Analysis**: Handle very large mod collections
2. **Incremental Processing**: Process only changed files
3. **Database Integration**: Store analysis results for quick retrieval
4. **Distributed Processing**: Support for processing across multiple cores

## Security Considerations

### Input Validation

1. **Buffer Validation**: Validate all input buffers before processing
2. **Resource Validation**: Check resource integrity and format
3. **Path Validation**: Sanitize file paths and prevent directory traversal
4. **Size Limits**: Enforce reasonable limits on file and resource sizes

### Error Information Security

1. **Sensitive Data**: Avoid exposing sensitive file paths in errors
2. **Error Sanitization**: Clean error messages before user display
3. **Logging Security**: Ensure logs don't contain sensitive information
4. **Resource Protection**: Prevent access to unauthorized resources

## Migration Strategy

### Backward Compatibility

1. **Interface Preservation**: Keep all existing public interfaces
2. **Gradual Enhancement**: Add new features without breaking existing functionality
3. **Feature Flags**: Allow enabling/disabling new features
4. **Deprecation Path**: Provide clear migration path for deprecated features

### Rollout Plan

1. **Phase 1**: Core infrastructure and async methods
2. **Phase 2**: Specialized resource analyzers
3. **Phase 3**: Advanced features and optimizations
4. **Phase 4**: Performance tuning and final optimizations

## Monitoring and Observability

### Metrics Collection

1. **Performance Metrics**: Analysis times, memory usage, throughput
2. **Error Metrics**: Error rates, error types, recovery success
3. **Usage Metrics**: Feature usage, analysis types, file types
4. **Quality Metrics**: Analysis accuracy, validation success rates

### Logging Strategy

1. **Structured Logging**: JSON-formatted logs for easy parsing
2. **Log Levels**: Appropriate log levels for different audiences
3. **Context Preservation**: Maintain context across async operations
4. **Performance Logging**: Track performance-critical operations

### Health Checks

1. **Service Health**: Monitor service availability and responsiveness
2. **Resource Health**: Monitor memory usage and resource leaks
3. **Analysis Quality**: Monitor analysis success rates and accuracy
4. **Performance Health**: Monitor performance degradation over time