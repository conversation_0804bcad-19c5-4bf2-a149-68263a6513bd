#!/usr/bin/env node

/**
 * Llama-Logic Improvements Implementation
 * 
 * Implements the research findings from Llama-Logic to improve
 * package metadata extraction from 9% to 50%+
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface LlamaLogicTestResult {
    filePath: string;
    fileType: string;
    
    // Current detection
    currentAuthor?: string;
    currentSource?: string;
    
    // New detection methods
    manifestAuthor?: string;
    stringTableAuthor?: string;
    enhancedPatternAuthor?: string;
    
    // Best result
    bestAuthor?: string;
    bestSource?: string;
    improvement: boolean;
}

class LlamaLogicImplementationTester {
    private analysisService: PackageAnalysisService;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
    }
    
    /**
     * Tests Llama-Logic improvements against package files
     */
    public async testLlamaLogicImprovements(): Promise<void> {
        console.log('🔬 TESTING LLAMA-LOGIC IMPROVEMENTS');
        console.log('Implementing research findings to improve package metadata extraction\n');
        
        const assetsDir = path.join(process.cwd(), 'assets');
        
        if (!fs.existsSync(assetsDir)) {
            console.error('❌ Assets directory not found:', assetsDir);
            return;
        }
        
        // Focus on package files where we need improvement
        const packageFiles = fs.readdirSync(assetsDir)
            .filter(file => file.endsWith('.package'))
            .sort();
        
        console.log(`📦 Found ${packageFiles.length} package files to analyze\n`);
        
        const results: LlamaLogicTestResult[] = [];
        
        for (const file of packageFiles) {
            const filePath = path.join(assetsDir, file);
            console.log(`🔍 Analyzing: ${file}`);
            
            const result = await this.testFile(filePath);
            results.push(result);
            
            // Show immediate results
            const status = result.improvement ? '📈 IMPROVED' : '➖ No change';
            const author = result.bestAuthor || 'No author';
            console.log(`   ${status}: ${author} (${result.bestSource})\n`);
        }
        
        this.generateImprovementReport(results);
    }
    
    /**
     * Tests a single package file with new detection methods
     */
    private async testFile(filePath: string): Promise<LlamaLogicTestResult> {
        const fileName = path.basename(filePath);
        
        try {
            const buffer = fs.readFileSync(filePath);
            
            // Get current detection
            const currentAnalysis = await this.analysisService.detailedAnalyzeAsync(buffer, filePath);
            const currentAuthor = currentAnalysis.metadata.author;
            const currentSource = currentAnalysis.metadata.metadataSource;
            
            // Test new detection methods
            const manifestAuthor = await this.detectLlamaLogicManifest(buffer, filePath);
            const stringTableAuthor = await this.detectStringTableAuthor(buffer, filePath);
            const enhancedPatternAuthor = this.detectEnhancedPatterns(fileName);
            
            // Determine best result
            const bestResult = this.selectBestAuthor({
                current: currentAuthor,
                manifest: manifestAuthor,
                stringTable: stringTableAuthor,
                enhancedPattern: enhancedPatternAuthor
            });
            
            return {
                filePath: fileName,
                fileType: 'package',
                currentAuthor,
                currentSource,
                manifestAuthor,
                stringTableAuthor,
                enhancedPatternAuthor,
                bestAuthor: bestResult.author,
                bestSource: bestResult.source,
                improvement: !!bestResult.author && bestResult.author !== currentAuthor
            };
            
        } catch (error) {
            console.log(`   ❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
            
            return {
                filePath: fileName,
                fileType: 'package',
                improvement: false
            };
        }
    }
    
    /**
     * Detects Llama-Logic manifests in package files
     */
    private async detectLlamaLogicManifest(buffer: Buffer, filePath: string): Promise<string | undefined> {
        try {
            // This would be implemented by parsing SnippetTuning resources
            // For now, simulate the detection logic
            
            // Look for manifest indicators in the file
            const content = buffer.toString('binary');
            
            // Check for Llama-Logic manifest signatures
            if (content.includes('llamalogic') || content.includes('ModFileManifest')) {
                // Parse manifest structure (simplified simulation)
                const creatorMatch = content.match(/creator[s]?["\s]*:?\s*["']?([^"'\n\r]+)/i);
                if (creatorMatch) {
                    return creatorMatch[1].trim();
                }
            }
            
            return undefined;
        } catch (error) {
            return undefined;
        }
    }
    
    /**
     * Detects author from StringTable resources
     */
    private async detectStringTableAuthor(buffer: Buffer, filePath: string): Promise<string | undefined> {
        try {
            // This would be implemented by parsing StringTable resources using S4TK
            // For now, simulate the detection logic
            
            const content = buffer.toString('binary');
            
            // Look for common author attribution patterns in string resources
            const patterns = [
                /created?\s+by[:\s]+([^.\n\r]+)/i,
                /author[:\s]+([^.\n\r]+)/i,
                /made\s+by[:\s]+([^.\n\r]+)/i,
                /©\s*([^.\n\r]+)/i,
                /credit[s]?[:\s]+([^.\n\r]+)/i
            ];
            
            for (const pattern of patterns) {
                const match = content.match(pattern);
                if (match) {
                    const author = match[1].trim();
                    // Filter out common false positives
                    if (author.length > 2 && author.length < 50 && 
                        !author.includes('EA') && !author.includes('Maxis')) {
                        return author;
                    }
                }
            }
            
            return undefined;
        } catch (error) {
            return undefined;
        }
    }
    
    /**
     * Enhanced pattern recognition for package files
     */
    private detectEnhancedPatterns(fileName: string): string | undefined {
        // Remove extension
        const baseName = path.basename(fileName, '.package');
        
        // Enhanced patterns based on research
        const patterns = [
            // Brand patterns
            { pattern: /^TwistedCat_/, author: 'TwistedCat' },
            { pattern: /^Zero_/, author: 'Zero' },
            { pattern: /^Sims4Luxury_/, author: 'Sims4Luxury' },
            { pattern: /^Madlen_/, author: 'Madlen' },
            { pattern: /^Greenllamas_/, author: 'Greenllamas' },
            
            // CamelCase patterns (BrandProduct)
            { pattern: /^([A-Z][a-z]+)([A-Z].+)$/, author: '$1' },
            
            // Underscore patterns
            { pattern: /^([^_]+)_(.+)$/, author: '$1' },
            
            // Hyphen patterns
            { pattern: /^([^-]+)-(.+)$/, author: '$1' },
            
            // Parentheses patterns
            { pattern: /^\[([^\]]+)\]/, author: '$1' },
            { pattern: /^([^(]+)\(/, author: '$1' }
        ];
        
        for (const { pattern, author } of patterns) {
            const match = baseName.match(pattern);
            if (match) {
                if (author.includes('$1')) {
                    const extractedAuthor = match[1];
                    // Validate extracted author
                    if (extractedAuthor.length > 2 && extractedAuthor.length < 30) {
                        return extractedAuthor;
                    }
                } else {
                    return author;
                }
            }
        }
        
        return undefined;
    }
    
    /**
     * Selects the best author from multiple detection methods
     */
    private selectBestAuthor(authors: {
        current?: string;
        manifest?: string;
        stringTable?: string;
        enhancedPattern?: string;
    }): { author?: string; source: string } {
        
        // Priority order: manifest > stringTable > enhancedPattern > current
        if (authors.manifest) {
            return { author: authors.manifest, source: 'llama-logic-manifest' };
        }
        
        if (authors.stringTable) {
            return { author: authors.stringTable, source: 'string-table' };
        }
        
        if (authors.enhancedPattern) {
            return { author: authors.enhancedPattern, source: 'enhanced-pattern' };
        }
        
        if (authors.current) {
            return { author: authors.current, source: 'current-method' };
        }
        
        return { source: 'none' };
    }
    
    /**
     * Generates improvement report
     */
    private generateImprovementReport(results: LlamaLogicTestResult[]): void {
        console.log('🔬 LLAMA-LOGIC IMPROVEMENTS REPORT');
        console.log('==================================================\n');
        
        const totalFiles = results.length;
        const currentDetections = results.filter(r => r.currentAuthor).length;
        const improvedDetections = results.filter(r => r.bestAuthor).length;
        const newDetections = results.filter(r => r.improvement).length;
        
        const currentRate = Math.round((currentDetections / totalFiles) * 100);
        const improvedRate = Math.round((improvedDetections / totalFiles) * 100);
        const improvementGain = improvedRate - currentRate;
        
        console.log('📊 IMPROVEMENT SUMMARY:');
        console.log(`   Total Package Files: ${totalFiles}`);
        console.log(`   Current Detection: ${currentDetections}/${totalFiles} (${currentRate}%)`);
        console.log(`   Improved Detection: ${improvedDetections}/${totalFiles} (${improvedRate}%)`);
        console.log(`   New Detections: ${newDetections}`);
        console.log(`   Improvement Gain: +${improvementGain}% points\n`);
        
        // Method breakdown
        const manifestDetections = results.filter(r => r.manifestAuthor).length;
        const stringTableDetections = results.filter(r => r.stringTableAuthor).length;
        const enhancedPatternDetections = results.filter(r => r.enhancedPatternAuthor).length;
        
        console.log('🔍 DETECTION METHOD BREAKDOWN:');
        console.log(`   Llama-Logic Manifests: ${manifestDetections}/${totalFiles} (${Math.round((manifestDetections/totalFiles)*100)}%)`);
        console.log(`   StringTable Analysis: ${stringTableDetections}/${totalFiles} (${Math.round((stringTableDetections/totalFiles)*100)}%)`);
        console.log(`   Enhanced Patterns: ${enhancedPatternDetections}/${totalFiles} (${Math.round((enhancedPatternDetections/totalFiles)*100)}%)\n`);
        
        // Detailed results
        console.log('📋 DETAILED RESULTS:');
        results.forEach(result => {
            const status = result.improvement ? '📈' : '➖';
            const current = result.currentAuthor || 'None';
            const improved = result.bestAuthor || 'None';
            
            console.log(`   ${status} ${result.filePath}:`);
            console.log(`      Current: ${current}`);
            console.log(`      Improved: ${improved} (${result.bestSource})`);
        });
        
        console.log('\n🎯 IMPLEMENTATION RECOMMENDATIONS:');
        
        if (improvementGain >= 20) {
            console.log('   🏆 EXCELLENT IMPROVEMENT! Implement immediately.');
        } else if (improvementGain >= 10) {
            console.log('   ✅ GOOD IMPROVEMENT! Worth implementing.');
        } else if (improvementGain >= 5) {
            console.log('   📈 MODERATE IMPROVEMENT! Consider implementing.');
        } else {
            console.log('   📋 LIMITED IMPROVEMENT! Focus on other methods.');
        }
        
        console.log(`   Priority 1: ${manifestDetections > 0 ? 'Llama-Logic Manifest Support' : 'Enhanced Pattern Recognition'}`);
        console.log(`   Priority 2: ${stringTableDetections > 0 ? 'StringTable Analysis' : 'Resource Metadata Mining'}`);
        console.log(`   Priority 3: Performance optimization and integration`);
    }
}

// Run the test
async function main() {
    const tester = new LlamaLogicImplementationTester();
    await tester.testLlamaLogicImprovements();
}

if (require.main === module) {
    main().catch(console.error);
}
