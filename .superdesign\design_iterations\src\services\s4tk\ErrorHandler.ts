/**
 * Error categories for S4TK operations
 */
export enum ErrorCategory {
    PACKAGE_LOADING = 'package_loading',
    RESOURCE_PARSING = 'resource_parsing',
    COMPRESSION = 'compression',
    VALIDATION = 'validation',
    PERFORMANCE = 'performance',
    HASHING = 'hashing',
    XML_PARSING = 'xml_parsing',
    IMAGE_PROCESSING = 'image_processing',
    UNKNOWN = 'unknown'
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
    CRITICAL = 'critical',
    ERROR = 'error',
    WARNING = 'warning',
    INFO = 'info'
}

/**
 * Detailed error information structure
 */
export interface ErrorInfo {
    category: ErrorCategory;
    severity: ErrorSeverity;
    message: string;
    context: string;
    filePath: string;
    timestamp: string;
    suggestion?: string;
    recoverable: boolean;
    errorCode?: string;
    originalError?: any;
}

/**
 * Service for handling S4TK-specific errors with categorization and suggestions
 */
export class ErrorHandler {
    
    /**
     * Handles S4TK-specific errors with detailed context and suggestions
     * @param error - The original error
     * @param context - Context where the error occurred
     * @param filePath - File path being processed
     * @returns Detailed error information
     */
    public handleS4TKError(error: any, context: string, filePath: string): ErrorInfo {
        const errorInfo: ErrorInfo = {
            category: this.categorizeError(error),
            severity: this.determineSeverity(error),
            message: this.extractMessage(error),
            context,
            filePath,
            timestamp: new Date().toISOString(),
            suggestion: this.getSuggestion(error),
            recoverable: this.isRecoverable(error),
            errorCode: this.generateErrorCode(error),
            originalError: error
        };

        // Log the error with appropriate level
        this.logError(errorInfo);
        
        return errorInfo;
    }
    
    /**
     * Categorizes errors based on their type and message
     * @param error - Error to categorize
     * @returns Error category
     */
    public categorizeError(error: any): ErrorCategory {
        const message = this.extractMessage(error).toLowerCase();
        const errorType = error?.constructor?.name?.toLowerCase() || '';
        
        // Package loading errors
        if (message.includes('package') || message.includes('dbpf') || 
            message.includes('buffer') || message.includes('invalid format')) {
            return ErrorCategory.PACKAGE_LOADING;
        }
        
        // Compression errors
        if (message.includes('compression') || message.includes('decompress') ||
            message.includes('zlib') || message.includes('inflate')) {
            return ErrorCategory.COMPRESSION;
        }
        
        // Resource parsing errors
        if (message.includes('resource') || message.includes('simdata') ||
            message.includes('string table') || message.includes('stbl')) {
            return ErrorCategory.RESOURCE_PARSING;
        }
        
        // XML parsing errors
        if (message.includes('xml') || message.includes('tuning') ||
            message.includes('parse') || errorType.includes('xml')) {
            return ErrorCategory.XML_PARSING;
        }
        
        // Image processing errors
        if (message.includes('image') || message.includes('dds') ||
            message.includes('texture') || message.includes('bitmap')) {
            return ErrorCategory.IMAGE_PROCESSING;
        }
        
        // Hashing errors
        if (message.includes('hash') || message.includes('fnv') ||
            message.includes('format') || errorType.includes('hash')) {
            return ErrorCategory.HASHING;
        }
        
        // Validation errors
        if (message.includes('validation') || message.includes('invalid') ||
            message.includes('corrupt') || message.includes('malformed')) {
            return ErrorCategory.VALIDATION;
        }
        
        // Performance errors
        if (message.includes('timeout') || message.includes('memory') ||
            message.includes('limit') || message.includes('performance')) {
            return ErrorCategory.PERFORMANCE;
        }
        
        return ErrorCategory.UNKNOWN;
    }
    
    /**
     * Provides specific suggestions based on error type and context
     * @param error - Error to analyze
     * @returns Suggestion string or undefined
     */
    public getSuggestion(error: any): string | undefined {
        const category = this.categorizeError(error);
        const message = this.extractMessage(error).toLowerCase();
        
        switch (category) {
            case ErrorCategory.PACKAGE_LOADING:
                if (message.includes('buffer')) {
                    return 'File may be truncated or corrupted. Try re-downloading the mod file.';
                }
                if (message.includes('format')) {
                    return 'File format may be invalid or unsupported. Ensure this is a valid .package file.';
                }
                return 'Package file may be corrupted or in an unsupported format.';
                
            case ErrorCategory.COMPRESSION:
                return 'File may use unsupported compression or be corrupted. Try extracting with Sims 4 Studio first.';
                
            case ErrorCategory.RESOURCE_PARSING:
                return 'Resource data may be corrupted or in an unexpected format. File may need repair.';
                
            case ErrorCategory.XML_PARSING:
                return 'Tuning XML may be malformed. Check for syntax errors or missing elements.';
                
            case ErrorCategory.IMAGE_PROCESSING:
                return 'Image data may be corrupted or in an unsupported format. Try converting with S4S.';
                
            case ErrorCategory.HASHING:
                return 'Resource key formatting failed. File may have invalid resource identifiers.';
                
            case ErrorCategory.VALIDATION:
                return 'File failed validation checks. May be corrupted or modified incorrectly.';
                
            case ErrorCategory.PERFORMANCE:
                return 'Operation timed out or exceeded memory limits. Try processing smaller batches.';
                
            default:
                return 'An unexpected error occurred. Check file integrity and try again.';
        }
    }
    
    /**
     * Determines if an error is recoverable
     * @param error - Error to analyze
     * @returns True if recoverable
     */
    public isRecoverable(error: any): boolean {
        const category = this.categorizeError(error);
        
        // These categories are typically recoverable with fallback methods
        return [
            ErrorCategory.RESOURCE_PARSING,
            ErrorCategory.HASHING,
            ErrorCategory.XML_PARSING,
            ErrorCategory.IMAGE_PROCESSING
        ].includes(category);
    }
    
    /**
     * Determines error severity based on category and context
     * @param error - Error to analyze
     * @returns Error severity
     */
    private determineSeverity(error: any): ErrorSeverity {
        const category = this.categorizeError(error);
        
        switch (category) {
            case ErrorCategory.PACKAGE_LOADING:
            case ErrorCategory.COMPRESSION:
                return ErrorSeverity.CRITICAL;
                
            case ErrorCategory.RESOURCE_PARSING:
            case ErrorCategory.XML_PARSING:
            case ErrorCategory.IMAGE_PROCESSING:
                return ErrorSeverity.ERROR;
                
            case ErrorCategory.VALIDATION:
            case ErrorCategory.HASHING:
                return ErrorSeverity.WARNING;
                
            case ErrorCategory.PERFORMANCE:
                return ErrorSeverity.INFO;
                
            default:
                return ErrorSeverity.ERROR;
        }
    }
    
    /**
     * Extracts a meaningful message from various error types
     * @param error - Error to extract message from
     * @returns Error message
     */
    private extractMessage(error: any): string {
        if (typeof error === 'string') {
            return error;
        }
        
        if (error?.message) {
            return error.message;
        }
        
        if (error?.toString) {
            return error.toString();
        }
        
        return 'Unknown error occurred';
    }
    
    /**
     * Generates a unique error code for tracking
     * @param error - Error to generate code for
     * @returns Error code
     */
    private generateErrorCode(error: any): string {
        const category = this.categorizeError(error);
        const timestamp = Date.now().toString(36);
        const hash = this.simpleHash(this.extractMessage(error));
        
        return `${category.toUpperCase()}_${timestamp}_${hash}`;
    }
    
    /**
     * Logs error with appropriate level based on severity
     * @param errorInfo - Error information to log
     */
    private logError(errorInfo: ErrorInfo): void {
        const logMessage = `[${errorInfo.errorCode}] ${errorInfo.context}: ${errorInfo.message}`;
        
        switch (errorInfo.severity) {
            case ErrorSeverity.CRITICAL:
                console.error(logMessage, errorInfo);
                break;
            case ErrorSeverity.ERROR:
                console.error(logMessage);
                break;
            case ErrorSeverity.WARNING:
                console.warn(logMessage);
                break;
            case ErrorSeverity.INFO:
                console.info(logMessage);
                break;
        }
    }
    
    /**
     * Simple hash function for error code generation
     * @param str - String to hash
     * @returns Hash value
     */
    private simpleHash(str: string): string {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(36);
    }
}

// Export singleton instance
export const errorHandler = new ErrorHandler();