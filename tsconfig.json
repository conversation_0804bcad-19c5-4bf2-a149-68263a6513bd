{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "sourceMap": true, "lib": ["ES2022", "DOM"], "types": ["electron-vite/node"]}, "include": ["src/main/index.ts", "src/preload/index.ts", "src/services/**/*.ts", "src/types/**/*.ts", "src/constants/**/*.ts", "electron.vite.config.ts"], "exclude": ["node_modules", "dist", "src/renderer"]}