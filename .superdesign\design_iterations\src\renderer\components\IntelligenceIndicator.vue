<template>
  <div class="intelligence-indicator" :class="indicatorClass">
    <div class="intelligence-indicator__icon">
      <CpuChipIcon v-if="type === 'Script Intelligence'" />
      <CubeIcon v-else-if="type === 'Resource Intelligence'" />
      <DocumentTextIcon v-else />
    </div>
    
    <div class="intelligence-indicator__content">
      <div class="intelligence-indicator__type">{{ type }}</div>
      <div class="intelligence-indicator__details">
        <span v-if="qualityScore" class="quality-score">{{ qualityScore }}/100</span>
        <span v-if="riskLevel" class="risk-level" :class="`risk-${riskLevel}`">
          {{ formatRiskLevel(riskLevel) }}
        </span>
      </div>
    </div>
    
    <div v-if="qualityScore" class="intelligence-indicator__progress">
      <div 
        class="progress-bar" 
        :style="{ width: `${qualityScore}%` }"
        :class="getQualityClass(qualityScore)"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { CpuChipIcon, CubeIcon, DocumentTextIcon } from '@heroicons/vue/24/outline';

const props = defineProps<{
  type: string;
  qualityScore?: number;
  riskLevel?: string;
  size?: 'sm' | 'md' | 'lg';
}>();

const indicatorClass = computed(() => {
  const classes = ['intelligence-indicator'];
  
  if (props.size) {
    classes.push(`intelligence-indicator--${props.size}`);
  }
  
  if (props.type === 'Script Intelligence') {
    classes.push('intelligence-indicator--script');
  } else if (props.type === 'Resource Intelligence') {
    classes.push('intelligence-indicator--resource');
  } else {
    classes.push('intelligence-indicator--basic');
  }
  
  return classes;
});

const formatRiskLevel = (level: string): string => {
  const levels: Record<string, string> = {
    'low': 'Low Risk',
    'medium': 'Med Risk',
    'high': 'High Risk'
  };
  return levels[level] || level;
};

const getQualityClass = (score: number): string => {
  if (score >= 80) return 'progress-bar--excellent';
  if (score >= 60) return 'progress-bar--good';
  if (score >= 40) return 'progress-bar--fair';
  return 'progress-bar--poor';
};
</script>

<style scoped>
.intelligence-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  background: var(--bg-primary);
  transition: all var(--duration-150) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.intelligence-indicator:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.intelligence-indicator--sm {
  padding: var(--space-1) var(--space-2);
  gap: var(--space-1);
}

.intelligence-indicator--lg {
  padding: var(--space-3) var(--space-4);
  gap: var(--space-3);
}

.intelligence-indicator--script {
  border-color: var(--sims-purple-light);
  background: var(--sims-purple-bg);
}

.intelligence-indicator--resource {
  border-color: var(--sims-blue-light);
  background: var(--sims-blue-bg);
}

.intelligence-indicator--basic {
  border-color: var(--border-medium);
  background: var(--bg-secondary);
}

.intelligence-indicator__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.intelligence-indicator--script .intelligence-indicator__icon {
  color: var(--sims-purple);
}

.intelligence-indicator--resource .intelligence-indicator__icon {
  color: var(--sims-blue);
}

.intelligence-indicator--basic .intelligence-indicator__icon {
  color: var(--text-tertiary);
}

.intelligence-indicator__icon svg {
  width: 16px;
  height: 16px;
}

.intelligence-indicator--sm .intelligence-indicator__icon svg {
  width: 14px;
  height: 14px;
}

.intelligence-indicator--lg .intelligence-indicator__icon svg {
  width: 20px;
  height: 20px;
}

.intelligence-indicator__content {
  flex: 1;
  min-width: 0;
}

.intelligence-indicator__type {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.intelligence-indicator--sm .intelligence-indicator__type {
  font-size: 10px;
}

.intelligence-indicator--lg .intelligence-indicator__type {
  font-size: var(--text-sm);
}

.intelligence-indicator__details {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-1);
}

.intelligence-indicator--sm .intelligence-indicator__details {
  margin-top: 0;
  gap: var(--space-1);
}

.quality-score {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}

.intelligence-indicator--sm .quality-score {
  font-size: 10px;
}

.risk-level {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  padding: 1px var(--space-1);
  border-radius: var(--radius-sm);
}

.intelligence-indicator--sm .risk-level {
  font-size: 9px;
  padding: 0 var(--space-1);
}

.risk-level.risk-low {
  background: var(--success-bg);
  color: var(--success);
}

.risk-level.risk-medium {
  background: var(--warning-bg);
  color: var(--warning);
}

.risk-level.risk-high {
  background: var(--error-bg);
  color: var(--error);
}

.intelligence-indicator__progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--border-light);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width var(--duration-300) var(--ease-out);
}

.progress-bar--excellent {
  background: var(--success);
}

.progress-bar--good {
  background: var(--info);
}

.progress-bar--fair {
  background: var(--warning);
}

.progress-bar--poor {
  background: var(--error);
}
</style>
