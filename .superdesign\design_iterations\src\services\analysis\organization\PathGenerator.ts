import type { AnalyzedPackage } from '../../../types/analysis';
import { ModCategory, FileType } from '../../../types/analysis';
import type { OrganizationPreferences } from './types';
import { AuthorExtractor } from './AuthorExtractor';

/**
 * Specialized component for generating organization paths
 * Handles the logic of determining where files should be placed
 */
export class PathGenerator {
    
    /**
     * Generates a suggested folder path for a mod based on its analysis
     */
    public static generateSuggestedPath(analysis: AnalyzedPackage, preferences: OrganizationPreferences): string {
        const pathParts: string[] = [];

        // Start with base category folder
        const baseFolderName = preferences.customFolderNames[analysis.category] || 
                              this.getCategoryDisplayName(analysis.category);
        pathParts.push(baseFolderName);

        // Add subcategory if enabled and available
        if (preferences.useSubcategories && analysis.subcategory) {
            pathParts.push(this.formatSubcategoryName(analysis.subcategory));
        }

        // Add author grouping if enabled
        if (preferences.groupByAuthor) {
            const author = AuthorExtractor.extractAuthor(analysis);
            if (author) {
                pathParts.push(author);
            }
        }

        // Special handling for different file types
        if (analysis.fileType === FileType.SCRIPT && preferences.separateScriptMods) {
            // Script mods might need special organization
            const scriptCategory = this.getScriptCategory(analysis);
            if (scriptCategory && scriptCategory !== analysis.subcategory) {
                pathParts.push(scriptCategory);
            }
        }

        // Handle overrides separately if enabled
        if (analysis.isOverride && preferences.separateOverrides) {
            if (!pathParts.includes('Overrides')) {
                pathParts.splice(1, 0, 'Overrides'); // Insert after main category
            }
        }

        return pathParts.join('/');
    }

    /**
     * Gets display name for a mod category
     */
    public static getCategoryDisplayName(category: ModCategory): string {
        const displayNames: Record<ModCategory, string> = {
            [ModCategory.CAS_CC]: 'CAS Custom Content',
            [ModCategory.BUILD_BUY_CC]: 'Build/Buy Custom Content',
            [ModCategory.SCRIPT_MOD]: 'Script Mods',
            [ModCategory.TUNING_MOD]: 'Tuning Mods',
            [ModCategory.OVERRIDE]: 'Overrides',
            [ModCategory.FRAMEWORK]: 'Framework & Core Mods',
            [ModCategory.LIBRARY]: 'Libraries & Dependencies',
            [ModCategory.UNKNOWN]: 'Uncategorized'
        };

        return displayNames[category] || 'Unknown Category';
    }

    /**
     * Formats subcategory names for folder structure
     */
    private static formatSubcategoryName(subcategory: string): string {
        return subcategory
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    /**
     * Gets specific script category for better organization
     */
    private static getScriptCategory(analysis: AnalyzedPackage): string | null {
        const fileName = analysis.filePath.toLowerCase();
        
        // Framework mods
        if (fileName.includes('mc_') || fileName.includes('mccommand')) {
            return 'MC Command Center';
        }
        
        if (fileName.includes('xmlinjector')) {
            return 'XML Injector';
        }
        
        if (fileName.includes('basemental')) {
            return 'Basemental';
        }
        
        if (fileName.includes('wickedwhims')) {
            return 'WickedWhims';
        }

        // Gameplay categories
        if (fileName.includes('ui_cheat') || fileName.includes('uicheat')) {
            return 'UI Cheats';
        }

        return null;
    }

    /**
     * Generates description for organization path
     */
    public static generatePathDescription(path: string, files: AnalyzedPackage[]): string {
        const fileTypes = new Set(files.map(f => f.fileType));
        const categories = new Set(files.map(f => f.category));
        
        let description = `${files.length} file(s)`;
        
        if (fileTypes.size === 1) {
            const fileType = Array.from(fileTypes)[0];
            description += ` (${fileType === FileType.PACKAGE ? '.package' : '.ts4script'})`;
        }
        
        if (categories.size === 1) {
            const category = Array.from(categories)[0];
            description += ` - ${this.getCategoryDisplayName(category)}`;
        }

        return description;
    }
}