/**
 * Test Universal Classifier - Phase 3
 * 
 * Tests all object categories (furniture, decorations, appliances, build items)
 * to verify comprehensive subcategory detection across all content types.
 */

import { UniversalSubcategoryClassifier } from '../services/analysis/specialized/cas/UniversalSubcategoryClassifier';
import { SubcategoryPatternConfig } from '../services/analysis/specialized/cas/SubcategoryPatternConfig';

/**
 * Test comprehensive object category detection
 */
function testAllObjectCategories() {
    console.log('🏠 Testing All Object Categories...\n');
    
    const testCases = [
        // Furniture tests
        { filename: 'modern_leather_sofa_furniture.package', category: 'furniture', expectedSubcategories: ['seating', 'modern'] },
        { filename: 'wooden_dining_table_rustic.package', category: 'furniture', expectedSubcategories: ['table', 'rustic'] },
        { filename: 'king_size_bed_bedroom.package', category: 'furniture', expectedSubcategories: ['bed'] },
        { filename: 'vintage_bookshelf_storage.package', category: 'furniture', expectedSubcategories: ['storage', 'vintage'] },
        { filename: 'gaming_chair_entertainment.package', category: 'furniture', expectedSubcategories: ['entertainment'] },
        
        // Decoration tests
        { filename: 'abstract_painting_wall_art.package', category: 'decorations', expectedSubcategories: ['art'] },
        { filename: 'potted_plant_flowers.package', category: 'decorations', expectedSubcategories: ['plants'] },
        { filename: 'crystal_chandelier_lighting.package', category: 'decorations', expectedSubcategories: ['lighting'] },
        { filename: 'ceramic_figurine_ornament.package', category: 'decorations', expectedSubcategories: ['ornaments'] },
        
        // Appliance tests
        { filename: 'stainless_steel_refrigerator.package', category: 'appliances', expectedSubcategories: ['kitchen'] },
        { filename: 'luxury_bathtub_bathroom.package', category: 'appliances', expectedSubcategories: ['bathroom'] },
        { filename: 'smart_tv_electronics.package', category: 'appliances', expectedSubcategories: ['electronics'] },
        { filename: 'front_load_washer_laundry.package', category: 'appliances', expectedSubcategories: ['laundry'] },
        
        // Build item tests
        { filename: 'wooden_front_door_entrance.package', category: 'build_items', expectedSubcategories: ['doors'] },
        { filename: 'large_bay_window_glass.package', category: 'build_items', expectedSubcategories: ['windows'] },
        { filename: 'spiral_staircase_steps.package', category: 'build_items', expectedSubcategories: ['stairs'] },
        { filename: 'brick_wall_panel.package', category: 'build_items', expectedSubcategories: ['walls'] },
        { filename: 'hardwood_floor_tile.package', category: 'build_items', expectedSubcategories: ['flooring'] }
    ];
    
    let successCount = 0;
    let totalTests = testCases.length;
    
    for (const testCase of testCases) {
        console.log(`📁 Testing: ${testCase.filename} (${testCase.category})`);
        
        const result = UniversalSubcategoryClassifier.classifySubcategories(
            testCase.filename, 
            testCase.category
        );
        
        console.log(`   Detected subcategories: ${result.subcategories.join(', ')}`);
        console.log(`   Expected subcategories: ${testCase.expectedSubcategories.join(', ')}`);
        console.log(`   Confidence: ${result.confidence.toFixed(2)}`);
        console.log(`   Tags: ${result.tags.join(', ')}`);
        
        // Check if at least one expected subcategory was detected
        const hasExpectedSubcategory = testCase.expectedSubcategories.some(expected => 
            result.subcategories.includes(expected)
        );
        
        if (hasExpectedSubcategory && result.confidence > 0.5) {
            console.log(`   ✅ Success`);
            successCount++;
        } else {
            console.log(`   ❌ Failed`);
        }
        
        console.log('');
    }
    
    console.log(`📊 Results: ${successCount}/${totalTests} tests passed (${(successCount/totalTests*100).toFixed(1)}%)\n`);
    return successCount / totalTests;
}

/**
 * Test comprehensive pattern configuration
 */
function testComprehensivePatternConfiguration() {
    console.log('⚙️ Testing Comprehensive Pattern Configuration...\n');
    
    const allPatterns = SubcategoryPatternConfig.getAllPatterns();
    const casPatterns = SubcategoryPatternConfig.getAllCASPatterns();
    const objectPatterns = SubcategoryPatternConfig.getAllObjectPatterns();
    const categories = SubcategoryPatternConfig.getAllCategories();
    
    console.log(`📊 Total patterns loaded: ${allPatterns.length}`);
    console.log(`📊 CAS patterns: ${casPatterns.length}`);
    console.log(`📊 Object patterns: ${objectPatterns.length}`);
    console.log(`📊 Categories available: ${categories.join(', ')}`);
    
    for (const category of categories) {
        const categoryPatterns = SubcategoryPatternConfig.getPatternsByCategory(category);
        const subcategories = SubcategoryPatternConfig.getSubcategoriesForCategory(category);
        const isCAS = SubcategoryPatternConfig.isCASCategory(category);
        const isObject = SubcategoryPatternConfig.isObjectCategory(category);
        
        console.log(`   ${category}: ${categoryPatterns.length} patterns, ${subcategories.length} subcategories (${isCAS ? 'CAS' : isObject ? 'Object' : 'Unknown'})`);
        console.log(`      Subcategories: ${subcategories.join(', ')}`);
    }
    
    console.log('');
}

/**
 * Test cross-category performance
 */
function testCrossCategoryPerformance() {
    console.log('⚡ Testing Cross-Category Performance...\n');
    
    const testFiles = [
        'hair_long_wavy_ponytail.package',
        'clothing_formal_business_suit.package',
        'makeup_red_lipstick.package',
        'accessories_gold_necklace.package',
        'furniture_modern_sofa.package',
        'decorations_crystal_chandelier.package',
        'appliances_smart_refrigerator.package',
        'build_wooden_door.package'
    ];
    
    const categories = SubcategoryPatternConfig.getAllCategories();
    
    const startTime = Date.now();
    let totalClassifications = 0;
    
    for (const filename of testFiles) {
        for (const category of categories) {
            const result = UniversalSubcategoryClassifier.classifySubcategories(filename, category);
            totalClassifications++;
        }
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / totalClassifications;
    
    console.log(`📊 Performance Results:`);
    console.log(`   Total classifications: ${totalClassifications}`);
    console.log(`   Total time: ${totalTime}ms`);
    console.log(`   Average time per classification: ${avgTime.toFixed(2)}ms`);
    console.log(`   Target: <50ms per mod (✅ ${avgTime < 50 ? 'PASSED' : 'FAILED'})`);
    console.log('');
    
    return avgTime;
}

/**
 * Test universal description generation
 */
function testUniversalDescriptions() {
    console.log('📝 Testing Universal Description Generation...\n');
    
    const testCases = [
        { filename: 'modern_leather_sofa_furniture.package', category: 'furniture' },
        { filename: 'abstract_painting_wall_art.package', category: 'decorations' },
        { filename: 'smart_refrigerator_kitchen.package', category: 'appliances' },
        { filename: 'wooden_front_door.package', category: 'build_items' }
    ];
    
    for (const testCase of testCases) {
        console.log(`📁 Testing: ${testCase.filename} (${testCase.category})`);
        
        const result = UniversalSubcategoryClassifier.classifySubcategories(
            testCase.filename, 
            testCase.category
        );
        
        const description = UniversalSubcategoryClassifier.getDescription(result);
        
        console.log(`   Subcategories: ${result.subcategories.join(', ')}`);
        console.log(`   Generated description: "${description}"`);
        console.log(`   Confidence: ${result.confidence.toFixed(2)}`);
        console.log('');
    }
}

/**
 * Main test execution
 */
function main() {
    console.log('🚀 Universal Subcategory Classifier - Phase 3 Testing\n');
    console.log('Testing comprehensive object category detection...\n');
    
    try {
        testComprehensivePatternConfiguration();
        const successRate = testAllObjectCategories();
        const avgTime = testCrossCategoryPerformance();
        testUniversalDescriptions();
        
        console.log('📋 Phase 3 Summary:');
        console.log(`   Success Rate: ${(successRate * 100).toFixed(1)}%`);
        console.log(`   Performance: ${avgTime.toFixed(2)}ms per classification`);
        console.log(`   Target Success: >70% (${successRate > 0.7 ? '✅ PASSED' : '❌ FAILED'})`);
        console.log(`   Target Performance: <50ms (${avgTime < 50 ? '✅ PASSED' : '❌ FAILED'})`);
        
        if (successRate > 0.7 && avgTime < 50) {
            console.log('\n✅ Phase 3 completed successfully!');
            console.log('🎯 All object categories enhanced with unified subcategory detection');
            console.log('🏆 Universal system now covers CAS + Objects comprehensively');
        } else {
            console.log('\n⚠️ Phase 3 needs optimization');
        }
        
    } catch (error) {
        console.error('❌ Testing failed:', error);
        process.exit(1);
    }
}

// Run tests
main();
