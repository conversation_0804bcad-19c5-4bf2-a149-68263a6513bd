import type { ResourceEntry } from '@s4tk/models/types';
import { ResourceTypeHelpers } from '../../../constants/unifiedResourceTypes';
import type { OverrideAnalysisResult } from './types';

/**
 * Specialized detector for override resources and modifications
 * Identifies what base game content is being overridden
 */
export class OverrideDetector {
    
    /**
     * Analyzes resources to detect override patterns
     */
    public static analyze(resources: ResourceEntry[], filePath: string): OverrideAnalysisResult {
        const overriddenResources = this.detectOverriddenResources(resources);
        const isOverride = overriddenResources.length > 0;
        const overrideType = this.determineOverrideType(resources, filePath);
        const confidence = this.calculateOverrideConfidence(resources, filePath, overriddenResources);
        
        return {
            isOverride,
            overriddenResources,
            overrideType,
            confidence
        };
    }
    
    /**
     * Detects which resources are being overridden
     */
    private static detectOverriddenResources(resources: ResourceEntry[]): Array<{
        type: number;
        group: number;
        instance: string;
        description: string;
    }> {
        const overriddenResources: Array<{
            type: number;
            group: number;
            instance: string;
            description: string;
        }> = [];
        
        resources.forEach(resource => {
            if (this.isOverrideResource(resource)) {
                const description = this.getOverrideDescription(resource);
                
                overriddenResources.push({
                    type: resource.key.type,
                    group: resource.key.group,
                    instance: resource.key.instance.toString(16),
                    description
                });
            }
        });
        
        return overriddenResources;
    }
    
    /**
     * Checks if a resource is an override resource
     */
    private static isOverrideResource(resource: ResourceEntry): boolean {
        // Check if resource type is override-prone
        if (!ResourceTypeHelpers.isOverrideProne(resource.key.type)) {
            return false;
        }
        
        // Check if group indicates base game content
        const isBaseGameGroup = resource.key.group === 0x80000000 || resource.key.group === 0x00000000;
        
        // Check if instance ID matches known base game patterns
        const hasBaseGameInstance = this.hasBaseGameInstancePattern(resource.key.instance);
        
        return isBaseGameGroup || hasBaseGameInstance;
    }
    
    /**
     * Checks if instance ID follows base game patterns
     */
    private static hasBaseGameInstancePattern(instance: bigint): boolean {
        // Convert to hex string for pattern matching
        const instanceHex = instance.toString(16).toUpperCase();
        
        // Base game instances often start with specific patterns
        const baseGamePatterns = [
            '00000000', // Common base game pattern
            '80000000', // Another base game pattern
            'FFFFFFFF'  // Special base game pattern
        ];
        
        return baseGamePatterns.some(pattern => instanceHex.startsWith(pattern));
    }
    
    /**
     * Gets description of what is being overridden
     */
    private static getOverrideDescription(resource: ResourceEntry): string {
        const resourceTypeName = ResourceTypeHelpers.getTypeName(resource.key.type);
        const groupHex = resource.key.group.toString(16).toUpperCase().padStart(8, '0');
        const instanceHex = resource.key.instance.toString(16).toUpperCase().padStart(16, '0');
        
        // Try to provide more specific descriptions based on resource type
        switch (resource.key.type) {
            case 0x545AC67A: // Tuning
                return `Overrides base game tuning (${resourceTypeName})`;
            case 0x220557DA: // String Table
                return `Overrides base game strings (${resourceTypeName})`;
            case 0x00B2D882: // DDS Image
                return `Overrides base game texture (${resourceTypeName})`;
            case 0x2E75C764: // DST Image
                return `Overrides base game texture (${resourceTypeName})`;
            default:
                return `Overrides base game ${resourceTypeName} (${groupHex}:${instanceHex})`;
        }
    }
    
    /**
     * Determines the type of override
     */
    private static determineOverrideType(resources: ResourceEntry[], filePath: string): 'replacement' | 'modification' | 'addition' | 'unknown' {
        const fileName = filePath.toLowerCase();
        
        // Check filename patterns for override type hints
        if (fileName.includes('replacement') || fileName.includes('replace')) {
            return 'replacement';
        }
        
        if (fileName.includes('modification') || fileName.includes('modify') || fileName.includes('mod')) {
            return 'modification';
        }
        
        if (fileName.includes('addition') || fileName.includes('add') || fileName.includes('new')) {
            return 'addition';
        }
        
        // Analyze resource patterns to determine type
        const overrideResources = resources.filter(r => this.isOverrideResource(r));
        const totalResources = resources.length;
        
        // If most resources are overrides, likely a replacement
        if (overrideResources.length / totalResources > 0.8) {
            return 'replacement';
        }
        
        // If some resources are overrides mixed with new content, likely modification
        if (overrideResources.length / totalResources > 0.3) {
            return 'modification';
        }
        
        // If few overrides with mostly new content, likely addition
        if (overrideResources.length / totalResources < 0.3 && overrideResources.length > 0) {
            return 'addition';
        }
        
        return 'unknown';
    }
    
    /**
     * Calculates confidence score for override detection
     */
    private static calculateOverrideConfidence(
        resources: ResourceEntry[], 
        filePath: string, 
        overriddenResources: Array<any>
    ): number {
        let confidence = 0;
        
        // Base confidence from detected override resources
        if (overriddenResources.length > 0) {
            confidence += 0.4;
        }
        
        // Filename pattern confidence
        const fileName = filePath.toLowerCase();
        const overridePatterns = ['override', 'replacement', 'replace', 'vanilla', 'basegame', 'fix'];
        const matchedPatterns = overridePatterns.filter(pattern => fileName.includes(pattern));
        if (matchedPatterns.length > 0) {
            confidence += 0.3;
        }
        
        // Resource type confidence
        const overrideProneResources = resources.filter(r => ResourceTypeHelpers.isOverrideProne(r.key.type));
        if (overrideProneResources.length > 0) {
            confidence += 0.2;
        }
        
        // Group pattern confidence
        const baseGameGroups = resources.filter(r => 
            r.key.group === 0x80000000 || r.key.group === 0x00000000
        );
        if (baseGameGroups.length > 0) {
            confidence += 0.1;
        }
        
        return Math.min(confidence, 1.0);
    }
    
    /**
     * Gets detailed override information for reporting
     */
    public static getOverrideDetails(resources: ResourceEntry[]): {
        totalOverrides: number;
        overridesByType: Record<string, number>;
        criticalOverrides: Array<{
            type: string;
            description: string;
            impact: string;
        }>;
    } {
        const overrideResources = resources.filter(r => this.isOverrideResource(r));
        
        // Count overrides by type
        const overridesByType: Record<string, number> = {};
        overrideResources.forEach(resource => {
            const typeName = ResourceTypeHelpers.getTypeName(resource.key.type);
            overridesByType[typeName] = (overridesByType[typeName] || 0) + 1;
        });
        
        // Identify critical overrides (those that commonly cause conflicts)
        const criticalOverrides = overrideResources
            .filter(r => this.isCriticalOverride(r))
            .map(r => ({
                type: ResourceTypeHelpers.getTypeName(r.key.type),
                description: this.getOverrideDescription(r),
                impact: this.getOverrideImpact(r)
            }));
        
        return {
            totalOverrides: overrideResources.length,
            overridesByType,
            criticalOverrides
        };
    }
    
    /**
     * Checks if an override is critical (high conflict potential)
     */
    private static isCriticalOverride(resource: ResourceEntry): boolean {
        // Tuning overrides are often critical
        if (ResourceTypeHelpers.isTuningResource(resource.key.type)) {
            return true;
        }
        
        // String table overrides can be critical
        if (ResourceTypeHelpers.isStringTableResource(resource.key.type)) {
            return true;
        }
        
        // Core game system overrides
        if (resource.key.group === 0x80000000) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Gets the impact description of an override
     */
    private static getOverrideImpact(resource: ResourceEntry): string {
        if (ResourceTypeHelpers.isTuningResource(resource.key.type)) {
            return 'May affect gameplay mechanics and conflict with other tuning mods';
        }
        
        if (ResourceTypeHelpers.isStringTableResource(resource.key.type)) {
            return 'May change game text and conflict with translation mods';
        }
        
        if (ResourceTypeHelpers.isTextureResource(resource.key.type)) {
            return 'May change visual appearance and conflict with other texture mods';
        }
        
        return 'May conflict with other mods modifying the same content';
    }
}