#!/usr/bin/env node

/**
 * Full Detailed Report Generator
 * 
 * Generates comprehensive detailed results for every single mod file
 * in the user's collection with complete analysis data.
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface DetailedModResult {
    // File Information
    fileName: string;
    filePath: string;
    fileExtension: string;
    fileSize: number;
    fileSizeFormatted: string;
    
    // Analysis Results
    fileType: string;
    category?: string;
    subcategory?: string;
    
    // Phase 3A: Metadata
    author?: string;
    version?: string;
    modName?: string;
    metadataSource?: string;
    metadataConfidence?: number;
    
    // Phase 4A: Intelligence
    hasIntelligence: boolean;
    qualityScore?: number;
    riskLevel?: string;
    dependencyCount?: number;
    hasResourceIntelligence: boolean;
    
    // Resource Information
    resourceCount?: number;
    resourceTypes?: string[];
    
    // Performance
    processingTime: number;
    analysisDate: string;
    
    // Error Information
    error?: string;
    errorDetails?: string;
}

class FullDetailedReportGenerator {
    private analysisService: PackageAnalysisService;
    private reportPath: string;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
        this.reportPath = path.join(process.cwd(), 'reports', `full-mod-analysis-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.md`);
    }
    
    /**
     * Generates full detailed report for all mods
     */
    public async generateFullDetailedReport(): Promise<void> {
        console.log('📋 FULL DETAILED REPORT GENERATOR');
        console.log('Analyzing ALL mods with complete detailed results\n');
        
        const modsDir = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
        
        if (!fs.existsSync(modsDir)) {
            console.error('❌ Mods directory not found:', modsDir);
            return;
        }
        
        // Ensure reports directory exists
        const reportsDir = path.dirname(this.reportPath);
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }
        
        console.log(`📁 Scanning mods directory: ${modsDir}`);
        console.log(`📄 Report will be saved to: ${this.reportPath}\n`);
        
        // Find all mod files
        const modFiles = this.findModFiles(modsDir);
        console.log(`📦 Found ${modFiles.length} mod files to analyze\n`);
        
        const results: DetailedModResult[] = [];
        let processedCount = 0;
        const startTime = Date.now();
        
        // Initialize report file
        this.initializeReportFile(modFiles.length);
        
        // Progress tracking
        const progressInterval = Math.max(1, Math.floor(modFiles.length / 20));
        
        for (const filePath of modFiles) {
            processedCount++;
            const fileName = path.basename(filePath);
            
            // Show progress
            if (processedCount % progressInterval === 0 || processedCount === modFiles.length) {
                const progress = Math.round((processedCount / modFiles.length) * 100);
                const elapsed = Math.round((Date.now() - startTime) / 1000);
                console.log(`📊 Progress: ${progress}% (${processedCount}/${modFiles.length}) | ⏱️ ${elapsed}s elapsed`);
            }
            
            const result = await this.analyzeFile(filePath);
            results.push(result);
            
            // Write individual result to file immediately
            this.writeIndividualResult(result, processedCount);
            
            // Show errors immediately
            if (result.error) {
                console.log(`   ❌ [${processedCount}] ${fileName}: ${result.error}`);
            }
        }
        
        const totalElapsed = Math.round((Date.now() - startTime) / 1000);
        console.log(`\n🏁 ANALYSIS COMPLETE: ${modFiles.length} files in ${totalElapsed}s`);
        
        // Write summary to file
        this.writeSummaryToFile(results, totalElapsed);
        
        console.log(`\n📄 FULL DETAILED REPORT SAVED TO:`);
        console.log(`   ${this.reportPath}`);
        console.log(`\n📊 Report contains detailed analysis of all ${modFiles.length} mod files`);
    }
    
    /**
     * Recursively finds all mod files
     */
    private findModFiles(dir: string): string[] {
        const modFiles: string[] = [];
        
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    modFiles.push(...this.findModFiles(fullPath));
                } else if (stat.isFile()) {
                    if (item.endsWith('.package') || item.endsWith('.ts4script')) {
                        modFiles.push(fullPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not read directory ${dir}:`, error);
        }
        
        return modFiles;
    }
    
    /**
     * Analyzes a single file with full detail capture
     */
    private async analyzeFile(filePath: string): Promise<DetailedModResult> {
        const fileName = path.basename(filePath);
        const fileExtension = path.extname(fileName);
        const startTime = Date.now();
        
        try {
            const stats = fs.statSync(filePath);
            const fileSize = stats.size;
            const fileSizeFormatted = this.formatFileSize(fileSize);
            
            // Skip extremely large files
            const maxFileSize = 500 * 1024 * 1024; // 500MB
            if (fileSize > maxFileSize) {
                return {
                    fileName,
                    filePath,
                    fileExtension,
                    fileSize,
                    fileSizeFormatted,
                    fileType: 'skipped',
                    hasIntelligence: false,
                    hasResourceIntelligence: false,
                    processingTime: Date.now() - startTime,
                    analysisDate: new Date().toISOString(),
                    error: `File too large (${fileSizeFormatted} > 500MB limit)`
                };
            }
            
            const buffer = fs.readFileSync(filePath);
            
            // Analysis with timeout
            const timeoutMs = 30000;
            const analysisPromise = this.analysisService.detailedAnalyzeAsync(buffer, filePath);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error(`Analysis timeout after ${timeoutMs}ms`)), timeoutMs);
            });
            
            const analysis = await Promise.race([analysisPromise, timeoutPromise]) as any;
            const processingTime = Date.now() - startTime;
            
            return {
                fileName,
                filePath,
                fileExtension,
                fileSize,
                fileSizeFormatted,
                fileType: analysis.fileType,
                category: analysis.category,
                subcategory: analysis.subcategory,
                
                // Metadata
                author: analysis.metadata?.author,
                version: analysis.metadata?.version,
                modName: analysis.metadata?.modName,
                metadataSource: analysis.metadata?.metadataSource,
                metadataConfidence: analysis.metadata?.metadataConfidence,
                
                // Intelligence
                hasIntelligence: !!analysis.intelligence,
                qualityScore: analysis.intelligence?.qualityAssessment?.overallScore,
                riskLevel: analysis.intelligence?.dependencies?.riskLevel,
                dependencyCount: analysis.intelligence?.dependencies?.dependencies?.length || 0,
                hasResourceIntelligence: !!analysis.intelligence?.resourceIntelligence,
                
                // Resources
                resourceCount: analysis.resourceCount,
                resourceTypes: analysis.metadata?.resourceTypes,
                
                processingTime,
                analysisDate: new Date().toISOString()
            };
            
        } catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            return {
                fileName,
                filePath,
                fileExtension,
                fileSize: 0,
                fileSizeFormatted: '0 B',
                fileType: 'error',
                hasIntelligence: false,
                hasResourceIntelligence: false,
                processingTime,
                analysisDate: new Date().toISOString(),
                error: errorMessage,
                errorDetails: error instanceof Error ? error.stack : undefined
            };
        }
    }
    
    /**
     * Formats file size in human readable format
     */
    private formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    /**
     * Initializes the report file with header
     */
    private initializeReportFile(totalFiles: number): void {
        const header = `# Full Detailed Mod Analysis Report
Generated: ${new Date().toISOString()}
Total Files: ${totalFiles}

## Analysis Results

`;
        fs.writeFileSync(this.reportPath, header);
    }
    
    /**
     * Writes individual result to file
     */
    private writeIndividualResult(result: DetailedModResult, index: number): void {
        const content = `### [${index}] ${result.fileName}

**File Information:**
- Path: \`${result.filePath}\`
- Extension: ${result.fileExtension}
- Size: ${result.fileSizeFormatted} (${result.fileSize.toLocaleString()} bytes)
- Type: ${result.fileType}
- Category: ${result.category || 'N/A'}
- Subcategory: ${result.subcategory || 'N/A'}

**Metadata (Phase 3A):**
- Author: ${result.author || 'Not detected'}
- Version: ${result.version || 'Not detected'}
- Mod Name: ${result.modName || 'Not detected'}
- Source: ${result.metadataSource || 'N/A'}
- Confidence: ${result.metadataConfidence || 0}%

**Intelligence (Phase 4A):**
- Has Intelligence Data: ${result.hasIntelligence ? '✅ Yes' : '❌ No'}
- Quality Score: ${result.qualityScore || 'N/A'}/100
- Risk Level: ${result.riskLevel || 'N/A'}
- Dependencies: ${result.dependencyCount || 0}
- Resource Intelligence: ${result.hasResourceIntelligence ? '✅ Yes' : '❌ No'}

**Resources:**
- Resource Count: ${result.resourceCount || 'N/A'}
- Resource Types: ${result.resourceTypes?.join(', ') || 'N/A'}

**Performance:**
- Processing Time: ${result.processingTime}ms
- Analysis Date: ${result.analysisDate}

${result.error ? `**Error:**
- Error: ${result.error}
${result.errorDetails ? `- Details: \`${result.errorDetails}\`` : ''}` : '**Status:** ✅ Success'}

---

`;
        fs.appendFileSync(this.reportPath, content);
    }
    
    /**
     * Writes summary to file
     */
    private writeSummaryToFile(results: DetailedModResult[], totalElapsed: number): void {
        const summary = this.generateSummary(results, totalElapsed);
        fs.appendFileSync(this.reportPath, summary);
    }
    
    /**
     * Generates summary statistics
     */
    private generateSummary(results: DetailedModResult[], totalElapsed: number): string {
        const totalFiles = results.length;
        const successfulFiles = results.filter(r => !r.error).length;
        const scriptFiles = results.filter(r => r.fileExtension === '.ts4script');
        const packageFiles = results.filter(r => r.fileExtension === '.package');
        
        const filesWithAuthor = results.filter(r => r.author).length;
        const filesWithIntelligence = results.filter(r => r.hasIntelligence).length;
        const avgProcessingTime = Math.round(results.reduce((sum, r) => sum + r.processingTime, 0) / totalFiles);
        
        const totalSize = results.reduce((sum, r) => sum + r.fileSize, 0);
        const totalSizeFormatted = this.formatFileSize(totalSize);
        
        // Creator analysis
        const creators = new Map<string, number>();
        results.forEach(result => {
            if (result.author) {
                creators.set(result.author, (creators.get(result.author) || 0) + 1);
            }
        });
        
        const topCreators = Array.from(creators.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 20);
        
        return `
## Summary Statistics

**Collection Overview:**
- Total Files: ${totalFiles}
- Successful Analysis: ${successfulFiles}/${totalFiles} (${Math.round((successfulFiles/totalFiles)*100)}%)
- Script Files: ${scriptFiles.length}
- Package Files: ${packageFiles.length}
- Total Size: ${totalSizeFormatted}

**Metadata Extraction:**
- Author Detection: ${filesWithAuthor}/${totalFiles} (${Math.round((filesWithAuthor/totalFiles)*100)}%)
- Script Author Detection: ${scriptFiles.filter(r => r.author).length}/${scriptFiles.length} (${Math.round((scriptFiles.filter(r => r.author).length/scriptFiles.length)*100)}%)
- Package Author Detection: ${packageFiles.filter(r => r.author).length}/${packageFiles.length} (${Math.round((packageFiles.filter(r => r.author).length/packageFiles.length)*100)}%)

**Intelligence Integration:**
- Intelligence Coverage: ${filesWithIntelligence}/${totalFiles} (${Math.round((filesWithIntelligence/totalFiles)*100)}%)
- Resource Intelligence: ${results.filter(r => r.hasResourceIntelligence).length}/${totalFiles} (${Math.round((results.filter(r => r.hasResourceIntelligence).length/totalFiles)*100)}%)
- Quality Assessment: ${results.filter(r => r.qualityScore !== undefined).length}/${totalFiles} (${Math.round((results.filter(r => r.qualityScore !== undefined).length/totalFiles)*100)}%)

**Performance:**
- Total Processing Time: ${totalElapsed}s (${Math.round(totalElapsed/60)}m ${totalElapsed%60}s)
- Average Processing Time: ${avgProcessingTime}ms per file
- Throughput: ${Math.round(totalFiles / totalElapsed)} files/second

**Top Creators (by mod count):**
${topCreators.map(([creator, count]) => `- ${creator}: ${count} mods`).join('\n')}

**Errors:**
${results.filter(r => r.error).map(r => `- ${r.fileName}: ${r.error}`).join('\n') || 'None'}

---

*Report generated by Simonitor v4A - Advanced Intelligence Mod Management System*
*Analysis completed at: ${new Date().toISOString()}*
`;
    }
}

// Run the full detailed report generator
async function main() {
    const generator = new FullDetailedReportGenerator();
    await generator.generateFullDetailedReport();
}

if (require.main === module) {
    main().catch(console.error);
}
