/**
 * Debug Pattern Matching
 * 
 * Tests our pattern matching logic with the actual MCCC files
 * to find out why detection is failing.
 */

import { KnownConflictDatabase } from '../services/analysis/conflicts/KnownConflictDatabase';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Debug pattern matching with real MCCC files
 */
async function debugPatternMatching() {
    console.log('🔍 DEBUGGING PATTERN MATCHING');
    console.log('=============================\n');
    
    // Get actual MCCC files from assets
    const assetsPath = path.join(__dirname, '../../assets');
    const allFiles = fs.readdirSync(assetsPath);
    const mcccFiles = allFiles.filter(f => f.startsWith('mc_'));
    
    console.log('📁 MCCC Files Found in Assets:');
    mcccFiles.forEach(file => console.log(`   - ${file}`));
    console.log('');
    
    // Test with these real files
    const testMods = new Map();
    mcccFiles.forEach(file => {
        testMods.set(file, {} as any);
    });
    
    console.log('🔍 Testing Known Conflict Detection:');
    const conflicts = await KnownConflictDatabase.checkKnownConflicts(testMods);
    
    console.log(`📊 Results: ${conflicts.length} conflicts detected`);
    
    if (conflicts.length > 0) {
        console.log('✅ MCCC conflicts detected:');
        conflicts.forEach(conflict => {
            console.log(`   - ${conflict.title}`);
            console.log(`     Affected: ${conflict.affectedMods.join(', ')}`);
        });
    } else {
        console.log('❌ NO MCCC conflicts detected - investigating...\n');
        
        // Manual pattern testing
        console.log('🔍 Manual Pattern Testing:');
        const patterns = ['mc_cmd_center', 'mc_command_center', 'mccc'];
        
        for (const pattern of patterns) {
            console.log(`\n📋 Testing pattern: "${pattern}"`);
            for (const file of mcccFiles) {
                const fileName = file.toLowerCase();
                const matches = fileName.includes(pattern.toLowerCase());
                console.log(`   ${matches ? '✅' : '❌'} ${file} ${matches ? 'MATCHES' : 'no match'}`);
            }
        }
        
        // Check if our config is loading properly
        console.log('\n🔍 Checking Configuration Loading:');
        try {
            const configPath = path.join(__dirname, '../config/conflictRules.json');
            const configExists = fs.existsSync(configPath);
            console.log(`   Config file exists: ${configExists ? '✅' : '❌'} ${configPath}`);
            
            if (configExists) {
                const configData = fs.readFileSync(configPath, 'utf8');
                const config = JSON.parse(configData);
                console.log(`   Config loaded: ✅`);
                console.log(`   Verified conflicts: ${Object.keys(config.verifiedConflicts || {}).length}`);
                
                // Check MCCC config specifically
                if (config.verifiedConflicts?.mcccMultipleVersions) {
                    const mcccConfig = config.verifiedConflicts.mcccMultipleVersions;
                    console.log(`   MCCC patterns: ${JSON.stringify(mcccConfig.patterns)}`);
                } else {
                    console.log(`   ❌ MCCC config not found in verifiedConflicts`);
                }
            }
        } catch (error) {
            console.log(`   ❌ Config error: ${error.message}`);
        }
    }
}

// Run the debug test
if (require.main === module) {
    debugPatternMatching().catch(console.error);
}

export { debugPatternMatching };
