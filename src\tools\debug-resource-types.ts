/**
 * Debug resource type constants
 */

import { URT } from '../constants/unifiedResourceTypes';
import { BinaryResourceType } from '@s4tk/models/enums';

function debugResourceTypes(): void {
    console.log('🔍 Debug Resource Types');
    console.log('=' .repeat(40));
    
    console.log('S4TK BinaryResourceType.StringTable:', BinaryResourceType.StringTable);
    console.log('S4TK BinaryResourceType.StringTable (hex):', '0x' + BinaryResourceType.StringTable.toString(16).toUpperCase());
    
    console.log('URT.StringTable:', URT.StringTable);
    console.log('URT.StringTable (hex):', '0x' + URT.StringTable.toString(16).toUpperCase());
    
    console.log('Are they equal?', BinaryResourceType.StringTable === URT.StringTable);
    
    console.log('');
    console.log('Expected StringTable type from debug: 0x220557DA');
    console.log('Matches S4TK?', BinaryResourceType.StringTable === 0x220557DA);
    console.log('Matches URT?', URT.StringTable === 0x220557DA);
}

// Run the debug
if (require.main === module) {
    debugResourceTypes();
}

export { debugResourceTypes };
