import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

/**
 * The `contextBridge` is a secure way to expose APIs from the main process
 * to the renderer process. We will define a clear, typed API here for
 * all our inter-process communication.
 */
contextBridge.exposeInMainWorld('electronAPI', {
    /**
     * Analyzes a single package file with detailed intelligence
     * @param filePath The absolute path of the file to analyze
     * @returns Promise with analysis result
     */
    analyzePackage: (filePath: string) => ipcRenderer.invoke('analyze-package', filePath),

    /**
     * Analyzes all mod files in a folder recursively
     * @param folderPath The absolute path of the mods folder
     * @returns Promise with array of analysis results
     */
    analyzeModsFolder: (folderPath: string) => ipcRenderer.invoke('analyze-mods-folder', folderPath),

    /**
     * Opens a folder selection dialog
     * @returns Promise with selected folder path
     */
    selectModsFolder: () => ipcRenderer.invoke('select-mods-folder'),

    /**
     * Exports analysis results to file
     * @param data The analysis data to export
     * @param format The export format ('json' | 'csv')
     * @returns Promise with export result
     */
    exportResults: (data: any, format: 'json' | 'csv') => ipcRenderer.invoke('export-results', data, format),

    /**
     * Legacy support for old analysis method
     * @deprecated Use analyzePackage instead
     */
    onAnalysisResult: (callback: (result: any) => void) => {
        ipcRenderer.on('analysis-result', (_event, result) => {
            callback(result);
        });
    },
});