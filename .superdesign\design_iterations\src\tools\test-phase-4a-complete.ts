#!/usr/bin/env node

/**
 * Phase 4A Complete Integration Test
 * 
 * Comprehensive test showing both Phase 3A metadata extraction 
 * AND Phase 4A intelligence integration working together.
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface CompleteTestResult {
    filePath: string;
    fileType: string;
    
    // Phase 3A: Metadata
    author?: string;
    version?: string;
    modName?: string;
    metadataSource?: string;
    metadataConfidence?: number;
    
    // Phase 4A: Intelligence
    hasIntelligence: boolean;
    qualityScore?: number;
    riskLevel?: string;
    dependencyCount?: number;
    
    // Performance
    processingTime: number;
    error?: string;
}

class Phase4ACompleteTester {
    private analysisService: PackageAnalysisService;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
    }
    
    /**
     * Tests complete Phase 4A integration
     */
    public async testCompleteIntegration(): Promise<void> {
        console.log('🚀 Testing Phase 4A Complete Integration...\n');
        console.log('📊 Metadata Extraction (Phase 3A) + Intelligence Analysis (Phase 4A)\n');
        
        const assetsDir = path.join(process.cwd(), 'assets');
        
        if (!fs.existsSync(assetsDir)) {
            console.error('❌ Assets directory not found:', assetsDir);
            return;
        }
        
        const files = fs.readdirSync(assetsDir)
            .filter(file => file.endsWith('.package') || file.endsWith('.ts4script'))
            .slice(0, 5); // Limit to first 5 files for testing
        
        console.log(`📁 Found ${files.length} test files\n`);
        
        const results: CompleteTestResult[] = [];
        
        for (const file of files) {
            const filePath = path.join(assetsDir, file);
            const result = await this.testFile(filePath);
            results.push(result);
        }
        
        this.generateReport(results);
    }
    
    /**
     * Tests complete integration for a single file
     */
    private async testFile(filePath: string): Promise<CompleteTestResult> {
        const fileName = path.basename(filePath);
        console.log(`🔍 Testing Complete Analysis: ${fileName}`);
        
        const startTime = Date.now();
        
        try {
            const buffer = fs.readFileSync(filePath);
            const analysis = await this.analysisService.detailedAnalyzeAsync(buffer, filePath);
            
            const processingTime = Date.now() - startTime;
            
            const result: CompleteTestResult = {
                filePath: fileName,
                fileType: analysis.fileType,
                
                // Phase 3A: Metadata
                author: analysis.metadata.author,
                version: analysis.metadata.version,
                modName: analysis.metadata.modName,
                metadataSource: analysis.metadata.metadataSource,
                metadataConfidence: analysis.metadata.metadataConfidence,
                
                // Phase 4A: Intelligence
                hasIntelligence: !!analysis.intelligence,
                qualityScore: analysis.intelligence?.qualityAssessment?.overallScore,
                riskLevel: analysis.intelligence?.dependencies?.riskLevel,
                dependencyCount: analysis.intelligence?.dependencies?.dependencies?.length || 0,
                
                processingTime
            };
            
            // Display results
            console.log(`   📝 Author: ${result.author || 'No author'}`);
            console.log(`   📝 Version: ${result.version || 'No version'}`);
            console.log(`   📝 Source: ${result.metadataSource || 'unknown'} (${result.metadataConfidence || 0}%)`);
            console.log(`   🧠 Intelligence: ${result.hasIntelligence ? '✅ Present' : '❌ Missing'}`);
            console.log(`   ⭐ Quality Score: ${result.qualityScore || 'N/A'}/100`);
            console.log(`   ⚠️ Risk Level: ${result.riskLevel || 'unknown'}`);
            console.log(`   📦 Dependencies: ${result.dependencyCount}`);
            console.log(`   ⏱️ Processing Time: ${result.processingTime}ms`);
            console.log(`   ✅ Success\n`);
            
            return result;
            
        } catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            console.log(`   ❌ Error: ${errorMessage}`);
            console.log(`   ⏱️ Processing Time: ${processingTime}ms\n`);
            
            return {
                filePath: fileName,
                fileType: 'unknown',
                hasIntelligence: false,
                processingTime,
                error: errorMessage
            };
        }
    }
    
    /**
     * Generates comprehensive integration report
     */
    private generateReport(results: CompleteTestResult[]): void {
        console.log('🚀 PHASE 4A COMPLETE INTEGRATION REPORT');
        console.log('==================================================\n');
        
        // Calculate statistics
        const totalFiles = results.length;
        const successfulFiles = results.filter(r => !r.error).length;
        
        // Phase 3A metrics
        const filesWithAuthor = results.filter(r => r.author).length;
        const filesWithVersion = results.filter(r => r.version).length;
        const authorPercentage = Math.round((filesWithAuthor / totalFiles) * 100);
        const versionPercentage = Math.round((filesWithVersion / totalFiles) * 100);
        
        // Phase 4A metrics
        const filesWithIntelligence = results.filter(r => r.hasIntelligence).length;
        const intelligencePercentage = Math.round((filesWithIntelligence / totalFiles) * 100);
        
        console.log('📊 PHASE 3A - METADATA EXTRACTION:');
        console.log(`   Author Detection: ${filesWithAuthor}/${totalFiles} (${authorPercentage}%)`);
        console.log(`   Version Detection: ${filesWithVersion}/${totalFiles} (${versionPercentage}%)`);
        console.log(`   Target: 95%+ author detection ✅ ${authorPercentage >= 95 ? 'ACHIEVED' : 'NEEDS WORK'}\n`);
        
        console.log('🧠 PHASE 4A - INTELLIGENCE ANALYSIS:');
        console.log(`   Intelligence Data: ${filesWithIntelligence}/${totalFiles} (${intelligencePercentage}%)`);
        console.log(`   Target: 100% intelligence integration ✅ ${intelligencePercentage >= 100 ? 'ACHIEVED' : 'NEEDS WORK'}\n`);
        
        // Performance metrics
        const avgProcessingTime = Math.round(
            results.reduce((sum, r) => sum + r.processingTime, 0) / totalFiles
        );
        const maxProcessingTime = Math.max(...results.map(r => r.processingTime));
        
        console.log('⏱️ PERFORMANCE METRICS:');
        console.log(`   Average Processing Time: ${avgProcessingTime}ms`);
        console.log(`   Maximum Processing Time: ${maxProcessingTime}ms`);
        console.log(`   Target: <10ms average ✅ ${avgProcessingTime <= 10 ? 'ACHIEVED' : 'NEEDS OPTIMIZATION'}\n`);
        
        // Quality assessment
        const qualityScores = results
            .filter(r => r.qualityScore !== undefined)
            .map(r => r.qualityScore!);
        
        if (qualityScores.length > 0) {
            const avgQualityScore = Math.round(
                qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
            );
            console.log('⭐ QUALITY ASSESSMENT:');
            console.log(`   Average Quality Score: ${avgQualityScore}/100`);
            console.log(`   Range: ${Math.min(...qualityScores)} - ${Math.max(...qualityScores)}\n`);
        }
        
        // Overall success assessment
        const metadataSuccess = authorPercentage >= 95;
        const intelligenceSuccess = intelligencePercentage >= 100;
        const performanceSuccess = avgProcessingTime <= 10;
        
        const overallSuccess = metadataSuccess && intelligenceSuccess && performanceSuccess;
        
        console.log('🎯 OVERALL ASSESSMENT:');
        console.log(`   Phase 3A Metadata: ${metadataSuccess ? '✅ SUCCESS' : '❌ NEEDS WORK'}`);
        console.log(`   Phase 4A Intelligence: ${intelligenceSuccess ? '✅ SUCCESS' : '❌ NEEDS WORK'}`);
        console.log(`   Performance: ${performanceSuccess ? '✅ SUCCESS' : '❌ NEEDS OPTIMIZATION'}`);
        
        if (overallSuccess) {
            console.log('\n🏆 PHASE 4A INTEGRATION COMPLETE!');
            console.log('   ✅ 100% Author Detection Maintained');
            console.log('   ✅ 100% Intelligence Integration Achieved');
            console.log('   ✅ Performance Targets Met');
            console.log('   🚀 Ready for Phase 4B: Dashboard & Visualization');
        } else {
            console.log('\n📈 Phase 4A Progress:');
            if (!metadataSuccess) console.log('   📋 Continue improving metadata extraction');
            if (!intelligenceSuccess) console.log('   📋 Continue implementing intelligence integration');
            if (!performanceSuccess) console.log('   📋 Optimize performance for large-scale processing');
        }
        
        // Error summary
        const errors = results.filter(r => r.error);
        if (errors.length > 0) {
            console.log('\n❌ ERRORS:');
            errors.forEach(error => {
                console.log(`   ${error.filePath}: ${error.error}`);
            });
        }
    }
}

// Run the test
async function main() {
    const tester = new Phase4ACompleteTester();
    await tester.testCompleteIntegration();
}

if (require.main === module) {
    main().catch(console.error);
}
