/**
 * Test Conflict Detection System
 * 
 * Comprehensive test for the new conflict detection algorithms.
 * Tests with real mod files to validate actual conflict detection.
 */

import * as fs from 'fs';
import * as path from 'path';
import { Package } from '@s4tk/models';
import { ContentAnalysisService } from '../services/analysis/content/ContentAnalysisService';
import { ModCollectionAnalyzer } from '../services/analysis/conflicts/ModCollectionAnalyzer';
import { KnownConflictDatabase } from '../services/analysis/conflicts/KnownConflictDatabase';

/**
 * Test conflict detection with real mod collection
 */
async function testConflictDetection() {
    console.log('🔍 Testing Conflict Detection System');
    console.log('===================================\n');

    const contentAnalysisService = new ContentAnalysisService();
    const assetsPath = path.join(__dirname, '../../assets');
    
    if (!fs.existsSync(assetsPath)) {
        console.log('❌ Assets directory not found');
        return;
    }

    const modFiles = fs.readdirSync(assetsPath)
        .filter(file => file.endsWith('.package'))
        .slice(0, 20); // Test with first 20 mods for performance

    if (modFiles.length === 0) {
        console.log('❌ No .package files found');
        return;
    }

    console.log(`📦 Testing conflict detection with ${modFiles.length} mods:\n`);

    // Step 1: Analyze individual mods
    console.log('🔍 Step 1: Analyzing individual mods...');
    const modAnalyses = new Map();
    const packages = new Map();
    let analysisTime = 0;

    for (const modFile of modFiles) {
        const modPath = path.join(assetsPath, modFile);
        
        try {
            const buffer = fs.readFileSync(modPath);
            const startTime = performance.now();
            
            // Analyze content
            const analysis = await contentAnalysisService.analyzeModContent(buffer, modFile);
            modAnalyses.set(modFile, analysis);
            
            // Parse package for conflict detection
            const s4tkPackage = Package.from(buffer);
            packages.set(modFile, s4tkPackage);
            
            analysisTime += performance.now() - startTime;
            
            console.log(`   ✅ ${modFile} - ${analysis.contentType} (${analysis.totalItems} items)`);
            
        } catch (error) {
            console.error(`   ❌ Error analyzing ${modFile}:`, error.message);
        }
    }

    console.log(`\n📊 Individual analysis complete: ${analysisTime.toFixed(2)}ms total\n`);

    // Step 2: Collection-level conflict analysis
    console.log('🔍 Step 2: Analyzing collection for conflicts...');
    const collectionStartTime = performance.now();
    
    try {
        const collectionAnalysis = await ModCollectionAnalyzer.analyzeCollection(
            modAnalyses,
            packages
        );
        
        const collectionTime = performance.now() - collectionStartTime;
        
        // Display results
        console.log('\n📊 CONFLICT DETECTION RESULTS');
        console.log('=============================');
        console.log(`Total mods analyzed: ${collectionAnalysis.totalMods}`);
        console.log(`Total conflicts found: ${collectionAnalysis.totalConflicts}`);
        console.log(`Analysis time: ${collectionTime.toFixed(2)}ms`);
        
        // Conflict summary
        console.log('\n🚨 Conflict Summary:');
        console.log(`   Exact duplicates: ${collectionAnalysis.conflictSummary.byType.exactDuplicates}`);
        console.log(`   Override conflicts: ${collectionAnalysis.conflictSummary.byType.overrideConflicts}`);
        console.log(`   Hash collisions: ${collectionAnalysis.conflictSummary.byType.hashCollisions}`);
        console.log(`   Missing dependencies: ${collectionAnalysis.conflictSummary.byType.dependencyMissing}`);
        console.log(`   Known conflicts: ${collectionAnalysis.conflictSummary.byType.knownConflicts}`);
        
        // Severity breakdown
        console.log('\n⚠️  Severity Breakdown:');
        console.log(`   Critical: ${collectionAnalysis.conflictSummary.bySeverity.critical}`);
        console.log(`   High: ${collectionAnalysis.conflictSummary.bySeverity.high}`);
        console.log(`   Medium: ${collectionAnalysis.conflictSummary.bySeverity.medium}`);
        console.log(`   Low: ${collectionAnalysis.conflictSummary.bySeverity.low}`);
        
        // Auto-fix potential
        console.log('\n🔧 Resolution Potential:');
        console.log(`   Auto-fixable: ${collectionAnalysis.conflictSummary.autoFixableConflicts}`);
        console.log(`   Manual resolution required: ${collectionAnalysis.conflictSummary.manualResolutionRequired}`);
        
        // Detailed conflicts
        if (collectionAnalysis.resourceConflicts.length > 0) {
            console.log('\n🔍 Resource Conflicts:');
            collectionAnalysis.resourceConflicts.slice(0, 5).forEach((conflict, index) => {
                console.log(`   ${index + 1}. ${conflict.description}`);
                console.log(`      Type: ${conflict.conflictType}`);
                console.log(`      Severity: ${conflict.severity}`);
                console.log(`      Affected mods: ${conflict.affectedMods.slice(0, 3).join(', ')}${conflict.affectedMods.length > 3 ? '...' : ''}`);
                console.log(`      Auto-fix: ${conflict.autoFixAvailable ? 'Yes' : 'No'}`);
                console.log('');
            });
            
            if (collectionAnalysis.resourceConflicts.length > 5) {
                console.log(`   ... and ${collectionAnalysis.resourceConflicts.length - 5} more conflicts\n`);
            }
        }
        
        // Known conflicts
        if (collectionAnalysis.knownConflicts.length > 0) {
            console.log('🗃️  Known Conflicts:');
            collectionAnalysis.knownConflicts.forEach((conflict, index) => {
                console.log(`   ${index + 1}. ${conflict.title}`);
                console.log(`      ${conflict.description}`);
                console.log(`      Severity: ${conflict.severity}`);
                console.log(`      Affected mods: ${conflict.affectedMods.join(', ')}`);
                console.log('');
            });
        }
        
        // Recommendations
        if (collectionAnalysis.recommendations.length > 0) {
            console.log('💡 Recommendations:');
            collectionAnalysis.recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec.title} (${rec.priority})`);
                console.log(`      ${rec.description}`);
                console.log(`      Action required: ${rec.actionRequired ? 'Yes' : 'No'}`);
                console.log(`      Auto-fix available: ${rec.autoFixAvailable ? 'Yes' : 'No'}`);
                console.log('');
            });
        }
        
        // Organization suggestions
        if (collectionAnalysis.organizationSuggestions.length > 0) {
            console.log('📁 Organization Suggestions:');
            collectionAnalysis.organizationSuggestions.forEach((suggestion, index) => {
                console.log(`   ${index + 1}. ${suggestion.description}`);
                suggestion.suggestedStructure.forEach(folder => {
                    console.log(`      📂 ${folder.folderName}: ${folder.mods.length} mods (${folder.reason})`);
                });
                console.log('');
            });
        }
        
        // Performance assessment
        console.log('⚡ Performance Assessment:');
        const avgAnalysisTime = analysisTime / modFiles.length;
        const collectionAnalysisTime = collectionTime;
        
        console.log(`   Individual mod analysis: ${avgAnalysisTime.toFixed(2)}ms average`);
        console.log(`   Collection analysis: ${collectionAnalysisTime.toFixed(2)}ms`);
        console.log(`   Total time: ${(analysisTime + collectionTime).toFixed(2)}ms`);
        
        if (avgAnalysisTime < 100 && collectionAnalysisTime < 1000) {
            console.log('   ✅ Excellent performance!');
        } else if (avgAnalysisTime < 200 && collectionAnalysisTime < 2000) {
            console.log('   ✅ Good performance');
        } else {
            console.log('   ⚠️  Performance could be improved');
        }
        
        // Success assessment
        console.log('\n🎯 System Assessment:');
        console.log(`   Conflict detection: ${collectionAnalysis.totalConflicts > 0 ? 'WORKING' : 'NO CONFLICTS FOUND'}`);
        console.log(`   Known conflict database: ${collectionAnalysis.knownConflicts.length > 0 ? 'WORKING' : 'NO KNOWN CONFLICTS'}`);
        console.log(`   Recommendations: ${collectionAnalysis.recommendations.length > 0 ? 'WORKING' : 'NONE GENERATED'}`);
        console.log(`   Organization suggestions: ${collectionAnalysis.organizationSuggestions.length > 0 ? 'WORKING' : 'NONE GENERATED'}`);
        
        if (collectionAnalysis.totalConflicts > 0 || collectionAnalysis.recommendations.length > 0) {
            console.log('\n🎉 Conflict detection system is working correctly!');
        } else {
            console.log('\n💡 No conflicts detected - this may indicate a clean mod collection or need for more test mods');
        }
        
    } catch (error) {
        console.error('❌ Error during collection analysis:', error);
    }
}

/**
 * Test known conflict database with actual mod collection
 * No more fake/simulated mod names - only test with real files
 */
async function testKnownConflictDatabaseWithRealMods() {
    console.log('\n🗃️  Testing Known Conflict Database with Real Mods');
    console.log('==================================================\n');

    // This test is now integrated into the main conflict detection test
    // We only test with actual mod files, not simulated ones
    console.log('✅ Known conflict database testing is integrated with real mod analysis above.');
    console.log('💡 No separate testing with fake mod names - only real conflicts matter!');
}

// Run the tests
if (require.main === module) {
    testConflictDetection()
        .then(() => testKnownConflictDatabaseWithRealMods())
        .catch(console.error);
}

export { testConflictDetection };
