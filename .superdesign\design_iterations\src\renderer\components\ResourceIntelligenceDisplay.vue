<template>
  <div class="resource-intelligence">
    <!-- Category Overview -->
    <div class="resource-intelligence__overview">
      <div class="category-card">
        <div class="category-card__icon" :class="`category-${intelligence.category}`">
          <component :is="getCategoryIcon(intelligence.category)" />
        </div>
        <div class="category-card__content">
          <div class="category-card__label">Category</div>
          <div class="category-card__value">{{ formatCategory(intelligence.category) }}</div>
          <div class="category-card__subtitle">{{ formatContentType(intelligence.contentType) }}</div>
        </div>
      </div>
      
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">Resources</div>
          <div class="stat-value">{{ resourceCount }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Quality</div>
          <div class="stat-value">{{ intelligence.qualityScore }}/100</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">Impact</div>
          <div class="stat-value" :class="`impact-${intelligence.performance.estimatedImpact}`">
            {{ formatImpact(intelligence.performance.estimatedImpact) }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Resource Breakdown Chart -->
    <div class="resource-intelligence__section">
      <h5 class="section-title">Resource Breakdown</h5>
      <div class="resource-breakdown">
        <div class="breakdown-chart">
          <ResourceBreakdownChart :breakdown="intelligence.resourceBreakdown" />
        </div>
        <div class="breakdown-legend">
          <div 
            v-for="(count, type) in nonZeroResources" 
            :key="type"
            class="legend-item"
          >
            <div class="legend-color" :class="`resource-${type}`"></div>
            <div class="legend-label">{{ formatResourceType(type) }}</div>
            <div class="legend-count">{{ count }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Game Systems Affected -->
    <div v-if="intelligence.gameSystemsAffected.length > 0" class="resource-intelligence__section">
      <h5 class="section-title">Game Systems Affected</h5>
      <div class="systems-grid">
        <div 
          v-for="system in intelligence.gameSystemsAffected" 
          :key="system"
          class="system-item"
        >
          <component :is="getSystemIcon(system)" class="system-icon" />
          <span class="system-name">{{ formatSystem(system) }}</span>
        </div>
      </div>
    </div>
    
    <!-- Custom Content Analysis -->
    <div class="resource-intelligence__section">
      <h5 class="section-title">Custom Content Analysis</h5>
      <div class="custom-content-grid">
        <div class="cc-metric">
          <div class="cc-label">Type</div>
          <div class="cc-value">
            <span class="cc-badge" :class="{ 'cc-badge--custom': intelligence.customContent.isCustomContent }">
              {{ intelligence.customContent.isCustomContent ? 'Custom Content' : 'Override' }}
            </span>
          </div>
        </div>
        
        <div class="cc-metric">
          <div class="cc-label">New Resources</div>
          <div class="cc-value">{{ intelligence.customContent.newResourcesAdded }}</div>
        </div>
        
        <div class="cc-metric">
          <div class="cc-label">Mesh Quality</div>
          <div class="cc-value">
            <QualityIndicator :quality="intelligence.customContent.meshQuality" />
          </div>
        </div>
        
        <div class="cc-metric">
          <div class="cc-label">Texture Quality</div>
          <div class="cc-value">
            <QualityIndicator :quality="intelligence.customContent.textureQuality" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- Localization Info -->
    <div v-if="intelligence.localization.hasStringTables" class="resource-intelligence__section">
      <h5 class="section-title">Localization</h5>
      <div class="localization-info">
        <div class="localization-status">
          <GlobeAltIcon class="localization-icon" />
          <div class="localization-details">
            <div class="localization-primary">
              {{ intelligence.localization.languages.length > 0 ? 
                  `${intelligence.localization.languages.length} languages supported` : 
                  'String tables present' }}
            </div>
            <div class="localization-secondary">
              {{ intelligence.localization.localizationComplete ? 
                  'Complete localization' : 
                  'Partial localization' }}
            </div>
          </div>
        </div>
        
        <div v-if="intelligence.localization.languages.length > 0" class="language-list">
          <span 
            v-for="language in intelligence.localization.languages" 
            :key="language"
            class="language-tag"
          >
            {{ language }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- Performance Impact Details -->
    <div class="resource-intelligence__section">
      <h5 class="section-title">Performance Analysis</h5>
      <div class="performance-details">
        <div class="performance-overview">
          <div class="performance-score" :class="`impact-${intelligence.performance.estimatedImpact}`">
            <div class="score-value">{{ formatImpact(intelligence.performance.estimatedImpact) }}</div>
            <div class="score-label">Overall Impact</div>
          </div>
          
          <div class="performance-metrics">
            <div class="metric">
              <div class="metric-label">Complexity Score</div>
              <div class="metric-value">{{ intelligence.performance.complexityScore.toFixed(1) }}</div>
            </div>
            <div class="metric">
              <div class="metric-label">Total Size</div>
              <div class="metric-value">{{ formatBytes(intelligence.performance.totalSize) }}</div>
            </div>
          </div>
        </div>
        
        <div v-if="intelligence.performance.optimizationSuggestions.length > 0" class="optimization-suggestions">
          <h6 class="suggestions-title">Optimization Suggestions</h6>
          <ul class="suggestions-list">
            <li 
              v-for="suggestion in intelligence.performance.optimizationSuggestions" 
              :key="suggestion"
              class="suggestion-item"
            >
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  SwatchIcon,
  HomeIcon,
  CogIcon,
  DocumentTextIcon,
  CodeBracketIcon,
  GlobeAltIcon,
  UserIcon,
  HeartIcon,
  AcademicCapIcon,
  BriefcaseIcon,
  ChatBubbleLeftRightIcon,
  MapPinIcon,
  TrophyIcon,
  WrenchScrewdriverIcon,
  CubeIcon,
  PaintBrushIcon
} from '@heroicons/vue/24/outline';

import ResourceBreakdownChart from './ResourceBreakdownChart.vue';
import QualityIndicator from './QualityIndicator.vue';

interface ResourceIntelligence {
  category: string;
  subcategory: string;
  contentType: string;
  qualityScore: number;
  resourceBreakdown: {
    cas: number;
    buildBuy: number;
    gameplay: number;
    audio: number;
    visual: number;
    tuning: number;
    scripts: number;
    other: number;
  };
  gameSystemsAffected: string[];
  customContent: {
    isCustomContent: boolean;
    isOverride: boolean;
    originalResourcesModified: string[];
    newResourcesAdded: number;
    meshQuality: string;
    textureQuality: string;
  };
  localization: {
    languages: string[];
    hasStringTables: boolean;
    localizationComplete: boolean;
    missingTranslations: string[];
  };
  performance: {
    estimatedImpact: string;
    resourceCount: number;
    totalSize: number;
    complexityScore: number;
    optimizationSuggestions: string[];
  };
}

const props = defineProps<{
  intelligence: ResourceIntelligence;
  resourceCount: number;
}>();

// Computed properties
const nonZeroResources = computed(() => {
  const breakdown = props.intelligence.resourceBreakdown;
  return Object.fromEntries(
    Object.entries(breakdown).filter(([_, count]) => count > 0)
  );
});

// Formatting functions
const formatCategory = (category: string): string => {
  const categories: Record<string, string> = {
    'cas': 'Create-a-Sim',
    'build_buy': 'Build & Buy',
    'gameplay': 'Gameplay',
    'tuning_override': 'Tuning Override',
    'framework': 'Framework',
    'mixed': 'Mixed Content',
    'unknown': 'Unknown'
  };
  return categories[category] || category;
};

const formatContentType = (contentType: string): string => {
  const types: Record<string, string> = {
    'clothing': 'Clothing',
    'hair': 'Hair',
    'makeup': 'Makeup',
    'accessories': 'Accessories',
    'furniture': 'Furniture',
    'decorations': 'Decorations',
    'traits': 'Traits',
    'careers': 'Careers',
    'interactions': 'Interactions',
    'lot_traits': 'Lot Traits',
    'aspirations': 'Aspirations',
    'skills': 'Skills',
    'objects': 'Objects',
    'library': 'Library',
    'utility': 'Utility',
    'mixed': 'Mixed'
  };
  return types[contentType] || contentType;
};

const formatResourceType = (type: string): string => {
  const types: Record<string, string> = {
    'cas': 'CAS',
    'buildBuy': 'Build/Buy',
    'gameplay': 'Gameplay',
    'audio': 'Audio',
    'visual': 'Visual',
    'tuning': 'Tuning',
    'scripts': 'Scripts',
    'other': 'Other'
  };
  return types[type] || type;
};

const formatImpact = (impact: string): string => {
  const impacts: Record<string, string> = {
    'minimal': 'Minimal',
    'low': 'Low',
    'medium': 'Medium',
    'high': 'High'
  };
  return impacts[impact] || impact;
};

const formatSystem = (system: string): string => {
  const systems: Record<string, string> = {
    'cas': 'Create-a-Sim',
    'build_buy': 'Build & Buy Mode',
    'gameplay': 'Core Gameplay',
    'ui': 'User Interface',
    'audio': 'Audio System',
    'graphics': 'Graphics Engine'
  };
  return systems[system] || system;
};

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const getCategoryIcon = (category: string) => {
  const icons: Record<string, any> = {
    'cas': UserIcon,
    'build_buy': HomeIcon,
    'gameplay': CogIcon,
    'tuning_override': DocumentTextIcon,
    'framework': CodeBracketIcon,
    'mixed': CubeIcon,
    'unknown': CubeIcon
  };
  return icons[category] || CubeIcon;
};

const getSystemIcon = (system: string) => {
  const icons: Record<string, any> = {
    'cas': UserIcon,
    'build_buy': HomeIcon,
    'gameplay': CogIcon,
    'ui': PaintBrushIcon,
    'audio': SwatchIcon,
    'graphics': SwatchIcon
  };
  return icons[system] || CogIcon;
};
</script>

<style scoped>
.resource-intelligence {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Overview */
.resource-intelligence__overview {
  display: flex;
  gap: var(--space-6);
  align-items: stretch;
}

.category-card {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  flex: 1;
}

.category-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
}

.category-card__icon.category-cas {
  background: var(--sims-pink-bg);
  color: var(--sims-pink);
}

.category-card__icon.category-build_buy {
  background: var(--sims-blue-bg);
  color: var(--sims-blue);
}

.category-card__icon.category-gameplay {
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green);
}

.category-card__icon.category-tuning_override {
  background: var(--sims-orange-bg);
  color: var(--sims-orange);
}

.category-card__icon.category-framework {
  background: var(--sims-purple-bg);
  color: var(--sims-purple);
}

.category-card__icon svg {
  width: 24px;
  height: 24px;
}

.category-card__content {
  flex: 1;
}

.category-card__label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.category-card__value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.category-card__subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-4);
  flex: 1;
}

.stat-item {
  padding: var(--space-4);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  text-align: center;
}

.stat-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-2);
}

.stat-value {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

.stat-value.impact-minimal {
  color: var(--success);
}

.stat-value.impact-low {
  color: var(--info);
}

.stat-value.impact-medium {
  color: var(--warning);
}

.stat-value.impact-high {
  color: var(--error);
}

/* Sections */
.resource-intelligence__section {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.section-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Resource Breakdown */
.resource-breakdown {
  display: flex;
  gap: var(--space-6);
  align-items: flex-start;
}

.breakdown-chart {
  flex: 2;
}

.breakdown-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-sm);
}

.legend-color.resource-cas { background: var(--sims-pink); }
.legend-color.resource-buildBuy { background: var(--sims-blue); }
.legend-color.resource-gameplay { background: var(--plumbob-green); }
.legend-color.resource-visual { background: var(--sims-purple); }
.legend-color.resource-audio { background: var(--sims-orange); }
.legend-color.resource-tuning { background: var(--info); }
.legend-color.resource-scripts { background: var(--warning); }
.legend-color.resource-other { background: var(--gray-400); }

.legend-label {
  flex: 1;
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.legend-count {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}

/* Systems Grid */
.systems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.system-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.system-icon {
  width: 16px;
  height: 16px;
  color: var(--sims-blue);
}

.system-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

/* Custom Content */
.custom-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-4);
}

.cc-metric {
  text-align: center;
}

.cc-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-2);
}

.cc-value {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.cc-badge {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.cc-badge--custom {
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green);
}

/* Performance */
.performance-details {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.performance-overview {
  display: flex;
  gap: var(--space-6);
  align-items: center;
}

.performance-score {
  text-align: center;
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  min-width: 120px;
}

.performance-score.impact-minimal {
  background: var(--success-bg);
}

.performance-score.impact-low {
  background: var(--info-bg);
}

.performance-score.impact-medium {
  background: var(--warning-bg);
}

.performance-score.impact-high {
  background: var(--error-bg);
}

.score-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-1);
}

.score-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.8;
}

.performance-metrics {
  display: flex;
  gap: var(--space-6);
  flex: 1;
}

.metric {
  text-align: center;
}

.metric-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.metric-value {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
}
</style>
