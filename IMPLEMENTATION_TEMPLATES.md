# Phase 1 Implementation Templates & Code Guidelines

## Code Templates for Key Components

### StringTableProcessor.ts Template

```typescript
import { ResourceEntry, StringTableResource } from '@s4tk/models';
import { errorHandler } from '../../s4tk/ErrorHandler';
import { PerformanceMonitor } from '../core/PerformanceMonitor';

export interface StringTableData {
  modName?: string;
  description?: string;
  itemNames: string[];
  customStringCount: number;
  locale: string;
  confidence: number;
  processingTime: number;
}

export class StringTableProcessor {
  private static readonly MAX_STRING_LENGTH = 1000;
  private static readonly MAX_ITEMS = 500;
  
  /**
   * Process StringTable resource and extract mod metadata
   * @param entry ResourceEntry containing STBL data
   * @returns Promise<StringTableData> Extracted metadata with confidence scoring
   */
  static async processStringTable(entry: ResourceEntry): Promise<StringTableData> {
    const timer = PerformanceMonitor.startTiming('stringtable_processing');
    
    try {
      // Validate input
      if (!entry?.value?.buffer) {
        throw new Error('Invalid StringTable resource entry');
      }
      
      // Parse StringTable using S4TK
      const stringTable = StringTableResource.from(entry.value.buffer);
      
      // Extract metadata with safety checks
      const modName = this.extractModName(stringTable);
      const description = this.extractDescription(stringTable);
      const itemNames = this.extractItemNames(stringTable);
      
      // Calculate confidence score
      const confidence = this.calculateConfidence(modName, description, itemNames);
      
      const result: StringTableData = {
        modName,
        description,
        itemNames,
        customStringCount: stringTable.size,
        locale: stringTable.locale || 'unknown',
        confidence,
        processingTime: PerformanceMonitor.endTiming(timer)
      };
      
      return result;
      
    } catch (error) {
      PerformanceMonitor.endTiming(timer);
      errorHandler.handleS4TKError(error, 'processStringTable', entry.id?.toString());
      
      // Return safe fallback
      return {
        itemNames: [],
        customStringCount: 0,
        locale: 'unknown',
        confidence: 0,
        processingTime: 0
      };
    }
  }
  
  /**
   * Extract mod name using pattern matching
   */
  private static extractModName(stringTable: StringTableResource): string | undefined {
    const namePatterns = [
      /^MOD_NAME$/i,
      /^TITLE$/i,
      /^DISPLAY_NAME$/i,
      /^PACKAGE_NAME$/i,
      /^NAME$/i
    ];
    
    for (const [key, value] of stringTable.entries) {
      // Security: Validate string length
      if (value.length > this.MAX_STRING_LENGTH) continue;
      
      for (const pattern of namePatterns) {
        if (pattern.test(key)) {
          return this.sanitizeString(value);
        }
      }
    }
    
    return undefined;
  }
  
  /**
   * Extract description using pattern matching
   */
  private static extractDescription(stringTable: StringTableResource): string | undefined {
    const descPatterns = [
      /^DESCRIPTION$/i,
      /^MOD_DESCRIPTION$/i,
      /^PACKAGE_DESCRIPTION$/i,
      /^INFO$/i,
      /^ABOUT$/i
    ];
    
    for (const [key, value] of stringTable.entries) {
      // Security: Validate string length
      if (value.length > this.MAX_STRING_LENGTH) continue;
      
      for (const pattern of descPatterns) {
        if (pattern.test(key)) {
          return this.sanitizeString(value);
        }
      }
    }
    
    return undefined;
  }
  
  /**
   * Extract item names from string table
   */
  private static extractItemNames(stringTable: StringTableResource): string[] {
    const itemPatterns = [
      /^ITEM_\d+_NAME$/i,
      /^CAS_PART_\d+$/i,
      /^OBJECT_\d+_NAME$/i,
      /.*_NAME$/i
    ];
    
    const itemNames: string[] = [];
    
    for (const [key, value] of stringTable.entries) {
      // Security: Limit number of items and string length
      if (itemNames.length >= this.MAX_ITEMS) break;
      if (value.length > this.MAX_STRING_LENGTH) continue;
      
      for (const pattern of itemPatterns) {
        if (pattern.test(key)) {
          const sanitized = this.sanitizeString(value);
          if (sanitized && !itemNames.includes(sanitized)) {
            itemNames.push(sanitized);
          }
          break;
        }
      }
    }
    
    return itemNames;
  }
  
  /**
   * Calculate confidence score based on extracted data
   */
  private static calculateConfidence(
    modName?: string, 
    description?: string, 
    itemNames: string[] = []
  ): number {
    let confidence = 0;
    
    // Mod name contributes 40% to confidence
    if (modName) {
      confidence += 40;
      // Bonus for meaningful names (not just "Mod" or "Package")
      if (modName.length > 5 && !/^(mod|package)$/i.test(modName)) {
        confidence += 10;
      }
    }
    
    // Description contributes 30% to confidence
    if (description) {
      confidence += 30;
      // Bonus for detailed descriptions
      if (description.length > 50) {
        confidence += 10;
      }
    }
    
    // Item names contribute 20% to confidence
    if (itemNames.length > 0) {
      confidence += Math.min(20, itemNames.length * 5);
    }
    
    return Math.min(100, confidence);
  }
  
  /**
   * Sanitize string for safe display
   */
  private static sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/[<>]/g, '') // Remove potential HTML
      .substring(0, this.MAX_STRING_LENGTH);
  }
}
```

### EnhancedCASAnalyzer.ts Template

```typescript
import { ResourceEntry, CasPartResource } from '@s4tk/models';
import { URT } from '../../../constants/unifiedResourceTypes';
import { PerformanceMonitor } from '../core/PerformanceMonitor';

export interface CASAnalysisResult {
  items: CASItem[];
  totalItems: number;
  itemsByType: Record<CASItemType, number>;
  itemsByAgeGroup: Record<AgeGroup, number>;
  itemsByGender: Record<Gender, number>;
  categories: string[];
  processingTime: number;
}

export interface CASItem {
  name: string;
  type: CASItemType;
  ageGroups: AgeGroup[];
  gender: Gender;
  category: string;
  subcategory?: string;
}

export enum CASItemType {
  HAIR = 'hair',
  HAT = 'hat',
  TOP = 'top',
  BOTTOM = 'bottom',
  FULLBODY = 'fullbody',
  SHOES = 'shoes',
  ACCESSORIES = 'accessories',
  MAKEUP = 'makeup',
  SKIN = 'skin'
}

export enum AgeGroup {
  INFANT = 'infant',
  TODDLER = 'toddler',
  CHILD = 'child',
  TEEN = 'teen',
  ADULT = 'adult',
  ELDER = 'elder'
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  UNISEX = 'unisex'
}

export class EnhancedCASAnalyzer {
  private static readonly MAX_CAS_ITEMS = 1000;
  
  /**
   * Analyze CAS resources and extract detailed item information
   */
  static async analyzeCASResources(resources: ResourceEntry[]): Promise<CASAnalysisResult> {
    const timer = PerformanceMonitor.startTiming('cas_analysis');
    
    try {
      const casResources = resources.filter(r => r.key.type === URT.CasPart);
      
      if (casResources.length === 0) {
        return this.getEmptyResult(PerformanceMonitor.endTiming(timer));
      }
      
      const items = await this.extractCASItems(casResources);
      const analysis = this.categorizeItems(items);
      analysis.processingTime = PerformanceMonitor.endTiming(timer);
      
      return analysis;
      
    } catch (error) {
      PerformanceMonitor.endTiming(timer);
      console.error('CAS analysis failed:', error);
      return this.getEmptyResult(0);
    }
  }
  
  /**
   * Extract individual CAS items from resources
   */
  private static async extractCASItems(casResources: ResourceEntry[]): Promise<CASItem[]> {
    const items: CASItem[] = [];
    
    for (const resource of casResources.slice(0, this.MAX_CAS_ITEMS)) {
      try {
        const casPart = CasPartResource.from(resource.value.buffer);
        const item = this.parseCASPart(casPart, resource);
        if (item) {
          items.push(item);
        }
      } catch (error) {
        console.warn('Failed to parse CAS part:', error);
        continue;
      }
    }
    
    return items;
  }
  
  /**
   * Parse individual CAS part resource
   */
  private static parseCASPart(casPart: CasPartResource, resource: ResourceEntry): CASItem | null {
    try {
      return {
        name: this.extractItemName(casPart) || `CAS_${resource.id}`,
        type: this.mapCASType(casPart.bodyType),
        ageGroups: this.mapAgeGroups(casPart.ageGender),
        gender: this.mapGender(casPart.ageGender),
        category: this.mapCategory(casPart.category),
        subcategory: this.mapSubcategory(casPart.subcategory)
      };
    } catch (error) {
      console.warn('Failed to parse CAS part details:', error);
      return null;
    }
  }
  
  /**
   * Categorize items and generate statistics
   */
  private static categorizeItems(items: CASItem[]): CASAnalysisResult {
    const itemsByType: Record<CASItemType, number> = {} as any;
    const itemsByAgeGroup: Record<AgeGroup, number> = {} as any;
    const itemsByGender: Record<Gender, number> = {} as any;
    const categories = new Set<string>();
    
    // Initialize counters
    Object.values(CASItemType).forEach(type => itemsByType[type] = 0);
    Object.values(AgeGroup).forEach(age => itemsByAgeGroup[age] = 0);
    Object.values(Gender).forEach(gender => itemsByGender[gender] = 0);
    
    // Count items
    for (const item of items) {
      itemsByType[item.type]++;
      itemsByGender[item.gender]++;
      categories.add(item.category);
      
      // Count age groups (item can have multiple)
      for (const ageGroup of item.ageGroups) {
        itemsByAgeGroup[ageGroup]++;
      }
    }
    
    return {
      items,
      totalItems: items.length,
      itemsByType,
      itemsByAgeGroup,
      itemsByGender,
      categories: Array.from(categories),
      processingTime: 0 // Will be set by caller
    };
  }
  
  /**
   * Map S4TK body type to our CAS item type
   */
  private static mapCASType(bodyType: number): CASItemType {
    // S4TK body type mappings (these are example values)
    const typeMap: Record<number, CASItemType> = {
      1: CASItemType.HAIR,
      2: CASItemType.HAT,
      3: CASItemType.TOP,
      4: CASItemType.BOTTOM,
      5: CASItemType.FULLBODY,
      6: CASItemType.SHOES,
      7: CASItemType.ACCESSORIES,
      8: CASItemType.MAKEUP,
      9: CASItemType.SKIN
    };
    
    return typeMap[bodyType] || CASItemType.ACCESSORIES;
  }
  
  /**
   * Map age/gender flags to age groups
   */
  private static mapAgeGroups(ageGender: number): AgeGroup[] {
    const ageGroups: AgeGroup[] = [];
    
    // Age group bit flags (example values)
    if (ageGender & 0x0001) ageGroups.push(AgeGroup.INFANT);
    if (ageGender & 0x0002) ageGroups.push(AgeGroup.TODDLER);
    if (ageGender & 0x0004) ageGroups.push(AgeGroup.CHILD);
    if (ageGender & 0x0008) ageGroups.push(AgeGroup.TEEN);
    if (ageGender & 0x0010) ageGroups.push(AgeGroup.ADULT);
    if (ageGender & 0x0020) ageGroups.push(AgeGroup.ELDER);
    
    return ageGroups.length > 0 ? ageGroups : [AgeGroup.ADULT];
  }
  
  /**
   * Map age/gender flags to gender
   */
  private static mapGender(ageGender: number): Gender {
    // Gender bit flags (example values)
    const hasMale = ageGender & 0x1000;
    const hasFemale = ageGender & 0x2000;
    
    if (hasMale && hasFemale) return Gender.UNISEX;
    if (hasFemale) return Gender.FEMALE;
    if (hasMale) return Gender.MALE;
    
    return Gender.UNISEX;
  }
  
  /**
   * Extract item name from CAS part
   */
  private static extractItemName(casPart: CasPartResource): string | undefined {
    // Try to extract name from CAS part data
    // This would depend on the actual S4TK CasPartResource structure
    return undefined; // Placeholder
  }
  
  /**
   * Map category number to string
   */
  private static mapCategory(category: number): string {
    const categoryMap: Record<number, string> = {
      1: 'Hair',
      2: 'Hats',
      3: 'Tops',
      4: 'Bottoms',
      5: 'Full Body',
      6: 'Shoes',
      7: 'Accessories',
      8: 'Makeup',
      9: 'Skin Details'
    };
    
    return categoryMap[category] || 'Unknown';
  }
  
  /**
   * Map subcategory number to string
   */
  private static mapSubcategory(subcategory: number): string | undefined {
    // Implementation depends on S4TK subcategory mappings
    return undefined; // Placeholder
  }
  
  /**
   * Get empty result for error cases
   */
  private static getEmptyResult(processingTime: number): CASAnalysisResult {
    const itemsByType = {} as Record<CASItemType, number>;
    const itemsByAgeGroup = {} as Record<AgeGroup, number>;
    const itemsByGender = {} as Record<Gender, number>;
    
    Object.values(CASItemType).forEach(type => itemsByType[type] = 0);
    Object.values(AgeGroup).forEach(age => itemsByAgeGroup[age] = 0);
    Object.values(Gender).forEach(gender => itemsByGender[gender] = 0);
    
    return {
      items: [],
      totalItems: 0,
      itemsByType,
      itemsByAgeGroup,
      itemsByGender,
      categories: [],
      processingTime
    };
  }
}
```

### ContentCounter.vue Template

```vue
<template>
  <div 
    class="content-counter" 
    :class="[
      `content-counter--${type}`,
      `content-counter--${size}`,
      { 'content-counter--zero': count === 0 }
    ]"
    :aria-label="`${count} ${getLabel(type, count)}`"
  >
    <component 
      :is="getIcon(type)" 
      class="content-counter__icon"
      :aria-hidden="true"
    />
    <span class="content-counter__count">{{ formatCount(count) }}</span>
    <span v-if="showLabel" class="content-counter__label">
      {{ getLabel(type, count) }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  UserIcon,
  HomeIcon,
  StarIcon,
  BriefcaseIcon,
  CubeIcon,
  SparklesIcon
} from '@heroicons/vue/24/outline';

interface Props {
  type: 'hair' | 'clothing' | 'traits' | 'careers' | 'objects' | 'makeup';
  count: number;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  maxCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showLabel: true,
  maxCount: 999
});

const getIcon = (type: string) => {
  const iconMap = {
    hair: UserIcon,
    clothing: UserIcon,
    traits: StarIcon,
    careers: BriefcaseIcon,
    objects: HomeIcon,
    makeup: SparklesIcon
  };
  return iconMap[type as keyof typeof iconMap] || CubeIcon;
};

const getLabel = (type: string, count: number) => {
  const labels = {
    hair: count === 1 ? 'Hairstyle' : 'Hairstyles',
    clothing: count === 1 ? 'Clothing' : 'Clothing',
    traits: count === 1 ? 'Trait' : 'Traits',
    careers: count === 1 ? 'Career' : 'Careers',
    objects: count === 1 ? 'Object' : 'Objects',
    makeup: count === 1 ? 'Makeup' : 'Makeup'
  };
  return labels[type as keyof typeof labels] || 'Items';
};

const formatCount = (count: number) => {
  if (count > props.maxCount) {
    return `${props.maxCount}+`;
  }
  return count.toString();
};
</script>

<style scoped>
.content-counter {
  @apply inline-flex items-center gap-1 px-2 py-1 rounded-md text-sm font-medium;
  @apply bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300;
  transition: all 0.2s ease;
}

.content-counter--xs {
  @apply text-xs px-1.5 py-0.5 gap-0.5;
}

.content-counter--sm {
  @apply text-xs px-1.5 py-0.5 gap-1;
}

.content-counter--lg {
  @apply text-base px-3 py-2 gap-2;
}

.content-counter--zero {
  @apply opacity-50;
}

.content-counter__icon {
  @apply w-4 h-4 flex-shrink-0;
}

.content-counter--xs .content-counter__icon {
  @apply w-3 h-3;
}

.content-counter--lg .content-counter__icon {
  @apply w-5 h-5;
}

.content-counter__count {
  @apply font-semibold;
}

.content-counter__label {
  @apply hidden sm:inline;
}

/* Type-specific colors */
.content-counter--hair {
  @apply bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300;
}

.content-counter--clothing {
  @apply bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300;
}

.content-counter--traits {
  @apply bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300;
}

.content-counter--careers {
  @apply bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300;
}

.content-counter--objects {
  @apply bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300;
}

.content-counter--makeup {
  @apply bg-pink-100 text-pink-700 dark:bg-pink-900 dark:text-pink-300;
}
</style>
```

## Development Guidelines

### Error Handling Standards
```typescript
// Always use try-catch for S4TK operations
try {
  const resource = ResourceType.from(buffer);
  return processResource(resource);
} catch (error) {
  errorHandler.handleS4TKError(error, 'operationName', context);
  return fallbackValue;
}

// Validate inputs before processing
if (!buffer || buffer.length === 0) {
  throw new Error('Invalid buffer provided');
}

// Use performance monitoring
const timer = PerformanceMonitor.startTiming('operation');
try {
  // ... operation
  return result;
} finally {
  PerformanceMonitor.endTiming(timer);
}
```

### Security Best Practices
```typescript
// Sanitize all string inputs
private static sanitizeString(input: string): string {
  return input
    .trim()
    .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
    .replace(/[<>]/g, '') // Remove potential HTML
    .substring(0, MAX_STRING_LENGTH);
}

// Validate numeric inputs
private static validateNumber(value: number, min: number, max: number): number {
  if (isNaN(value) || value < min || value > max) {
    throw new Error(`Invalid number: ${value}`);
  }
  return value;
}

// Limit array sizes
private static limitArray<T>(array: T[], maxSize: number): T[] {
  return array.slice(0, maxSize);
}
```

### Testing Standards
```typescript
// Unit test template
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup
  });
  
  afterEach(() => {
    // Cleanup
  });
  
  it('should handle valid input correctly', () => {
    // Arrange
    const input = createValidInput();
    
    // Act
    const result = component.process(input);
    
    // Assert
    expect(result).toBeDefined();
    expect(result.property).toBe(expectedValue);
  });
  
  it('should handle invalid input gracefully', () => {
    // Arrange
    const invalidInput = createInvalidInput();
    
    // Act & Assert
    expect(() => component.process(invalidInput)).not.toThrow();
  });
  
  it('should meet performance requirements', async () => {
    // Arrange
    const largeInput = createLargeInput();
    
    // Act
    const startTime = performance.now();
    await component.process(largeInput);
    const endTime = performance.now();
    
    // Assert
    expect(endTime - startTime).toBeLessThan(10); // 10ms limit
  });
});
```

## Quality Assurance Checklist

### Code Review Checklist
- [ ] **Type Safety**: All functions have proper TypeScript types
- [ ] **Error Handling**: Try-catch blocks around all S4TK operations
- [ ] **Performance**: Operations complete within time limits
- [ ] **Security**: Input validation and sanitization implemented
- [ ] **Testing**: Unit tests cover happy path and error cases
- [ ] **Documentation**: JSDoc comments for public methods
- [ ] **Accessibility**: UI components have proper ARIA labels

### Performance Validation
```typescript
// Performance test template
describe('Performance Tests', () => {
  it('should process StringTable under 3ms', async () => {
    const testFile = loadTestSTBL();
    const iterations = 100;
    const times: number[] = [];

    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      await StringTableProcessor.processStringTable(testFile);
      times.push(performance.now() - start);
    }

    const average = times.reduce((a, b) => a + b) / times.length;
    expect(average).toBeLessThan(3);
  });
});
```

### Security Validation
```typescript
// Security test template
describe('Security Tests', () => {
  it('should handle malformed STBL data safely', () => {
    const malformedData = createMalformedSTBL();
    expect(() => StringTableProcessor.processStringTable(malformedData)).not.toThrow();
  });

  it('should sanitize extracted strings', () => {
    const maliciousString = '<script>alert("xss")</script>';
    const sanitized = StringTableProcessor.sanitizeString(maliciousString);
    expect(sanitized).not.toContain('<script>');
  });
});
```

### Integration Test Strategy
1. **Real Mod Testing**: Test with 50+ real mod files from different creators
2. **Edge Case Testing**: Test with corrupted, empty, and malformed files
3. **Performance Testing**: Validate with large mod collections (500+ files)
4. **Cross-Platform Testing**: Test on Windows, macOS, and Linux
5. **Memory Testing**: Monitor for memory leaks during extended use

### Deployment Checklist
- [ ] All unit tests passing
- [ ] Integration tests passing
- [ ] Performance benchmarks met
- [ ] Security tests passing
- [ ] Code review completed
- [ ] Documentation updated
- [ ] User acceptance testing completed

This implementation template provides the foundation for building robust, secure, and performant components for Phase 1 of the Simonitor enhancement project.
