/**
 * Tuning Resource Metadata Extractor
 * 
 * Extracts metadata from XML tuning resources by analyzing comments,
 * tuning IDs, and XML structure for creator information.
 */

import { DEFAULT_METADATA_CONFIG, type MetadataExtractionConfig } from '../../../config/metadata-extraction.config';

export interface TuningMetadata {
    readonly author?: string;
    readonly modName?: string;
    readonly version?: string;
    readonly description?: string;
    readonly confidence: number;
    readonly source: 'tuning';
    readonly tuningId?: string;
    readonly tuningType?: string;
    readonly comments: readonly string[];
    readonly processingTime: number;
}

export interface TuningAnalysisResult {
    readonly hasCreatorInfo: boolean;
    readonly extractedComments: readonly string[];
    readonly tuningIdentifiers: readonly string[];
    readonly xmlStructureInfo: {
        readonly rootElement?: string;
        readonly className?: string;
        readonly instanceName?: string;
        readonly moduleName?: string;
    };
}

/**
 * Tuning resource metadata extractor
 */
export class TuningMetadataExtractor {
    private readonly config: MetadataExtractionConfig;
    
    constructor(config: Partial<MetadataExtractionConfig> = {}) {
        this.config = {
            ...DEFAULT_METADATA_CONFIG,
            ...config
        };
    }
    
    /**
     * Extracts metadata from XML tuning content
     */
    async extractMetadata(xmlContent: string, tuningId?: string): Promise<TuningMetadata | null> {
        const startTime = performance.now();
        
        try {
            // Security validation
            if (!this.isValidXmlContent(xmlContent)) {
                return null;
            }
            
            // Analyze the tuning content
            const analysis = this.analyzeTuningContent(xmlContent);
            
            // Extract metadata from analysis
            const metadata = this.extractMetadataFromAnalysis(analysis, tuningId);
            
            const processingTime = performance.now() - startTime;
            
            if (metadata) {
                return {
                    ...metadata,
                    processingTime
                };
            }
            
            return null;
            
        } catch (error) {
            console.warn('[TuningMetadataExtractor] Extraction failed:', error);
            return null;
        }
    }
    
    /**
     * Validates XML content for security
     */
    private isValidXmlContent(xmlContent: string): boolean {
        if (!xmlContent || typeof xmlContent !== 'string') {
            return false;
        }
        
        // Check for reasonable size limits
        if (xmlContent.length > 10 * 1024 * 1024) { // 10MB limit
            return false;
        }
        
        // Check for XML structure
        if (!xmlContent.includes('<') || !xmlContent.includes('>')) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Analyzes tuning content for metadata
     */
    private analyzeTuningContent(xmlContent: string): TuningAnalysisResult {
        const result: {
            hasCreatorInfo: boolean;
            extractedComments: string[];
            tuningIdentifiers: string[];
            xmlStructureInfo: {
                rootElement?: string;
                className?: string;
                instanceName?: string;
                moduleName?: string;
            };
        } = {
            hasCreatorInfo: false,
            extractedComments: [],
            tuningIdentifiers: [],
            xmlStructureInfo: {}
        };
        
        // Extract comments
        result.extractedComments = this.extractComments(xmlContent);
        
        // Extract XML structure information
        result.xmlStructureInfo = this.extractXmlStructureInfo(xmlContent);
        
        // Extract tuning identifiers
        result.tuningIdentifiers = this.extractTuningIdentifiers(xmlContent);
        
        // Check if we found creator information
        result.hasCreatorInfo = result.extractedComments.length > 0 || 
                               result.tuningIdentifiers.some(id => this.containsCreatorInfo(id));
        
        return result;
    }
    
    /**
     * Extracts comments from XML content
     */
    private extractComments(xmlContent: string): string[] {
        const comments: string[] = [];
        
        for (const pattern of this.config.tuning.commentPatterns) {
            let match;
            const regex = new RegExp(pattern.source, pattern.flags);
            
            while ((match = regex.exec(xmlContent)) !== null) {
                const comment = match[1]?.trim();
                if (comment && 
                    comment.length <= this.config.tuning.maxCommentLength &&
                    !comments.includes(comment)) {
                    comments.push(this.sanitizeComment(comment));
                }
                
                // Prevent infinite loops with global regex
                if (!pattern.global) break;
            }
        }
        
        return comments;
    }
    
    /**
     * Extracts XML structure information
     */
    private extractXmlStructureInfo(xmlContent: string): {
        rootElement?: string;
        className?: string;
        instanceName?: string;
        moduleName?: string;
    } {
        const info: {
            rootElement?: string;
            className?: string;
            instanceName?: string;
            moduleName?: string;
        } = {};
        
        // Extract root element
        const rootMatch = xmlContent.match(/<(\w+)[^>]*>/);
        if (rootMatch) {
            info.rootElement = rootMatch[1];
        }
        
        // Extract class name (c attribute)
        const classMatch = xmlContent.match(/\bc\s*=\s*["']([^"']+)["']/);
        if (classMatch) {
            info.className = classMatch[1];
        }
        
        // Extract instance name (n attribute)
        const instanceMatch = xmlContent.match(/\bn\s*=\s*["']([^"']+)["']/);
        if (instanceMatch) {
            info.instanceName = instanceMatch[1];
        }
        
        // Extract module name (m attribute)
        const moduleMatch = xmlContent.match(/\bm\s*=\s*["']([^"']+)["']/);
        if (moduleMatch) {
            info.moduleName = moduleMatch[1];
        }
        
        return info;
    }
    
    /**
     * Extracts tuning identifiers from XML
     */
    private extractTuningIdentifiers(xmlContent: string): string[] {
        const identifiers: string[] = [];
        
        // Extract various identifier patterns
        const patterns = [
            /\bn\s*=\s*["']([^"']+)["']/g,  // name attribute
            /\bs\s*=\s*["']([^"']+)["']/g,  // instance attribute
            /\bm\s*=\s*["']([^"']+)["']/g,  // module attribute
            /\bc\s*=\s*["']([^"']+)["']/g   // class attribute
        ];
        
        for (const pattern of patterns) {
            let match;
            while ((match = pattern.exec(xmlContent)) !== null) {
                const identifier = match[1]?.trim();
                if (identifier && !identifiers.includes(identifier)) {
                    identifiers.push(identifier);
                }
            }
        }
        
        return identifiers;
    }
    
    /**
     * Extracts metadata from analysis results
     */
    private extractMetadataFromAnalysis(
        analysis: TuningAnalysisResult,
        tuningId?: string
    ): Omit<TuningMetadata, 'processingTime'> | null {
        const metadata: {
            author?: string;
            modName?: string;
            version?: string;
            description?: string;
            confidence: number;
            source: 'tuning';
            tuningId?: string;
            tuningType?: string;
            comments: string[];
        } = {
            source: 'tuning',
            confidence: 0,
            comments: [...analysis.extractedComments],
            tuningId
        };
        
        // Extract creator information from comments
        const creatorInfo = this.extractCreatorFromComments(analysis.extractedComments);
        if (creatorInfo.author) metadata.author = creatorInfo.author;
        if (creatorInfo.modName) metadata.modName = creatorInfo.modName;
        if (creatorInfo.version) metadata.version = creatorInfo.version;
        if (creatorInfo.description) metadata.description = creatorInfo.description;
        
        // Extract information from tuning identifiers
        const identifierInfo = this.extractInfoFromIdentifiers(analysis.tuningIdentifiers);
        if (!metadata.author && identifierInfo.author) metadata.author = identifierInfo.author;
        if (!metadata.modName && identifierInfo.modName) metadata.modName = identifierInfo.modName;
        
        // Extract tuning type from XML structure
        if (analysis.xmlStructureInfo.className) {
            metadata.tuningType = analysis.xmlStructureInfo.className;
        }
        
        // Calculate confidence
        metadata.confidence = this.calculateTuningConfidence(metadata, analysis);
        
        // Only return if we found meaningful metadata
        if (metadata.author || metadata.modName || metadata.version || metadata.comments.length > 0) {
            return metadata;
        }
        
        return null;
    }
    
    /**
     * Extracts creator information from comments
     */
    private extractCreatorFromComments(comments: string[]): {
        author?: string;
        modName?: string;
        version?: string;
        description?: string;
    } {
        const result: {
            author?: string;
            modName?: string;
            version?: string;
            description?: string;
        } = {};
        
        for (const comment of comments) {
            // Try creator patterns
            for (const pattern of this.config.tuning.creatorPatterns) {
                const match = comment.match(pattern);
                if (match && match[1]) {
                    const extracted = match[1].trim();
                    if (!result.author && this.looksLikeAuthor(extracted)) {
                        result.author = extracted;
                    }
                }
            }
            
            // Look for version patterns
            const versionMatch = comment.match(/v(?:ersion)?[\s:]*(\d+(?:\.\d+)*)/i);
            if (versionMatch && !result.version) {
                result.version = versionMatch[1];
            }
            
            // Use longer comments as descriptions
            if (!result.description && comment.length > 20 && comment.length < 200) {
                result.description = comment;
            }
        }
        
        return result;
    }
    
    /**
     * Extracts information from tuning identifiers
     */
    private extractInfoFromIdentifiers(identifiers: string[]): {
        author?: string;
        modName?: string;
    } {
        const result: {
            author?: string;
            modName?: string;
        } = {};
        
        for (const identifier of identifiers) {
            // Look for creator prefixes in identifiers
            const parts = identifier.split(/[._:]/);
            if (parts.length >= 2) {
                const firstPart = parts[0];
                if (this.looksLikeAuthor(firstPart) && !result.author) {
                    result.author = firstPart;
                }
                
                const secondPart = parts[1];
                if (this.looksLikeModName(secondPart) && !result.modName) {
                    result.modName = secondPart;
                }
            }
        }
        
        return result;
    }
    
    /**
     * Checks if a string contains creator information
     */
    private containsCreatorInfo(text: string): boolean {
        return this.config.tuning.creatorPatterns.some(pattern => pattern.test(text));
    }
    
    /**
     * Checks if a string looks like an author name
     */
    private looksLikeAuthor(text: string): boolean {
        return text.length >= 2 && 
               text.length <= 30 &&
               /[a-zA-Z]/.test(text) &&
               !/^\d+$/.test(text);
    }
    
    /**
     * Checks if a string looks like a mod name
     */
    private looksLikeModName(text: string): boolean {
        return text.length >= 3 && 
               text.length <= 50 &&
               /[a-zA-Z]/.test(text) &&
               !/^\d+$/.test(text);
    }
    
    /**
     * Sanitizes comment text
     */
    private sanitizeComment(comment: string): string {
        return comment
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control chars
            .trim()
            .substring(0, this.config.tuning.maxCommentLength);
    }
    
    /**
     * Calculates confidence for tuning metadata
     */
    private calculateTuningConfidence(
        metadata: { author?: string; modName?: string; version?: string; comments: string[] },
        analysis: TuningAnalysisResult
    ): number {
        let confidence = 10; // Base confidence
        
        if (metadata.author) confidence += 25;
        if (metadata.modName) confidence += 20;
        if (metadata.version) confidence += 15;
        if (metadata.comments.length > 0) confidence += 10;
        
        // Boost for having creator-specific comments
        if (analysis.hasCreatorInfo) confidence += 15;
        
        // Boost for structured tuning identifiers
        if (analysis.tuningIdentifiers.length > 0) confidence += 10;
        
        return Math.min(confidence, 100);
    }
}
