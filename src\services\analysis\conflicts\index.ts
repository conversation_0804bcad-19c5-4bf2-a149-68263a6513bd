/**
 * Conflict Detection Module
 * 
 * Comprehensive conflict detection system that replaces TODO placeholders
 * with actual working algorithms for the #1 Reddit user request.
 */

// Core conflict detection
export { ResourceConflictDetector } from './ResourceConflictDetector';
export { ModCollectionAnalyzer } from './ModCollectionAnalyzer';
export { KnownConflictDatabase } from './KnownConflictDatabase';

// Types and interfaces
export type {
    ResourceConflict,
    ResourceIdentifier,
    ConflictResolution
} from './ResourceConflictDetector';

export type {
    ModCollectionAnalysis,
    ConflictSummary,
    CollectionRecommendation,
    OrganizationSuggestion,
    FolderStructure
} from './ModCollectionAnalyzer';

export type {
    KnownConflict,
    ConflictPattern,
    ConflictCondition
} from './KnownConflictDatabase';

// Enums
export {
    ResourceConflictType,
    ResolutionStrategy
} from './ResourceConflictDetector';

export {
    RecommendationType
} from './ModCollectionAnalyzer';

export {
    KnownConflictType
} from './KnownConflictDatabase';
