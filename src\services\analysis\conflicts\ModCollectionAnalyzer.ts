/**
 * Mod Collection Analyzer
 * 
 * Orchestrates cross-mod analysis to detect conflicts, dependencies, and compatibility issues
 * across entire mod collections. This provides the missing piece for comprehensive
 * conflict detection that requires analyzing multiple mods together.
 * 
 * Addresses Reddit requests for automated mod management and conflict detection.
 */

import { Package } from '@s4tk/models';
import { ModContentAnalysis } from '../content/ContentAnalysisService';
import { ResourceConflictDetector, ResourceConflict } from './ResourceConflictDetector';
import { KnownConflictDatabase, KnownConflict } from './KnownConflictDatabase';
import { ConflictSeverity } from '../specialized/cas/types';

/**
 * Collection-level analysis result
 */
export interface ModCollectionAnalysis {
    totalMods: number;
    totalConflicts: number;
    conflictSummary: ConflictSummary;
    resourceConflicts: ResourceConflict[];
    knownConflicts: KnownConflict[];
    recommendations: CollectionRecommendation[];
    organizationSuggestions: OrganizationSuggestion[];
    analysisTime: number;
}

/**
 * Summary of conflicts by type and severity
 */
export interface ConflictSummary {
    byType: {
        exactDuplicates: number;
        overrideConflicts: number;
        hashCollisions: number;
        dependencyMissing: number;
        knownConflicts: number;
    };
    bySeverity: {
        low: number;
        medium: number;
        high: number;
        critical: number;
    };
    autoFixableConflicts: number;
    manualResolutionRequired: number;
}

/**
 * Collection-level recommendations
 */
export interface CollectionRecommendation {
    type: RecommendationType;
    priority: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    description: string;
    affectedMods: string[];
    actionRequired: boolean;
    autoFixAvailable: boolean;
}

export enum RecommendationType {
    REMOVE_CONFLICTING = 'remove_conflicting',
    UPDATE_OUTDATED = 'update_outdated',
    INSTALL_DEPENDENCIES = 'install_dependencies',
    REORGANIZE_STRUCTURE = 'reorganize_structure',
    PERFORMANCE_OPTIMIZATION = 'performance_optimization'
}

/**
 * Organization suggestions for mod collection
 */
export interface OrganizationSuggestion {
    type: 'folder_structure' | 'load_order' | 'dependency_grouping';
    description: string;
    suggestedStructure: FolderStructure[];
    benefits: string[];
}

export interface FolderStructure {
    folderName: string;
    mods: string[];
    reason: string;
    priority: number;
}

/**
 * Main Mod Collection Analyzer
 */
export class ModCollectionAnalyzer {
    
    /**
     * Analyzes an entire mod collection for conflicts and compatibility
     */
    public static async analyzeCollection(
        modAnalyses: Map<string, ModContentAnalysis>,
        packages: Map<string, Package>
    ): Promise<ModCollectionAnalysis> {
        const startTime = performance.now();
        
        console.log(`[ModCollectionAnalyzer] Analyzing collection of ${modAnalyses.size} mods...`);
        
        // Detect resource conflicts
        const resourceConflicts = ResourceConflictDetector.detectConflicts(modAnalyses, packages);
        
        // Check against known conflict database
        const knownConflicts = await KnownConflictDatabase.checkKnownConflicts(modAnalyses);
        
        // Generate conflict summary
        const conflictSummary = this.generateConflictSummary(resourceConflicts, knownConflicts);
        
        // Generate recommendations
        const recommendations = this.generateRecommendations(
            modAnalyses, 
            resourceConflicts, 
            knownConflicts
        );
        
        // Generate organization suggestions
        const organizationSuggestions = this.generateOrganizationSuggestions(
            modAnalyses, 
            resourceConflicts
        );
        
        const analysisTime = performance.now() - startTime;
        
        const result: ModCollectionAnalysis = {
            totalMods: modAnalyses.size,
            totalConflicts: resourceConflicts.length + knownConflicts.length,
            conflictSummary,
            resourceConflicts,
            knownConflicts,
            recommendations,
            organizationSuggestions,
            analysisTime
        };
        
        console.log(`[ModCollectionAnalyzer] Analysis complete: ${result.totalConflicts} conflicts found in ${analysisTime.toFixed(2)}ms`);
        
        return result;
    }
    
    /**
     * Updates individual mod analyses with collection-level conflict information
     */
    public static updateModAnalysesWithConflicts(
        modAnalyses: Map<string, ModContentAnalysis>,
        collectionAnalysis: ModCollectionAnalysis
    ): void {
        // Update each mod's conflict information based on collection analysis
        for (const [modFile, analysis] of modAnalyses) {
            this.updateModConflictInfo(modFile, analysis, collectionAnalysis);
        }
    }
    
    /**
     * Generates conflict summary statistics
     */
    private static generateConflictSummary(
        resourceConflicts: ResourceConflict[],
        knownConflicts: KnownConflict[]
    ): ConflictSummary {
        const summary: ConflictSummary = {
            byType: {
                exactDuplicates: 0,
                overrideConflicts: 0,
                hashCollisions: 0,
                dependencyMissing: 0,
                knownConflicts: knownConflicts.length
            },
            bySeverity: {
                low: 0,
                medium: 0,
                high: 0,
                critical: 0
            },
            autoFixableConflicts: 0,
            manualResolutionRequired: 0
        };
        
        // Count resource conflicts by type
        for (const conflict of resourceConflicts) {
            switch (conflict.conflictType) {
                case 'exact_duplicate':
                    summary.byType.exactDuplicates++;
                    break;
                case 'override_conflict':
                    summary.byType.overrideConflicts++;
                    break;
                case 'hash_collision':
                    summary.byType.hashCollisions++;
                    break;
                case 'dependency_missing':
                    summary.byType.dependencyMissing++;
                    break;
            }
            
            // Count by severity
            switch (conflict.severity) {
                case ConflictSeverity.LOW:
                    summary.bySeverity.low++;
                    break;
                case ConflictSeverity.MEDIUM:
                    summary.bySeverity.medium++;
                    break;
                case ConflictSeverity.HIGH:
                    summary.bySeverity.high++;
                    break;
                case ConflictSeverity.CRITICAL:
                    summary.bySeverity.critical++;
                    break;
            }
            
            // Count fixable vs manual
            if (conflict.autoFixAvailable) {
                summary.autoFixableConflicts++;
            } else {
                summary.manualResolutionRequired++;
            }
        }
        
        // Add known conflicts to severity counts
        for (const knownConflict of knownConflicts) {
            switch (knownConflict.severity) {
                case 'low':
                    summary.bySeverity.low++;
                    break;
                case 'medium':
                    summary.bySeverity.medium++;
                    break;
                case 'high':
                    summary.bySeverity.high++;
                    break;
                case 'critical':
                    summary.bySeverity.critical++;
                    break;
            }
            
            if (!knownConflict.autoFixAvailable) {
                summary.manualResolutionRequired++;
            }
        }
        
        return summary;
    }
    
    /**
     * Generates collection-level recommendations
     */
    private static generateRecommendations(
        modAnalyses: Map<string, ModContentAnalysis>,
        resourceConflicts: ResourceConflict[],
        knownConflicts: KnownConflict[]
    ): CollectionRecommendation[] {
        const recommendations: CollectionRecommendation[] = [];
        
        // Critical conflicts that need immediate attention
        const criticalConflicts = resourceConflicts.filter(c => c.severity === ConflictSeverity.CRITICAL);
        if (criticalConflicts.length > 0) {
            recommendations.push({
                type: RecommendationType.REMOVE_CONFLICTING,
                priority: 'critical',
                title: 'Critical Conflicts Detected',
                description: `${criticalConflicts.length} critical conflicts found that may cause game crashes`,
                affectedMods: [...new Set(criticalConflicts.flatMap(c => c.affectedMods))],
                actionRequired: true,
                autoFixAvailable: false
            });
        }
        
        // Missing dependencies
        const dependencyConflicts = resourceConflicts.filter(c => c.conflictType === 'dependency_missing');
        if (dependencyConflicts.length > 0) {
            recommendations.push({
                type: RecommendationType.INSTALL_DEPENDENCIES,
                priority: 'high',
                title: 'Missing Dependencies',
                description: `${dependencyConflicts.length} mods require missing dependencies`,
                affectedMods: [...new Set(dependencyConflicts.flatMap(c => c.affectedMods))],
                actionRequired: true,
                autoFixAvailable: false
            });
        }
        
        // Auto-fixable conflicts
        const autoFixableConflicts = resourceConflicts.filter(c => c.autoFixAvailable);
        if (autoFixableConflicts.length > 0) {
            recommendations.push({
                type: RecommendationType.REMOVE_CONFLICTING,
                priority: 'medium',
                title: 'Auto-Fixable Conflicts',
                description: `${autoFixableConflicts.length} conflicts can be automatically resolved`,
                affectedMods: [...new Set(autoFixableConflicts.flatMap(c => c.affectedMods))],
                actionRequired: false,
                autoFixAvailable: true
            });
        }
        
        // Performance optimization suggestions
        if (modAnalyses.size > 50) {
            recommendations.push({
                type: RecommendationType.PERFORMANCE_OPTIMIZATION,
                priority: 'low',
                title: 'Large Mod Collection',
                description: 'Consider organizing mods into subfolders for better performance',
                affectedMods: [],
                actionRequired: false,
                autoFixAvailable: true
            });
        }
        
        return recommendations;
    }
    
    /**
     * Generates organization suggestions
     */
    private static generateOrganizationSuggestions(
        modAnalyses: Map<string, ModContentAnalysis>,
        resourceConflicts: ResourceConflict[]
    ): OrganizationSuggestion[] {
        const suggestions: OrganizationSuggestion[] = [];
        
        // Suggest folder structure based on content types
        const folderStructure = this.suggestFolderStructure(modAnalyses);
        
        suggestions.push({
            type: 'folder_structure',
            description: 'Organize mods by content type for easier management',
            suggestedStructure: folderStructure,
            benefits: [
                'Easier to find specific types of mods',
                'Reduced chance of conflicts',
                'Better performance with organized structure',
                'Easier troubleshooting'
            ]
        });
        
        return suggestions;
    }
    
    /**
     * Suggests folder structure based on mod content
     */
    private static suggestFolderStructure(modAnalyses: Map<string, ModContentAnalysis>): FolderStructure[] {
        const structure: FolderStructure[] = [];
        
        const casMods: string[] = [];
        const objectMods: string[] = [];
        const gameplayMods: string[] = [];
        const scriptMods: string[] = [];
        
        for (const [modFile, analysis] of modAnalyses) {
            switch (analysis.contentType) {
                case 'cas_only':
                    casMods.push(modFile);
                    break;
                case 'objects_only':
                    objectMods.push(modFile);
                    break;
                case 'gameplay_only':
                    gameplayMods.push(modFile);
                    break;
                case 'script_mod':
                    scriptMods.push(modFile);
                    break;
                default:
                    // Mixed content - put in appropriate category based on primary content
                    if (analysis.casContent.totalItems > 0) {
                        casMods.push(modFile);
                    } else if (analysis.objectContent.totalItems > 0) {
                        objectMods.push(modFile);
                    } else {
                        gameplayMods.push(modFile);
                    }
            }
        }
        
        if (casMods.length > 0) {
            structure.push({
                folderName: 'CAS_Content',
                mods: casMods,
                reason: 'Clothing, hair, and accessories',
                priority: 1
            });
        }
        
        if (objectMods.length > 0) {
            structure.push({
                folderName: 'Build_Buy',
                mods: objectMods,
                reason: 'Furniture, decorations, and build items',
                priority: 2
            });
        }
        
        if (gameplayMods.length > 0) {
            structure.push({
                folderName: 'Gameplay',
                mods: gameplayMods,
                reason: 'Traits, interactions, and gameplay changes',
                priority: 3
            });
        }
        
        if (scriptMods.length > 0) {
            structure.push({
                folderName: 'Script_Mods',
                mods: scriptMods,
                reason: 'Python script mods (load first)',
                priority: 0
            });
        }
        
        return structure.sort((a, b) => a.priority - b.priority);
    }
    
    /**
     * Updates individual mod analysis with collection-level conflict info
     */
    private static updateModConflictInfo(
        modFile: string,
        analysis: ModContentAnalysis,
        collectionAnalysis: ModCollectionAnalysis
    ): void {
        // Find conflicts affecting this mod
        const affectingConflicts = collectionAnalysis.resourceConflicts.filter(
            conflict => conflict.affectedMods.includes(modFile)
        );
        
        // Update CAS content conflict summary
        if (analysis.casContent.totalItems > 0) {
            analysis.casContent.conflictSummary.totalConflicts = affectingConflicts.length;
            analysis.casContent.conflictSummary.brokenItems = affectingConflicts.filter(
                c => c.severity === ConflictSeverity.CRITICAL
            ).length;
        }
        
        // Update individual CAS items with specific conflict info
        for (const casItem of analysis.casContent.items) {
            const itemConflicts = affectingConflicts.filter(conflict => 
                // This would need more sophisticated matching based on resource IDs
                conflict.description.includes('CAS') || conflict.conflictType === 'exact_duplicate'
            );
            
            if (itemConflicts.length > 0) {
                casItem.conflicts.hasConflicts = true;
                casItem.conflicts.conflictType = itemConflicts.map(c => c.conflictType as any);
                casItem.conflicts.conflictingFiles = itemConflicts.flatMap(c => c.affectedMods);
                casItem.conflicts.conflictSeverity = itemConflicts.reduce((max, c) => 
                    c.severity === ConflictSeverity.CRITICAL ? ConflictSeverity.CRITICAL :
                    c.severity === ConflictSeverity.HIGH ? ConflictSeverity.HIGH :
                    max, ConflictSeverity.LOW
                );
            }
        }
    }
}
