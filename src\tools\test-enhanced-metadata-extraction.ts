/**
 * Test script for Enhanced Metadata Extraction System
 * Phase 2: Complete multi-source metadata extraction testing
 */

import * as fs from 'fs';
import * as path from 'path';
import { EnhancedMetadataExtractionService } from '../services/analysis/metadata/EnhancedMetadataExtractionService';

interface TestResult {
    fileName: string;
    filePath: string;
    fileSize: number;
    
    // Extraction Results
    overallConfidence: number;
    sourceCount: number;
    hasConflicts: boolean;
    qualityScore: number;
    primarySource: string;
    
    // Metadata Fields
    author?: string;
    modName?: string;
    version?: string;
    description?: string;
    downloadUrl?: string;
    requirements?: string[];
    
    // Source Breakdown
    sourceTypes: string[];
    extractionDetails: {
        filename: { attempted: boolean; successful: boolean; error?: string };
        stringTable: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        tuning: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        simData: { attempted: boolean; successful: boolean; resourceCount: number; error?: string };
        manifest: { attempted: boolean; successful: boolean; manifestCount: number; error?: string };
    };
    
    // Performance
    totalProcessingTime: number;
    aggregationTime: number;
    extractorTimes: Record<string, number>;
    
    error?: string;
}

async function testEnhancedMetadataExtraction(): Promise<void> {
    console.log('🧪 Testing Enhanced Metadata Extraction System');
    console.log('=' .repeat(70));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    if (!fs.existsSync(modsPath)) {
        console.log('❌ Mods folder not found. Please check the path.');
        return;
    }
    
    const results: TestResult[] = [];
    const files = fs.readdirSync(modsPath)
        .filter(file => file.endsWith('.package'))
        .slice(0, 15); // Test first 15 package files
    
    console.log(`📁 Found ${files.length} package files to test`);
    console.log('');
    
    const metadataService = new EnhancedMetadataExtractionService();
    
    for (const fileName of files) {
        const filePath = path.join(modsPath, fileName);
        const startTime = performance.now();
        
        try {
            console.log(`🔍 Analyzing: ${fileName}`);
            
            const buffer = fs.readFileSync(filePath);
            const fileSize = buffer.length;
            
            // Perform enhanced metadata extraction
            const extractionResult = await metadataService.extractMetadata(
                buffer,
                fileName,
                {
                    enabledExtractors: {
                        filename: true,
                        stringTable: true,
                        tuning: true,
                        simData: true,
                        manifest: true
                    }
                }
            );
            
            const totalTime = performance.now() - startTime;
            
            const result: TestResult = {
                fileName,
                filePath,
                fileSize,
                overallConfidence: extractionResult.metadata.overallConfidence,
                sourceCount: extractionResult.metadata.sourceCount,
                hasConflicts: extractionResult.metadata.hasConflicts,
                qualityScore: extractionResult.metadata.qualityScore,
                primarySource: extractionResult.metadata.primarySource,
                author: extractionResult.metadata.author,
                modName: extractionResult.metadata.modName,
                version: extractionResult.metadata.version,
                description: extractionResult.metadata.description,
                downloadUrl: extractionResult.metadata.downloadUrl,
                requirements: extractionResult.metadata.requirements,
                sourceTypes: extractionResult.metadata.sourceTypes,
                extractionDetails: extractionResult.extractionDetails,
                totalProcessingTime: totalTime,
                aggregationTime: extractionResult.performance.aggregationTime,
                extractorTimes: extractionResult.performance.extractorTimes
            };
            
            results.push(result);
            
            // Log results
            console.log(`  ✅ Extraction completed!`);
            console.log(`     📊 Overall Confidence: ${result.overallConfidence}%`);
            console.log(`     📈 Quality Score: ${result.qualityScore}%`);
            console.log(`     🔗 Sources: ${result.sourceCount} (${result.sourceTypes.join(', ')})`);
            console.log(`     🎯 Primary Source: ${result.primarySource}`);
            
            if (result.author) {
                console.log(`     👤 Author: "${result.author}"`);
            }
            if (result.modName) {
                console.log(`     📝 Mod Name: "${result.modName}"`);
            }
            if (result.version) {
                console.log(`     🔢 Version: "${result.version}"`);
            }
            if (result.description) {
                console.log(`     📄 Description: "${result.description.substring(0, 80)}${result.description.length > 80 ? '...' : ''}"`);
            }
            
            if (result.hasConflicts) {
                console.log(`     ⚠️  Has conflicts between sources`);
            }
            
            console.log(`     ⚡ Processing Time: ${result.totalProcessingTime.toFixed(2)}ms`);
            console.log(`        - Filename: ${result.extractorTimes.filename?.toFixed(2) || 0}ms`);
            console.log(`        - StringTable: ${result.extractorTimes.stringTable?.toFixed(2) || 0}ms`);
            console.log(`        - Tuning: ${result.extractorTimes.tuning?.toFixed(2) || 0}ms`);
            console.log(`        - SimData: ${result.extractorTimes.simData?.toFixed(2) || 0}ms`);
            console.log(`        - Manifest: ${result.extractorTimes.manifest?.toFixed(2) || 0}ms`);
            console.log(`        - Aggregation: ${result.aggregationTime.toFixed(2)}ms`);
            console.log('');
            
        } catch (error) {
            const totalTime = performance.now() - startTime;
            console.log(`  ❌ Error: ${error.message}`);
            console.log(`     ⏱️  Time: ${totalTime.toFixed(2)}ms`);
            console.log('');
            
            results.push({
                fileName,
                filePath,
                fileSize: 0,
                overallConfidence: 0,
                sourceCount: 0,
                hasConflicts: false,
                qualityScore: 0,
                primarySource: 'none',
                sourceTypes: [],
                extractionDetails: {
                    filename: { attempted: false, successful: false },
                    stringTable: { attempted: false, successful: false, resourceCount: 0 },
                    tuning: { attempted: false, successful: false, resourceCount: 0 },
                    simData: { attempted: false, successful: false, resourceCount: 0 },
                    manifest: { attempted: false, successful: false, manifestCount: 0 }
                },
                totalProcessingTime: totalTime,
                aggregationTime: 0,
                extractorTimes: {},
                error: error.message
            });
        }
    }
    
    // Summary Report
    console.log('📊 Enhanced Metadata Extraction Summary');
    console.log('=' .repeat(70));
    
    const successful = results.filter(r => !r.error);
    const withAuthor = results.filter(r => r.author);
    const withModName = results.filter(r => r.modName);
    const withVersion = results.filter(r => r.version);
    const withDescription = results.filter(r => r.description);
    const withManifest = results.filter(r => r.extractionDetails.manifest.successful);
    const withConflicts = results.filter(r => r.hasConflicts);
    const errors = results.filter(r => r.error);
    
    console.log(`📁 Total Files Analyzed: ${results.length}`);
    console.log(`✅ Successful Extractions: ${successful.length} (${(successful.length / results.length * 100).toFixed(1)}%)`);
    console.log(`👤 Files with Author: ${withAuthor.length} (${(withAuthor.length / results.length * 100).toFixed(1)}%)`);
    console.log(`📝 Files with Mod Name: ${withModName.length} (${(withModName.length / results.length * 100).toFixed(1)}%)`);
    console.log(`🔢 Files with Version: ${withVersion.length} (${(withVersion.length / results.length * 100).toFixed(1)}%)`);
    console.log(`📄 Files with Description: ${withDescription.length} (${(withDescription.length / results.length * 100).toFixed(1)}%)`);
    console.log(`📋 Files with Manifest: ${withManifest.length} (${(withManifest.length / results.length * 100).toFixed(1)}%)`);
    console.log(`⚠️  Files with Conflicts: ${withConflicts.length} (${(withConflicts.length / results.length * 100).toFixed(1)}%)`);
    console.log(`❌ Extraction Errors: ${errors.length} (${(errors.length / results.length * 100).toFixed(1)}%)`);
    console.log('');
    
    // Performance Metrics
    if (successful.length > 0) {
        const avgConfidence = successful.reduce((sum, r) => sum + r.overallConfidence, 0) / successful.length;
        const avgQuality = successful.reduce((sum, r) => sum + r.qualityScore, 0) / successful.length;
        const avgSources = successful.reduce((sum, r) => sum + r.sourceCount, 0) / successful.length;
        const avgTime = successful.reduce((sum, r) => sum + r.totalProcessingTime, 0) / successful.length;
        
        console.log('📈 Quality Metrics:');
        console.log(`   Average Confidence: ${avgConfidence.toFixed(1)}%`);
        console.log(`   Average Quality Score: ${avgQuality.toFixed(1)}%`);
        console.log(`   Average Sources per File: ${avgSources.toFixed(1)}`);
        console.log(`   Average Processing Time: ${avgTime.toFixed(2)}ms`);
        console.log('');
    }
    
    // Source Type Analysis
    const sourceTypeStats = new Map<string, number>();
    successful.forEach(result => {
        result.sourceTypes.forEach(sourceType => {
            sourceTypeStats.set(sourceType, (sourceTypeStats.get(sourceType) || 0) + 1);
        });
    });
    
    console.log('🔗 Source Type Statistics:');
    for (const [sourceType, count] of sourceTypeStats.entries()) {
        const percentage = (count / successful.length * 100).toFixed(1);
        console.log(`   ${sourceType}: ${count} files (${percentage}%)`);
    }
    console.log('');
    
    // Best Results
    const bestResults = successful
        .filter(r => r.overallConfidence > 70)
        .sort((a, b) => b.qualityScore - a.qualityScore)
        .slice(0, 5);
    
    if (bestResults.length > 0) {
        console.log('🏆 Top Quality Results:');
        console.log('-'.repeat(70));
        
        bestResults.forEach((result, index) => {
            console.log(`${index + 1}. ${result.fileName}`);
            console.log(`   Quality: ${result.qualityScore}% | Confidence: ${result.overallConfidence}%`);
            console.log(`   Sources: ${result.sourceTypes.join(', ')}`);
            if (result.author) console.log(`   Author: "${result.author}"`);
            if (result.modName) console.log(`   Mod: "${result.modName}"`);
            if (result.version) console.log(`   Version: "${result.version}"`);
            console.log('');
        });
    }
    
    console.log('✅ Enhanced Metadata Extraction Test Complete!');
}

// Run the test
if (require.main === module) {
    testEnhancedMetadataExtraction().catch(console.error);
}

export { testEnhancedMetadataExtraction };
