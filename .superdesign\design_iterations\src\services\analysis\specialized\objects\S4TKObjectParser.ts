import type { ResourceEntry } from '@s4tk/models/types';
import { SimDataResource, ObjectDefinitionResource } from '@s4tk/models';
import { ObjectInfo, ObjectCategory, ObjectFunction, ObjectStyle, RoomType } from './ObjectTypes';

/**
 * Specialized parser for S4TK-based object analysis
 * Handles ObjectDefinitionResource and SimDataResource parsing
 */
export class S4TKObjectParser {

    /**
     * Extracts object information from ObjectDefinitionResource (proper S4TK way)
     */
    public static extractFromObjectDefinition(objectDef: ObjectDefinitionResource, objectInfo: ObjectInfo): void {
        const props = objectDef.properties;

        // Extract price from SimoleonPrice property
        if (props.simoleonPrice !== undefined) {
            objectInfo.price = props.simoleonPrice;
        }

        // Extract tuning information
        if (props.tuning) {
            // The tuning string often contains category information
            this.extractCategoryFromTuning(props.tuning, objectInfo);
        }

        // Extract name information
        if (props.name) {
            // Use name to help determine category
            this.extractCategoryFromName(props.name, objectInfo);
        }

        // Extract components information
        if (props.components && props.components.length > 0) {
            // Components can indicate object functionality
            this.extractFunctionalityFromComponents(props.components, objectInfo);
        }

        // Extract material variant for style detection
        if (props.materialVariant) {
            this.extractStyleFromMaterialVariant(props.materialVariant, objectInfo);
        }

        // Set functional vs decorative based on available data
        objectInfo.isFunctional = objectInfo.price > 0 || (props.components && props.components.length > 0);
        objectInfo.isDecor = !objectInfo.isFunctional;

        // Generate description based on extracted data
        objectInfo.description = this.generateObjectDescription(objectInfo);
    }

    /**
     * Extracts object information from SimData structure
     */
    public static extractFromSimData(simData: SimDataResource, objectInfo: ObjectInfo): void {
        const instance = simData.instance;

        // Look for common object fields in SimData
        if (instance.has('Price')) {
            objectInfo.price = Number(instance.get('Price')) || 0;
        }

        if (instance.has('Function')) {
            const functionValue = instance.get('Function');
            this.mapObjectFunction(functionValue, objectInfo);
        }

        if (instance.has('RoomFlags')) {
            const roomFlags = instance.get('RoomFlags');
            this.mapRoomFlags(roomFlags, objectInfo);
        }

        if (instance.has('Category')) {
            const category = instance.get('Category');
            this.mapObjectCategoryFromValue(category, objectInfo);
        }

        if (instance.has('Subcategory')) {
            const subcategory = instance.get('Subcategory');
            this.mapObjectSubcategoryFromValue(subcategory, objectInfo);
        }

        if (instance.has('Tags')) {
            const tags = instance.get('Tags');
            if (Array.isArray(tags)) {
                objectInfo.tags = tags.map(tag => String(tag));
            }
        }

        // Determine if object is decorative or functional
        if (instance.has('IsDecorative')) {
            objectInfo.isDecor = Boolean(instance.get('IsDecorative'));
            objectInfo.isFunctional = !objectInfo.isDecor;
        }

        // Generate description based on extracted data
        objectInfo.description = this.generateObjectDescription(objectInfo);
    }

    /**
     * Attempts to parse resource as ObjectDefinitionResource
     */
    public static tryParseObjectDefinition(resource: ResourceEntry): ObjectDefinitionResource | null {
        try {
            return ObjectDefinitionResource.from(resource.value);
        } catch (error) {
            // Handle the specific S4TK parsing error gracefully
            if (error instanceof TypeError && error.message.includes('this._buffer[methodName] is not a function')) {
                console.warn('S4TK ObjectDefinitionResource parsing failed with buffer method error');
            } else {
                console.warn('Error parsing object definition:', error);
            }
            return null;
        }
    }

    /**
     * Attempts to parse resource as SimDataResource
     */
    public static tryParseSimData(resource: ResourceEntry): SimDataResource | null {
        try {
            return SimDataResource.from(resource.value);
        } catch (error) {
            console.warn('Error parsing SimData:', error);
            return null;
        }
    }

    /**
     * Extracts category information from tuning string
     */
    private static extractCategoryFromTuning(tuning: string, objectInfo: ObjectInfo): void {
        const lowerTuning = tuning.toLowerCase();

        // Seating detection
        if (lowerTuning.includes('chair') || lowerTuning.includes('seating') || lowerTuning.includes('sofa')) {
            objectInfo.category = ObjectCategory.SEATING;
            objectInfo.function = ObjectFunction.SEATING;
            objectInfo.subcategory = lowerTuning.includes('chair') ? 'chairs' :
                lowerTuning.includes('sofa') ? 'sofas' : 'seating';
        }
        // Storage detection
        else if (lowerTuning.includes('storage') || lowerTuning.includes('shelf') || lowerTuning.includes('cabinet')) {
            objectInfo.category = ObjectCategory.STORAGE;
            objectInfo.function = ObjectFunction.STORAGE;
            objectInfo.subcategory = lowerTuning.includes('shelf') ? 'shelving' :
                lowerTuning.includes('cabinet') ? 'cabinets' : 'storage';
        }
        // Surface detection
        else if (lowerTuning.includes('table') || lowerTuning.includes('desk') || lowerTuning.includes('counter')) {
            objectInfo.category = ObjectCategory.SURFACES;
            objectInfo.subcategory = lowerTuning.includes('desk') ? 'desks' :
                lowerTuning.includes('counter') ? 'counters' : 'tables';
        }
        // Lighting detection
        else if (lowerTuning.includes('light') || lowerTuning.includes('lamp')) {
            objectInfo.category = ObjectCategory.LIGHTING;
            objectInfo.function = ObjectFunction.LIGHTING;
            objectInfo.subcategory = 'lighting';
        }
        // Appliance detection
        else if (lowerTuning.includes('appliance') || lowerTuning.includes('fridge') || lowerTuning.includes('stove')) {
            objectInfo.category = ObjectCategory.APPLIANCES;
            objectInfo.subcategory = 'appliances';
        }
        // Decorative detection
        else if (lowerTuning.includes('decor') || lowerTuning.includes('decoration') || lowerTuning.includes('plant')) {
            objectInfo.category = ObjectCategory.DECORATIVE;
            objectInfo.function = ObjectFunction.DECORATION;
            objectInfo.subcategory = lowerTuning.includes('plant') ? 'plants' : 'decorative';
        }
    }

    /**
     * Extracts category information from object name
     */
    private static extractCategoryFromName(name: string, objectInfo: ObjectInfo): void {
        // Only use name if category is still unknown
        if (objectInfo.category !== ObjectCategory.UNKNOWN) return;

        const lowerName = name.toLowerCase();

        if (lowerName.includes('chair') || lowerName.includes('sofa') || lowerName.includes('bench')) {
            objectInfo.category = ObjectCategory.SEATING;
            objectInfo.function = ObjectFunction.SEATING;
        } else if (lowerName.includes('table') || lowerName.includes('desk')) {
            objectInfo.category = ObjectCategory.SURFACES;
        } else if (lowerName.includes('shelf') || lowerName.includes('storage') || lowerName.includes('cabinet')) {
            objectInfo.category = ObjectCategory.STORAGE;
            objectInfo.function = ObjectFunction.STORAGE;
        } else if (lowerName.includes('light') || lowerName.includes('lamp')) {
            objectInfo.category = ObjectCategory.LIGHTING;
            objectInfo.function = ObjectFunction.LIGHTING;
        }
    }

    /**
     * Extracts functionality from components array
     */
    private static extractFunctionalityFromComponents(components: number[], objectInfo: ObjectInfo): void {
        // Component analysis could be more sophisticated
        // For now, having components indicates functionality
        if (components.length > 0) {
            objectInfo.isFunctional = true;
            objectInfo.isDecor = false;
        }
    }

    /**
     * Extracts style information from material variant
     */
    private static extractStyleFromMaterialVariant(materialVariant: string, objectInfo: ObjectInfo): void {
        const lowerVariant = materialVariant.toLowerCase();

        if (lowerVariant.includes('modern')) {
            objectInfo.style.push(ObjectStyle.MODERN);
        } else if (lowerVariant.includes('traditional')) {
            objectInfo.style.push(ObjectStyle.TRADITIONAL);
        } else if (lowerVariant.includes('rustic')) {
            objectInfo.style.push(ObjectStyle.RUSTIC);
        } else if (lowerVariant.includes('industrial')) {
            objectInfo.style.push(ObjectStyle.INDUSTRIAL);
        } else if (lowerVariant.includes('luxury')) {
            objectInfo.style.push(ObjectStyle.LUXURY);
        } else if (lowerVariant.includes('vintage')) {
            objectInfo.style.push(ObjectStyle.VINTAGE);
        } else {
            objectInfo.style.push(ObjectStyle.CASUAL);
        }
    }

    /**
     * Maps object function values to our function system
     */
    private static mapObjectFunction(functionValue: any, objectInfo: ObjectInfo): void {
        const funcValue = Number(functionValue);

        switch (funcValue) {
            case 1:
                objectInfo.function = ObjectFunction.SEATING;
                objectInfo.category = ObjectCategory.SEATING;
                objectInfo.isFunctional = true;
                break;
            case 2:
                objectInfo.function = ObjectFunction.SLEEPING;
                objectInfo.category = ObjectCategory.SEATING; // Beds are often categorized as seating
                objectInfo.isFunctional = true;
                break;
            case 3:
                objectInfo.function = ObjectFunction.STORAGE;
                objectInfo.category = ObjectCategory.STORAGE;
                objectInfo.isFunctional = true;
                break;
            case 4:
                objectInfo.function = ObjectFunction.LIGHTING;
                objectInfo.category = ObjectCategory.LIGHTING;
                objectInfo.isFunctional = true;
                break;
            case 5:
                objectInfo.function = ObjectFunction.COOKING;
                objectInfo.category = ObjectCategory.APPLIANCES;
                objectInfo.isFunctional = true;
                break;
            case 6:
                objectInfo.function = ObjectFunction.CLEANING;
                objectInfo.category = ObjectCategory.APPLIANCES;
                objectInfo.isFunctional = true;
                break;
            case 7:
                objectInfo.function = ObjectFunction.ENTERTAINMENT;
                objectInfo.category = ObjectCategory.ELECTRONICS;
                objectInfo.isFunctional = true;
                break;
            case 8:
                objectInfo.function = ObjectFunction.DECORATION;
                objectInfo.category = ObjectCategory.DECORATIVE;
                objectInfo.isDecor = true;
                break;
            default:
                objectInfo.function = ObjectFunction.UNKNOWN;
                break;
        }
    }

    /**
     * Maps room flags to room assignments
     */
    private static mapRoomFlags(roomFlags: any, objectInfo: ObjectInfo): void {
        const roomValue = Number(roomFlags);

        // Room assignment bitwise flags
        objectInfo.roomAssignment = [];

        if (roomValue & 0x0001) objectInfo.roomAssignment.push(RoomType.LIVING_ROOM);
        if (roomValue & 0x0002) objectInfo.roomAssignment.push(RoomType.KITCHEN);
        if (roomValue & 0x0004) objectInfo.roomAssignment.push(RoomType.BATHROOM);
        if (roomValue & 0x0008) objectInfo.roomAssignment.push(RoomType.BEDROOM);
        if (roomValue & 0x0010) objectInfo.roomAssignment.push(RoomType.DINING_ROOM);
        if (roomValue & 0x0020) objectInfo.roomAssignment.push(RoomType.OFFICE);
        if (roomValue & 0x0040) objectInfo.roomAssignment.push(RoomType.KIDS_ROOM);
        if (roomValue & 0x0080) objectInfo.roomAssignment.push(RoomType.OUTDOOR);

        // Default to any room if no specific assignment
        if (objectInfo.roomAssignment.length === 0) {
            objectInfo.roomAssignment.push(RoomType.ANY_ROOM);
        }
    }

    /**
     * Maps object category values from SimData
     */
    private static mapObjectCategoryFromValue(categoryValue: any, objectInfo: ObjectInfo): void {
        const catValue = Number(categoryValue);

        switch (catValue) {
            case 1:
                objectInfo.category = ObjectCategory.SEATING;
                break;
            case 2:
                objectInfo.category = ObjectCategory.SURFACES;
                break;
            case 3:
                objectInfo.category = ObjectCategory.STORAGE;
                break;
            case 4:
                objectInfo.category = ObjectCategory.LIGHTING;
                break;
            case 5:
                objectInfo.category = ObjectCategory.DECORATIVE;
                break;
            case 6:
                objectInfo.category = ObjectCategory.ELECTRONICS;
                break;
            case 7:
                objectInfo.category = ObjectCategory.APPLIANCES;
                break;
            case 8:
                objectInfo.category = ObjectCategory.PLUMBING;
                break;
            case 9:
                objectInfo.category = ObjectCategory.PLANTS;
                break;
            default:
                objectInfo.category = ObjectCategory.MISC;
                break;
        }
    }

    /**
     * Maps object subcategory values from SimData
     */
    private static mapObjectSubcategoryFromValue(subcategoryValue: any, objectInfo: ObjectInfo): void {
        const subCatValue = Number(subcategoryValue);

        // Map based on category and subcategory value
        switch (objectInfo.category) {
            case ObjectCategory.SEATING:
                if (subCatValue === 1) objectInfo.subcategory = 'chairs';
                else if (subCatValue === 2) objectInfo.subcategory = 'sofas';
                else if (subCatValue === 3) objectInfo.subcategory = 'benches';
                else objectInfo.subcategory = 'seating';
                break;

            case ObjectCategory.SURFACES:
                if (subCatValue === 1) objectInfo.subcategory = 'tables';
                else if (subCatValue === 2) objectInfo.subcategory = 'desks';
                else if (subCatValue === 3) objectInfo.subcategory = 'counters';
                else objectInfo.subcategory = 'surfaces';
                break;

            case ObjectCategory.STORAGE:
                if (subCatValue === 1) objectInfo.subcategory = 'dressers';
                else if (subCatValue === 2) objectInfo.subcategory = 'shelving';
                else if (subCatValue === 3) objectInfo.subcategory = 'cabinets';
                else objectInfo.subcategory = 'storage';
                break;

            case ObjectCategory.LIGHTING:
                if (subCatValue === 1) objectInfo.subcategory = 'ceiling_lights';
                else if (subCatValue === 2) objectInfo.subcategory = 'table_lamps';
                else if (subCatValue === 3) objectInfo.subcategory = 'floor_lamps';
                else objectInfo.subcategory = 'lighting';
                break;

            default:
                objectInfo.subcategory = objectInfo.category.toLowerCase();
                break;
        }
    }

    /**
     * Generates object description based on extracted data
     */
    private static generateObjectDescription(objectInfo: ObjectInfo): string {
        const parts: string[] = [];

        // Add style if available
        if (objectInfo.style.length > 0 && objectInfo.style[0] !== ObjectStyle.UNKNOWN) {
            parts.push(objectInfo.style[0]);
        }

        // Add category
        parts.push(objectInfo.category);

        // Add function if different from category
        if (objectInfo.function !== ObjectFunction.UNKNOWN && 
            objectInfo.function.toLowerCase() !== objectInfo.category.toLowerCase()) {
            parts.push(`for ${objectInfo.function}`);
        }

        // Add room assignment if specific
        if (objectInfo.roomAssignment.length === 1 && objectInfo.roomAssignment[0] !== RoomType.ANY_ROOM) {
            parts.push(`(${objectInfo.roomAssignment[0].replace('_', ' ')})`);
        }

        return parts.join(' ').replace(/^\w/, c => c.toUpperCase());
    }
}