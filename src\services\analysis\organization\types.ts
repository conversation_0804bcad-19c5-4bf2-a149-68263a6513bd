import { ModCategory } from '../../../types/analysis';

/**
 * Organization preferences for mod folder structure
 */
export interface OrganizationPreferences {
    useSubcategories: boolean;
    separateScriptMods: boolean;
    separateOverrides: boolean;
    groupByAuthor: boolean;
    customFolderNames: Record<string, string>;
}

/**
 * Category data for organization statistics
 */
export interface CategoryData {
    count: number;
    files: any[];
    suggestedFolder: string;
}

/**
 * Organization suggestion for a group of files
 */
export interface OrganizationSuggestion {
    targetPath: string;
    files: string[];
    fileCount: number;
    category: ModCategory;
    description: string;
}

/**
 * Organization conflict information
 */
export interface OrganizationConflict {
    type: 'dependency_separation' | 'folder_conflicts';
    description: string;
    affectedFiles: string[];
    severity: 'warning' | 'error';
}

/**
 * Organization plan for batch mod organization
 */
export interface OrganizationPlan {
    totalFiles: number;
    categories: Map<ModCategory, CategoryData>;
    conflicts: OrganizationConflict[];
    suggestions: OrganizationSuggestion[];
}

/**
 * Default organization preferences
 */
export const DEFAULT_PREFERENCES: OrganizationPreferences = {
    useSubcategories: true,
    separateScriptMods: true,
    separateOverrides: true,
    groupByAuthor: false,
    customFolderNames: {
        [ModCategory.CAS_CC]: 'Custom Content/CAS',
        [ModCategory.BUILD_BUY_CC]: 'Custom Content/Build-Buy',
        [ModCategory.SCRIPT_MOD]: 'Script Mods',
        [ModCategory.TUNING_MOD]: 'Tuning Mods',
        [ModCategory.OVERRIDE]: 'Overrides',
        [ModCategory.FRAMEWORK]: 'Framework',
        [ModCategory.LIBRARY]: 'Libraries',
        [ModCategory.UNKNOWN]: 'Uncategorized'
    }
};