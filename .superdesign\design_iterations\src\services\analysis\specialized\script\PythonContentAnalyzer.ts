/**
 * Python Content Analyzer
 * 
 * Specialized component for analyzing Python file content in script mods.
 * Handles dependency detection, code analysis, and content classification.
 * 
 * Extracted from ScriptAnalyzer.ts as part of Phase 2 refactoring.
 */

import { PYTHON_IMPORT_REGEX, type ScriptModInfo } from './types';
import { MetadataExtractor } from './MetadataExtractor';
import { GameplayAreaDetector } from './GameplayAreaDetector';

/**
 * Handles analysis of Python file content
 */
export class PythonContentAnalyzer {
    
    /**
     * Analyzes Python file content to extract script information
     * 
     * @param content - Python file content as string
     * @param scriptInfo - Script info object to populate
     */
    public static analyzePythonContent(content: string, scriptInfo: ScriptModInfo): void {
        try {
            // Extract dependencies from import statements
            this.extractDependencies(content, scriptInfo);
            
            // Extract metadata (author, version, etc.)
            MetadataExtractor.extractFromPythonContent(content, scriptInfo);
            
            // Detect gameplay areas from code content
            GameplayAreaDetector.detectFromContent(content, scriptInfo);
            
            // Analyze code complexity and patterns
            this.analyzeCodePatterns(content, scriptInfo);
            
        } catch (error) {
            console.warn('Error analyzing Python content:', error);
        }
    }
    
    /**
     * Extracts dependencies from Python import statements
     * 
     * @param content - Python file content
     * @param scriptInfo - Script info object to populate
     */
    private static extractDependencies(content: string, scriptInfo: ScriptModInfo): void {
        try {
            const importMatches = content.match(PYTHON_IMPORT_REGEX);
            
            if (!importMatches) {
                return;
            }
            
            const imports = importMatches
                .map(match => this.parseImportStatement(match))
                .filter(imp => imp !== null && this.isValidDependency(imp))
                .map(imp => imp as string);
            
            // Add unique imports to dependencies
            imports.forEach(imp => {
                if (!scriptInfo.dependencies.includes(imp)) {
                    scriptInfo.dependencies.push(imp);
                }
            });
            
        } catch (error) {
            console.warn('Error extracting dependencies:', error);
        }
    }
    
    /**
     * Parses an import statement to extract the module name
     * 
     * @param importStatement - Raw import statement
     * @returns Module name or null if parsing fails
     */
    private static parseImportStatement(importStatement: string): string | null {
        try {
            // Remove 'import' or 'from' and get the first part
            const cleaned = importStatement
                .replace(/^(?:import|from)\s+/, '')
                .split('.')[0]
                .split(' ')[0]
                .trim();
            
            return cleaned || null;
            
        } catch (error) {
            return null;
        }
    }
    
    /**
     * Validates if a module name is a valid dependency
     * 
     * @param moduleName - Module name to validate
     * @returns true if module appears to be a valid dependency
     */
    private static isValidDependency(moduleName: string): boolean {
        // Filter out built-in Python modules and common standard library modules
        const builtinModules = [
            'os', 'sys', 'time', 'datetime', 'json', 'xml', 'math', 'random',
            'collections', 'itertools', 'functools', 're', 'string', 'io',
            'pathlib', 'typing', 'enum', 'abc', 'copy', 'pickle'
        ];
        
        return moduleName.length > 0 && 
               !builtinModules.includes(moduleName.toLowerCase()) &&
               /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(moduleName);
    }
    
    /**
     * Analyzes code patterns to determine script characteristics
     * 
     * @param content - Python file content
     * @param scriptInfo - Script info object to populate
     */
    private static analyzeCodePatterns(content: string, scriptInfo: ScriptModInfo): void {
        const patterns = {
            hasClasses: /class\s+\w+/g,
            hasFunctions: /def\s+\w+/g,
            hasDecorators: /@\w+/g,
            hasEventHandlers: /@event_handler|@sims4\.commands/g,
            hasUIElements: /ui\.|dialog\.|notification\./gi,
            hasGameplayHooks: /inject_to|_inject|override/gi,
            hasDataStructures: /dict\(|list\(|\{.*\}|\[.*\]/g
        };
        
        const analysis = {
            classCount: (content.match(patterns.hasClasses) || []).length,
            functionCount: (content.match(patterns.hasFunctions) || []).length,
            decoratorCount: (content.match(patterns.hasDecorators) || []).length,
            hasEventHandlers: patterns.hasEventHandlers.test(content),
            hasUIElements: patterns.hasUIElements.test(content),
            hasGameplayHooks: patterns.hasGameplayHooks.test(content),
            hasDataStructures: patterns.hasDataStructures.test(content),
            lineCount: content.split('\n').length
        };
        
        // Store analysis results (could be used for complexity estimation)
        this.updateScriptInfoFromAnalysis(analysis, scriptInfo);
    }
    
    /**
     * Updates script info based on code analysis
     * 
     * @param analysis - Code analysis results
     * @param scriptInfo - Script info to update
     */
    private static updateScriptInfoFromAnalysis(analysis: any, scriptInfo: ScriptModInfo): void {
        // Add potential conflicts based on code patterns
        if (analysis.hasEventHandlers) {
            this.addPotentialConflict(scriptInfo, 'May conflict with other mods that use event handlers');
        }
        
        if (analysis.hasGameplayHooks) {
            this.addPotentialConflict(scriptInfo, 'May conflict with other mods that modify game behavior');
        }
        
        if (analysis.hasUIElements) {
            this.addPotentialConflict(scriptInfo, 'May conflict with other UI modification mods');
        }
    }
    
    /**
     * Adds a potential conflict to the script info
     * 
     * @param scriptInfo - Script info to update
     * @param conflict - Conflict description
     */
    private static addPotentialConflict(scriptInfo: ScriptModInfo, conflict: string): void {
        if (!scriptInfo.conflicts.includes(conflict)) {
            scriptInfo.conflicts.push(conflict);
        }
    }
    
    /**
     * Estimates code complexity based on various metrics
     * 
     * @param content - Python file content
     * @returns Complexity estimate ('low', 'medium', 'high')
     */
    public static estimateComplexity(content: string): string {
        const metrics = {
            lineCount: content.split('\n').length,
            classCount: (content.match(/class\s+\w+/g) || []).length,
            functionCount: (content.match(/def\s+\w+/g) || []).length,
            importCount: (content.match(PYTHON_IMPORT_REGEX) || []).length,
            complexityIndicators: (content.match(/if|for|while|try|except|with/g) || []).length
        };
        
        // Simple scoring system
        let score = 0;
        score += Math.min(metrics.lineCount / 50, 10); // Max 10 points for lines
        score += metrics.classCount * 2; // 2 points per class
        score += metrics.functionCount * 1; // 1 point per function
        score += metrics.importCount * 0.5; // 0.5 points per import
        score += metrics.complexityIndicators * 0.5; // 0.5 points per complexity indicator
        
        if (score < 10) return 'low';
        if (score < 25) return 'medium';
        return 'high';
    }
    
    /**
     * Detects if Python content contains specific framework signatures
     * 
     * @param content - Python file content
     * @returns Array of detected framework signatures
     */
    public static detectFrameworkSignatures(content: string): string[] {
        const signatures = [
            { name: 'MC Command Center', patterns: ['mc_command_center', 'mccc'] },
            { name: 'XML Injector', patterns: ['xml_injector', 'xmlinjector'] },
            { name: 'UI Cheats Extension', patterns: ['ui_cheats', 'uicheats'] },
            { name: 'Basemental', patterns: ['basemental'] },
            { name: 'WickedWhims', patterns: ['wickedwhims', 'ww_'] }
        ];
        
        const detected: string[] = [];
        
        signatures.forEach(sig => {
            if (sig.patterns.some(pattern => content.toLowerCase().includes(pattern))) {
                detected.push(sig.name);
            }
        });
        
        return detected;
    }
}
