/**
 * CAS SimData Parser
 * 
 * Specialized component for parsing SimData resources to extract CAS information.
 * Handles the complex logic of reading SimData fields and delegating to appropriate mappers.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring.
 */

import { SimDataResource } from '@s4tk/models';
import type { CASPartInfo } from './types';
import { PartTypeMapper } from './PartTypeMapper';
import { AgeGenderMapper } from './AgeGenderMapper';
import { BodyLocationMapper } from './BodyLocationMapper';
import { ClothingCategoryMapper } from './ClothingCategoryMapper';
import { DescriptionGenerator } from './DescriptionGenerator';

/**
 * Handles parsing of SimData resources for CAS information
 */
export class SimDataParser {
    
    /**
     * Extracts CAS information from SimData structure
     * 
     * @param simData - The parsed SimData resource
     * @param casPartInfo - CAS part info object to populate
     */
    public static extractFromSimData(simData: SimDataResource, casPartInfo: CASPartInfo): void {
        if (!simData || !simData.instance || !simData.instance.row) {
            console.warn('Invalid SimData structure for CAS analysis');
            return;
        }
        
        const row = simData.instance.row;
        
        try {
            // Extract part type information
            this.extractPartType(row, casPartInfo);
            
            // Extract age and gender information
            this.extractAgeGender(row, casPartInfo);
            
            // Check species (only process human CAS parts)
            if (!this.isHumanSpecies(row)) {
                casPartInfo.category = casPartInfo.category; // Keep unknown
                return;
            }
            
            // Extract body type/location information
            this.extractBodyType(row, casPartInfo);
            
            // Extract clothing category information
            this.extractClothingCategory(row, casPartInfo);
            
            // Extract tags if available
            this.extractTags(row, casPartInfo);
            
            // Generate description based on extracted data
            casPartInfo.description = DescriptionGenerator.generateCASDescription(casPartInfo);
            
        } catch (error) {
            console.warn('Error extracting CAS data from SimData:', error);
        }
    }
    
    /**
     * Extracts part type information from SimData row
     * 
     * @param row - SimData row object
     * @param casPartInfo - CAS part info to populate
     */
    private static extractPartType(row: any, casPartInfo: CASPartInfo): void {
        if (row.PartType) {
            PartTypeMapper.mapPartTypeToCASCategory(row.PartType, casPartInfo);
        }
    }
    
    /**
     * Extracts age and gender information from SimData row
     * 
     * @param row - SimData row object
     * @param casPartInfo - CAS part info to populate
     */
    private static extractAgeGender(row: any, casPartInfo: CASPartInfo): void {
        if (row.AgeGender) {
            AgeGenderMapper.extractAgeGenderFromValue(row.AgeGender, casPartInfo);
        }
    }
    
    /**
     * Checks if the CAS part is for human species
     * 
     * @param row - SimData row object
     * @returns true if species is human (1) or not specified
     */
    private static isHumanSpecies(row: any): boolean {
        if (!row.Species) {
            return true; // Assume human if not specified
        }
        
        const speciesValue = this.extractValue(row.Species);
        return speciesValue === 1 || speciesValue === null; // 1 = human
    }
    
    /**
     * Extracts body type/location information from SimData row
     * 
     * @param row - SimData row object
     * @param casPartInfo - CAS part info to populate
     */
    private static extractBodyType(row: any, casPartInfo: CASPartInfo): void {
        if (row.BodyType) {
            BodyLocationMapper.mapBodyTypeToLocation(row.BodyType, casPartInfo);
        }
    }
    
    /**
     * Extracts clothing category information from SimData row
     * 
     * @param row - SimData row object
     * @param casPartInfo - CAS part info to populate
     */
    private static extractClothingCategory(row: any, casPartInfo: CASPartInfo): void {
        if (row.ClothingCategory) {
            ClothingCategoryMapper.mapClothingCategory(row.ClothingCategory, casPartInfo);
        }
    }
    
    /**
     * Extracts tags from SimData row
     * 
     * @param row - SimData row object
     * @param casPartInfo - CAS part info to populate
     */
    private static extractTags(row: any, casPartInfo: CASPartInfo): void {
        if (row.Tags) {
            const tagsValue = this.extractValue(row.Tags);
            
            if (Array.isArray(tagsValue)) {
                casPartInfo.tags = tagsValue
                    .map((tag: any) => this.extractValue(tag))
                    .filter((tag: any) => tag !== null)
                    .map((tag: any) => String(tag));
            } else if (tagsValue !== null) {
                casPartInfo.tags = [String(tagsValue)];
            }
        }
    }
    
    /**
     * Extracts value from SimData field, handling various formats
     * 
     * @param field - SimData field object
     * @returns Extracted value or null if extraction fails
     */
    private static extractValue(field: any): any {
        if (!field) {
            return null;
        }
        
        // Handle direct value
        if (field.value !== undefined) {
            return field.value;
        }
        
        // Handle direct field
        if (typeof field === 'string' || typeof field === 'number') {
            return field;
        }
        
        return null;
    }
    
    /**
     * Validates SimData structure for CAS analysis
     * 
     * @param simData - SimData resource to validate
     * @returns true if structure is valid for CAS analysis
     */
    public static isValidCASSimData(simData: SimDataResource): boolean {
        return !!(simData && 
                 simData.instance && 
                 simData.instance.row &&
                 typeof simData.instance.row === 'object');
    }
    
    /**
     * Gets available field names from SimData row
     * 
     * @param simData - SimData resource
     * @returns Array of field names available in the row
     */
    public static getAvailableFields(simData: SimDataResource): string[] {
        if (!this.isValidCASSimData(simData)) {
            return [];
        }
        
        return Object.keys(simData.instance.row);
    }
}
