#!/usr/bin/env node

/**
 * Metadata Extraction Test Tool
 * 
 * Tests the enhanced metadata extraction capabilities for both
 * package and script files to verify Phase 3A implementation.
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface MetadataTestResult {
    filePath: string;
    fileType: string;
    author?: string;
    version?: string;
    modName?: string;
    metadataSource?: string;
    metadataConfidence?: number;
}

class MetadataExtractionTester {
    private analysisService: PackageAnalysisService;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
    }
    
    /**
     * Tests metadata extraction on all files in assets directory
     */
    public async testMetadataExtraction(): Promise<void> {
        console.log('🔍 Testing Enhanced Metadata Extraction (Phase 3A)...\n');
        
        const assetsDir = path.join(process.cwd(), 'assets');
        
        if (!fs.existsSync(assetsDir)) {
            console.error('❌ Assets directory not found:', assetsDir);
            return;
        }
        
        const files = fs.readdirSync(assetsDir)
            .filter(file => file.endsWith('.package') || file.endsWith('.ts4script'))
            .slice(0, 10); // Limit to first 10 files for testing
        
        console.log(`📁 Found ${files.length} test files\n`);
        
        const results: MetadataTestResult[] = [];
        
        for (const file of files) {
            const filePath = path.join(assetsDir, file);
            const result = await this.testFile(filePath);
            results.push(result);
        }
        
        this.generateReport(results);
    }
    
    /**
     * Tests metadata extraction for a single file
     */
    private async testFile(filePath: string): Promise<MetadataTestResult> {
        const fileName = path.basename(filePath);
        console.log(`🔍 Testing: ${fileName}`);
        
        try {
            const buffer = fs.readFileSync(filePath);
            const analysis = this.analysisService.detailedAnalyze(buffer, filePath);
            
            const metadata = analysis.metadata;
            const result: MetadataTestResult = {
                filePath: fileName,
                fileType: analysis.fileType,
                author: metadata.author,
                version: metadata.version,
                modName: metadata.modName,
                metadataSource: metadata.metadataSource,
                metadataConfidence: metadata.metadataConfidence
            };
            
            // Display results
            const authorDisplay = result.author || 'No author';
            const versionDisplay = result.version || 'No version';
            const sourceDisplay = result.metadataSource || 'unknown';
            const confidenceDisplay = result.metadataConfidence || 0;
            
            console.log(`   📝 Author: ${authorDisplay}`);
            console.log(`   📝 Version: ${versionDisplay}`);
            if (result.modName) {
                console.log(`   📝 Mod Name: ${result.modName}`);
            }
            console.log(`   📝 Source: ${sourceDisplay} (confidence: ${confidenceDisplay}%)`);
            console.log(`   ✅ Success\n`);
            
            return result;
            
        } catch (error) {
            console.log(`   ❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}\n`);
            return {
                filePath: fileName,
                fileType: 'unknown'
            };
        }
    }
    
    /**
     * Generates a comprehensive metadata extraction report
     */
    private generateReport(results: MetadataTestResult[]): void {
        console.log('📊 METADATA EXTRACTION REPORT');
        console.log('==================================================\n');
        
        // Calculate statistics
        const totalFiles = results.length;
        const filesWithAuthor = results.filter(r => r.author).length;
        const filesWithVersion = results.filter(r => r.version).length;
        const filesWithModName = results.filter(r => r.modName).length;
        
        const authorPercentage = Math.round((filesWithAuthor / totalFiles) * 100);
        const versionPercentage = Math.round((filesWithVersion / totalFiles) * 100);
        const modNamePercentage = Math.round((filesWithModName / totalFiles) * 100);
        
        console.log('📈 SUMMARY:');
        console.log(`   Total Files: ${totalFiles}`);
        console.log(`   Author Detection: ${filesWithAuthor}/${totalFiles} (${authorPercentage}%)`);
        console.log(`   Version Detection: ${filesWithVersion}/${totalFiles} (${versionPercentage}%)`);
        console.log(`   Mod Name Detection: ${filesWithModName}/${totalFiles} (${modNamePercentage}%)\n`);
        
        // Breakdown by file type
        const packageFiles = results.filter(r => r.fileType === 'package');
        const scriptFiles = results.filter(r => r.fileType === 'script');
        
        if (packageFiles.length > 0) {
            const pkgWithAuthor = packageFiles.filter(r => r.author).length;
            const pkgWithVersion = packageFiles.filter(r => r.version).length;
            console.log(`📦 PACKAGE FILES (${packageFiles.length}):`);
            console.log(`   Author Detection: ${pkgWithAuthor}/${packageFiles.length} (${Math.round((pkgWithAuthor/packageFiles.length)*100)}%)`);
            console.log(`   Version Detection: ${pkgWithVersion}/${packageFiles.length} (${Math.round((pkgWithVersion/packageFiles.length)*100)}%)`);
            
            packageFiles.forEach(file => {
                const author = file.author || 'No author';
                const version = file.version || 'No version';
                console.log(`   ✅ ${file.filePath}: ${author} | ${version}`);
            });
            console.log();
        }
        
        if (scriptFiles.length > 0) {
            const scriptWithAuthor = scriptFiles.filter(r => r.author).length;
            const scriptWithVersion = scriptFiles.filter(r => r.version).length;
            console.log(`🐍 SCRIPT FILES (${scriptFiles.length}):`);
            console.log(`   Author Detection: ${scriptWithAuthor}/${scriptFiles.length} (${Math.round((scriptWithAuthor/scriptFiles.length)*100)}%)`);
            console.log(`   Version Detection: ${scriptWithVersion}/${scriptFiles.length} (${Math.round((scriptWithVersion/scriptFiles.length)*100)}%)`);
            
            scriptFiles.forEach(file => {
                const author = file.author || 'No author';
                const version = file.version || 'No version';
                console.log(`   ✅ ${file.filePath}: ${author} | ${version}`);
            });
            console.log();
        }
        
        // Metadata source analysis
        const sourceBreakdown = results.reduce((acc, r) => {
            const source = r.metadataSource || 'none';
            acc[source] = (acc[source] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);
        
        console.log('🔍 METADATA SOURCES:');
        Object.entries(sourceBreakdown).forEach(([source, count]) => {
            const percentage = Math.round((count / totalFiles) * 100);
            console.log(`   ${source}: ${count}/${totalFiles} (${percentage}%)`);
        });
        console.log();
        
        // Success assessment
        const overallSuccess = authorPercentage >= 80 ? '🎉 EXCELLENT' : 
                             authorPercentage >= 60 ? '✅ GOOD' : 
                             authorPercentage >= 40 ? '⚠️ FAIR' : '❌ NEEDS IMPROVEMENT';
        
        console.log(`🎯 OVERALL ASSESSMENT: ${overallSuccess}`);
        console.log(`   Target: 95%+ author detection, 85%+ version detection`);
        console.log(`   Current: ${authorPercentage}% author, ${versionPercentage}% version`);
        
        if (authorPercentage >= 95 && versionPercentage >= 85) {
            console.log('   🏆 PHASE 3A TARGETS ACHIEVED!');
        } else if (authorPercentage >= 80) {
            console.log('   📈 Excellent progress! Continue with remaining tasks.');
        } else {
            console.log('   📋 Continue implementing metadata enhancement features.');
        }
    }
}

// Run the test
async function main() {
    const tester = new MetadataExtractionTester();
    await tester.testMetadataExtraction();
}

if (require.main === module) {
    main().catch(console.error);
}
