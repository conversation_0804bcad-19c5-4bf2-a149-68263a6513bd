/**
 * Gameplay Area Detector
 * 
 * Specialized component for detecting which gameplay areas are affected
 * by script mods based on content analysis.
 * 
 * Extracted from ScriptAnalyzer.ts as part of Phase 2 refactoring.
 */

import { GameplayArea, GAMEPLAY_AREA_PATTERNS, type ScriptModInfo } from './types';

/**
 * Handles detection of gameplay areas affected by script mods
 */
export class GameplayAreaDetector {
    
    /**
     * Detects gameplay areas from content (Python code or ZIP content)
     * 
     * @param content - Content to analyze
     * @param scriptInfo - Script info object to populate
     */
    public static detectFromContent(content: string, scriptInfo: ScriptModInfo): void {
        try {
            // Clear existing gameplay areas to avoid duplicates
            scriptInfo.gameplayAreas = [];
            
            // Check each gameplay area pattern
            for (const [area, patterns] of Object.entries(GAMEPLAY_AREA_PATTERNS)) {
                if (area === GameplayArea.UNKNOWN) continue;
                
                // If any pattern matches, add the area
                if (this.matchesAnyPattern(content, patterns)) {
                    const gameplayArea = area as GameplayArea;
                    if (!scriptInfo.gameplayAreas.includes(gameplayArea)) {
                        scriptInfo.gameplayAreas.push(gameplayArea);
                    }
                }
            }
            
            // If no areas detected, add unknown
            if (scriptInfo.gameplayAreas.length === 0) {
                scriptInfo.gameplayAreas.push(GameplayArea.UNKNOWN);
            }
            
        } catch (error) {
            console.warn('Error detecting gameplay areas:', error);
            scriptInfo.gameplayAreas = [GameplayArea.UNKNOWN];
        }
    }
    
    /**
     * Checks if content matches any of the provided patterns
     * 
     * @param content - Content to check
     * @param patterns - Array of regex patterns to test
     * @returns true if any pattern matches
     */
    private static matchesAnyPattern(content: string, patterns: RegExp[]): boolean {
        return patterns.some(pattern => pattern.test(content));
    }
    
    /**
     * Detects gameplay areas from filename patterns
     * 
     * @param fileName - Script file name
     * @param scriptInfo - Script info object to populate
     */
    public static detectFromFileName(fileName: string, scriptInfo: ScriptModInfo): void {
        const lowerFileName = fileName.toLowerCase();
        
        const fileNamePatterns: Record<GameplayArea, string[]> = {
            [GameplayArea.CAREERS]: ['career', 'job', 'work', 'profession'],
            [GameplayArea.RELATIONSHIPS]: ['relationship', 'romance', 'love', 'dating'],
            [GameplayArea.FAMILY]: ['family', 'household', 'parent', 'child'],
            [GameplayArea.PREGNANCY]: ['pregnancy', 'pregnant', 'birth', 'baby'],
            [GameplayArea.SKILLS]: ['skill', 'ability', 'talent'],
            [GameplayArea.TRAITS]: ['trait', 'personality'],
            [GameplayArea.ASPIRATIONS]: ['aspiration', 'goal', 'wish'],
            [GameplayArea.EMOTIONS]: ['emotion', 'mood', 'feeling', 'buff'],
            [GameplayArea.INTERACTIONS]: ['interaction', 'social'],
            [GameplayArea.EVENTS]: ['event', 'party', 'holiday'],
            [GameplayArea.AUTONOMY]: ['autonomy', 'behavior', 'ai'],
            [GameplayArea.UI]: ['ui', 'interface', 'menu', 'cheat'],
            [GameplayArea.OBJECTS]: ['object', 'furniture', 'item'],
            [GameplayArea.UNKNOWN]: []
        };
        
        // Check filename patterns and add to existing areas
        for (const [area, patterns] of Object.entries(fileNamePatterns)) {
            if (area === GameplayArea.UNKNOWN) continue;
            
            if (patterns.some(pattern => lowerFileName.includes(pattern))) {
                const gameplayArea = area as GameplayArea;
                if (!scriptInfo.gameplayAreas.includes(gameplayArea)) {
                    scriptInfo.gameplayAreas.push(gameplayArea);
                }
            }
        }
    }
    
    /**
     * Gets human-readable descriptions for gameplay areas
     * 
     * @param gameplayAreas - Array of gameplay areas
     * @returns Array of human-readable descriptions
     */
    public static getAreaDescriptions(gameplayAreas: GameplayArea[]): string[] {
        const descriptions: Record<GameplayArea, string> = {
            [GameplayArea.CAREERS]: 'Career and Job System',
            [GameplayArea.RELATIONSHIPS]: 'Relationships and Romance',
            [GameplayArea.FAMILY]: 'Family and Household Management',
            [GameplayArea.PREGNANCY]: 'Pregnancy and Birth',
            [GameplayArea.SKILLS]: 'Skills and Abilities',
            [GameplayArea.TRAITS]: 'Personality Traits',
            [GameplayArea.ASPIRATIONS]: 'Aspirations and Goals',
            [GameplayArea.EMOTIONS]: 'Emotions and Moods',
            [GameplayArea.INTERACTIONS]: 'Social Interactions',
            [GameplayArea.EVENTS]: 'Events and Parties',
            [GameplayArea.AUTONOMY]: 'Autonomous Behavior',
            [GameplayArea.UI]: 'User Interface',
            [GameplayArea.OBJECTS]: 'Objects and Items',
            [GameplayArea.UNKNOWN]: 'Unknown Gameplay Area'
        };
        
        return gameplayAreas.map(area => descriptions[area] || descriptions[GameplayArea.UNKNOWN]);
    }
    
    /**
     * Determines the primary gameplay area from multiple areas
     * 
     * @param gameplayAreas - Array of gameplay areas
     * @returns Primary gameplay area
     */
    public static getPrimaryArea(gameplayAreas: GameplayArea[]): GameplayArea {
        if (gameplayAreas.length === 0) {
            return GameplayArea.UNKNOWN;
        }
        
        if (gameplayAreas.length === 1) {
            return gameplayAreas[0];
        }
        
        // Priority order for determining primary area
        const priorityOrder = [
            GameplayArea.FRAMEWORK, // Not in enum but would be highest priority
            GameplayArea.UI,
            GameplayArea.CAREERS,
            GameplayArea.RELATIONSHIPS,
            GameplayArea.FAMILY,
            GameplayArea.PREGNANCY,
            GameplayArea.SKILLS,
            GameplayArea.TRAITS,
            GameplayArea.ASPIRATIONS,
            GameplayArea.EMOTIONS,
            GameplayArea.INTERACTIONS,
            GameplayArea.EVENTS,
            GameplayArea.AUTONOMY,
            GameplayArea.OBJECTS,
            GameplayArea.UNKNOWN
        ];
        
        // Return the first area found in priority order
        for (const priorityArea of priorityOrder) {
            if (gameplayAreas.includes(priorityArea)) {
                return priorityArea;
            }
        }
        
        return gameplayAreas[0];
    }
    
    /**
     * Validates gameplay area assignments
     * 
     * @param gameplayAreas - Array of gameplay areas to validate
     * @returns Validation result
     */
    public static validateAreas(gameplayAreas: GameplayArea[]): {
        isValid: boolean;
        issues: string[];
    } {
        const issues: string[] = [];
        
        // Check for empty array
        if (gameplayAreas.length === 0) {
            issues.push('No gameplay areas detected');
        }
        
        // Check for invalid values
        const validAreas = Object.values(GameplayArea);
        const invalidAreas = gameplayAreas.filter(area => !validAreas.includes(area));
        if (invalidAreas.length > 0) {
            issues.push(`Invalid gameplay areas: ${invalidAreas.join(', ')}`);
        }
        
        // Check for too many areas (might indicate overly broad detection)
        if (gameplayAreas.length > 5) {
            issues.push('Too many gameplay areas detected - may indicate overly broad script');
        }
        
        return {
            isValid: issues.length === 0,
            issues
        };
    }
    
    /**
     * Suggests related gameplay areas based on detected areas
     * 
     * @param gameplayAreas - Currently detected areas
     * @returns Array of suggested related areas
     */
    public static suggestRelatedAreas(gameplayAreas: GameplayArea[]): GameplayArea[] {
        const relatedAreas: Record<GameplayArea, GameplayArea[]> = {
            [GameplayArea.CAREERS]: [GameplayArea.SKILLS, GameplayArea.ASPIRATIONS],
            [GameplayArea.RELATIONSHIPS]: [GameplayArea.EMOTIONS, GameplayArea.INTERACTIONS],
            [GameplayArea.FAMILY]: [GameplayArea.PREGNANCY, GameplayArea.RELATIONSHIPS],
            [GameplayArea.PREGNANCY]: [GameplayArea.FAMILY, GameplayArea.EMOTIONS],
            [GameplayArea.SKILLS]: [GameplayArea.CAREERS, GameplayArea.ASPIRATIONS],
            [GameplayArea.TRAITS]: [GameplayArea.EMOTIONS, GameplayArea.AUTONOMY],
            [GameplayArea.ASPIRATIONS]: [GameplayArea.CAREERS, GameplayArea.SKILLS],
            [GameplayArea.EMOTIONS]: [GameplayArea.TRAITS, GameplayArea.RELATIONSHIPS],
            [GameplayArea.INTERACTIONS]: [GameplayArea.RELATIONSHIPS, GameplayArea.EMOTIONS],
            [GameplayArea.EVENTS]: [GameplayArea.INTERACTIONS, GameplayArea.EMOTIONS],
            [GameplayArea.AUTONOMY]: [GameplayArea.TRAITS, GameplayArea.EMOTIONS],
            [GameplayArea.UI]: [],
            [GameplayArea.OBJECTS]: [],
            [GameplayArea.UNKNOWN]: []
        };
        
        const suggestions: GameplayArea[] = [];
        
        gameplayAreas.forEach(area => {
            const related = relatedAreas[area] || [];
            related.forEach(relatedArea => {
                if (!gameplayAreas.includes(relatedArea) && !suggestions.includes(relatedArea)) {
                    suggestions.push(relatedArea);
                }
            });
        });
        
        return suggestions;
    }
}
