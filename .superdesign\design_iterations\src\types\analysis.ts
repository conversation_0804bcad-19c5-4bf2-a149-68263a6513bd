/**
 * Represents the essential, extracted metadata for a single resource
 * within a .package file. This is a "Data Transfer Object" (DTO) that
 * will be used to pass information between the analysis engine and
 * other modules.
 */
export interface ResourceInfo {
    /** The unique identifier for the resource (Type, Group, Instance). */
    key: string;
    /** The file type of the resource (e.g., "SimData," "CASPart"). */
    type: string;
    /** The group ID of the resource. */
    group: number;
    /** The instance ID of the resource. */
    instance: string;
    /** A flag indicating if this resource is a suspected override of a game resource. */
    isOverride: boolean;
    /** A brief, human-readable summary of the resource's content. */
    contentSnippet: string;
}

/**
 * Mod categories for automatic organization
 */
export enum ModCategory {
    CAS_CC = 'cas_cc',
    BUILD_BUY_CC = 'build_buy_cc',
    SCRIPT_MOD = 'script_mod',
    TUNING_MOD = 'tuning_mod',
    OVERRIDE = 'override',
    FRAMEWORK = 'framework',
    LIBRARY = 'library',
    UNKNOWN = 'unknown'
}

/**
 * File type enumeration
 */
export enum FileType {
    PACKAGE = 'package',
    SCRIPT = 'script',
    UNKNOWN = 'unknown'
}

/**
 * Enhanced analysis results with categorization and organization data
 */
export interface AnalyzedPackage {
    /** The absolute file path of the analyzed package. */
    filePath: string;
    /** The detected file type. */
    fileType: FileType;
    /** The detected mod category for organization. */
    category: ModCategory;
    /** Subcategory for more specific organization. */
    subcategory?: string;
    /** A flag indicating if the package itself is considered an override. */
    isOverride: boolean;
    /** File size in bytes. */
    fileSize: number;
    /** Number of resources in the package. */
    resourceCount: number;
    /** An array of all the resources contained within this package. */
    resources: ResourceInfo[];
    /** Suggested organization folder path. */
    suggestedPath?: string;
    /** Dependencies detected in the mod. */
    dependencies: string[];
    /** Potential conflicts with other mods. */
    conflicts: string[];
    /** Additional metadata specific to the file type. */
    metadata: Record<string, any>;
}