/**
 * Test Full Mod Collection for Conflicts
 * 
 * Tests the entire mod collection for comprehensive conflict detection.
 * This may take longer but provides complete coverage.
 */

import { testRealWorldConflicts } from './test-real-world-conflicts';

/**
 * Test the full mod collection
 */
async function testFullCollection() {
    console.log('🔍 FULL COLLECTION CONFLICT ANALYSIS');
    console.log('====================================\n');
    console.log('⚠️  This will analyze your entire mod collection.');
    console.log('⏱️  This may take several minutes depending on collection size.\n');
    
    await testRealWorldConflicts(true);
}

// Run the full collection test
if (require.main === module) {
    testFullCollection().catch(console.error);
}

export { testFullCollection };
