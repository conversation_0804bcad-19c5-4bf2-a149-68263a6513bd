/**
 * Object Content Analyzer
 * 
 * Analyzes furniture, decorations, build items with room/category classification.
 * Follows the same patterns as CAS analyzer for consistency.
 * 
 * Covers ~40% of mod collections not handled by CAS analysis.
 */

import { SimDataResource } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';
import {
    ObjectPartInfo,
    ObjectCategory,
    RoomType,
    PriceRange,
    ObjectFunction,
    OBJECT_DEFINITION_RESOURCE_TYPE,
    OBJECT_CATALOG_RESOURCE_TYPE,
    DEFAULT_OBJECT_PART_INFO
} from './types';
import { EnhancedObjectClassifier, type EnhancedObjectClassification } from './EnhancedObjectClassifier';

/**
 * Main Object Content Analyzer
 * Following CAS analyzer patterns for consistency
 */
export class ObjectContentAnalyzer {
    
    /**
     * Analyzes object resources to extract detailed categorization information
     * Main entry point following CAS analyzer pattern with enhanced classification
     */
    public static analyzeObjectParts(resources: ResourceEntry[], filename?: string): ObjectPartInfo[] {
        const objectPartInfos: ObjectPartInfo[] = [];

        // Find all object-related resources
        const objectResources = resources.filter(resource =>
            resource.key.type === OBJECT_DEFINITION_RESOURCE_TYPE ||
            resource.key.type === OBJECT_CATALOG_RESOURCE_TYPE
        );

        console.log(`[ObjectContentAnalyzer] Found ${objectResources.length} object resources`);

        for (const objectResource of objectResources) {
            try {
                const objectPartInfo = this.analyzeSingleObjectPartEnhanced(objectResource, filename || '');
                objectPartInfos.push(objectPartInfo);
            } catch (error) {
                console.warn('Error analyzing Object Part:', error);
                // Add a fallback entry using default
                objectPartInfos.push({ ...DEFAULT_OBJECT_PART_INFO });
            }
        }

        return objectPartInfos;
    }

    /**
     * Enhanced single object analysis using the new classifier
     */
    private static analyzeSingleObjectPartEnhanced(resource: ResourceEntry, filename: string): ObjectPartInfo {
        // Initialize with defaults
        const objectPartInfo: ObjectPartInfo = { ...DEFAULT_OBJECT_PART_INFO };

        try {
            // Extract buffer
            const buffer = this.extractBuffer(resource.value);
            if (!buffer || !this.isValidObjectBuffer(buffer)) {
                return this.fallbackToFilenameClassification(filename);
            }

            let simData: SimDataResource | undefined;

            // Try to parse as SimData first (many objects are SimData resources)
            if (this.isSimDataFormat(buffer)) {
                simData = SimDataResource.from(buffer);
                if (!simData || !this.isValidObjectSimData(simData)) {
                    simData = undefined;
                }
            }

            // Use enhanced classifier
            const classification = EnhancedObjectClassifier.classifyObject(
                filename,
                simData,
                resource.key.type
            );

            // Convert enhanced classification to ObjectPartInfo
            this.applyEnhancedClassification(objectPartInfo, classification);

            // Generate description
            objectPartInfo.description = this.generateEnhancedDescription(classification);

        } catch (error) {
            console.warn('Error in enhanced object analysis:', error);
            return this.fallbackToFilenameClassification(filename);
        }

        return objectPartInfo;
    }

    /**
     * Apply enhanced classification results to ObjectPartInfo
     */
    private static applyEnhancedClassification(
        objectPartInfo: ObjectPartInfo,
        classification: EnhancedObjectClassification
    ): void {
        // Map enhanced categories to existing enum values
        objectPartInfo.category = this.mapToObjectCategory(classification.category);
        objectPartInfo.subcategory = classification.specificType;
        objectPartInfo.tags = classification.tags;

        // Map room types
        objectPartInfo.roomTypes = classification.roomTypes.map(room =>
            this.mapToRoomType(room)
        ).filter(room => room !== undefined) as RoomType[];

        // Map functionality
        objectPartInfo.functionality = classification.functionality.map(func =>
            this.mapToObjectFunction(func)
        ).filter(func => func !== undefined) as ObjectFunction[];

        // Set price range if available
        if (classification.metadata.price) {
            objectPartInfo.priceRange = this.mapToPriceRange(classification.metadata.price);
        }
    }

    /**
     * Fallback classification using filename only
     */
    private static fallbackToFilenameClassification(filename: string): ObjectPartInfo {
        const classification = EnhancedObjectClassifier.classifyObject(filename);
        const objectPartInfo: ObjectPartInfo = { ...DEFAULT_OBJECT_PART_INFO };

        this.applyEnhancedClassification(objectPartInfo, classification);
        objectPartInfo.description = this.generateEnhancedDescription(classification);

        return objectPartInfo;
    }

    /**
     * Generate enhanced description from classification
     */
    private static generateEnhancedDescription(classification: EnhancedObjectClassification): string {
        const { category, specificType, roomTypes, confidence } = classification;

        let description = `${specificType.charAt(0).toUpperCase() + specificType.slice(1)}`;

        if (roomTypes.length > 0 && roomTypes[0] !== 'any') {
            description += ` for ${roomTypes.join(', ')} use`;
        }

        if (confidence > 0.8) {
            description += ' (high confidence)';
        } else if (confidence < 0.5) {
            description += ' (estimated)';
        }

        return description;
    }

    /**
     * Mapping helper methods
     */
    private static mapToObjectCategory(category: string): ObjectCategory {
        const mapping: Record<string, ObjectCategory> = {
            'furniture': ObjectCategory.FURNITURE,
            'decorations': ObjectCategory.DECORATIONS,
            'lighting': ObjectCategory.LIGHTING,
            'plumbing': ObjectCategory.PLUMBING,
            'electronics': ObjectCategory.ELECTRONICS,
            'appliances': ObjectCategory.APPLIANCES,
            'outdoor': ObjectCategory.OUTDOOR,
            'storage': ObjectCategory.STORAGE
        };

        return mapping[category] || ObjectCategory.FURNITURE;
    }

    private static mapToRoomType(roomType: string): RoomType | undefined {
        const mapping: Record<string, RoomType> = {
            'living': RoomType.LIVING,
            'bedroom': RoomType.BEDROOM,
            'kitchen': RoomType.KITCHEN,
            'bathroom': RoomType.BATHROOM,
            'dining': RoomType.DINING,
            'outdoor': RoomType.OUTDOOR,
            'office': RoomType.OFFICE,
            'kids': RoomType.KIDS,
            'garage': RoomType.GARAGE,
            'basement': RoomType.BASEMENT,
            'any': RoomType.ANY
        };

        return mapping[roomType];
    }

    private static mapToObjectFunction(functionality: string): ObjectFunction | undefined {
        const mapping: Record<string, ObjectFunction> = {
            'seating': ObjectFunction.SEATING,
            'sleeping': ObjectFunction.SLEEPING,
            'storage': ObjectFunction.STORAGE,
            'entertainment': ObjectFunction.ENTERTAINMENT,
            'cooking': ObjectFunction.COOKING,
            'hygiene': ObjectFunction.HYGIENE,
            'decoration': ObjectFunction.DECORATION,
            'lighting': ObjectFunction.LIGHTING,
            'exercise': ObjectFunction.EXERCISE,
            'study': ObjectFunction.STUDY,
            'hobby': ObjectFunction.HOBBY,
            'comfort': ObjectFunction.SEATING, // Map comfort to seating
            'surface': ObjectFunction.STORAGE, // Map surface to storage
            'food': ObjectFunction.COOKING,    // Map food to cooking
            'outdoor': ObjectFunction.DECORATION // Map outdoor to decoration
        };

        return mapping[functionality];
    }

    private static mapToPriceRange(price: number): PriceRange {
        if (price < 500) return PriceRange.BUDGET;
        if (price < 2000) return PriceRange.MID;
        return PriceRange.EXPENSIVE;
    }

    /**
     * Analyzes a single object resource by parsing its binary structure
     * Following CAS analyzer pattern
     */
    private static analyzeSingleObjectPart(resource: ResourceEntry): ObjectPartInfo {
        // Initialize with defaults
        const objectPartInfo: ObjectPartInfo = { ...DEFAULT_OBJECT_PART_INFO };

        try {
            // Extract buffer
            const buffer = this.extractBuffer(resource.value);
            if (!buffer || !this.isValidObjectBuffer(buffer)) {
                return objectPartInfo;
            }

            // Try to parse as SimData first (many objects are SimData resources)
            if (this.isSimDataFormat(buffer)) {
                const simData = SimDataResource.from(buffer);
                if (simData && this.isValidObjectSimData(simData)) {
                    this.extractFromSimData(simData, objectPartInfo);
                } else {
                    // Fallback to binary analysis
                    this.extractObjectDataFallback(buffer, objectPartInfo);
                }
            } else {
                // Fallback to binary analysis
                this.extractObjectDataFallback(buffer, objectPartInfo);
            }

            // Generate description
            objectPartInfo.description = this.generateObjectDescription(objectPartInfo);

        } catch (error) {
            console.warn('Error parsing Object data:', error);
            // Return default info on error
        }

        return objectPartInfo;
    }

    /**
     * Extract buffer from resource value
     */
    private static extractBuffer(value: any): Buffer | null {
        try {
            if (Buffer.isBuffer(value)) {
                return value;
            }
            if (value && typeof value.getRawData === 'function') {
                return value.getRawData();
            }
            if (value && value.buffer) {
                return Buffer.from(value.buffer);
            }
            return null;
        } catch (error) {
            console.warn('Error extracting buffer:', error);
            return null;
        }
    }

    /**
     * Check if buffer is valid for object analysis
     */
    private static isValidObjectBuffer(buffer: Buffer): boolean {
        return buffer && buffer.length > 0;
    }

    /**
     * Check if buffer is in SimData format
     */
    private static isSimDataFormat(buffer: Buffer): boolean {
        // SimData resources typically start with specific headers
        if (buffer.length < 4) return false;
        
        // Check for common SimData signatures
        const header = buffer.readUInt32LE(0);
        return header === 0x44415453 || // 'STAD' 
               header === 0x53494D44;   // 'SIMD'
    }

    /**
     * Check if SimData is valid for object analysis
     */
    private static isValidObjectSimData(simData: SimDataResource): boolean {
        return simData && simData.instance && simData.instance.table;
    }

    /**
     * Extract object information from SimData
     */
    private static extractFromSimData(simData: SimDataResource, objectPartInfo: ObjectPartInfo): void {
        try {
            const table = simData.instance.table;
            
            // Extract basic object information
            this.extractObjectCategory(table, objectPartInfo);
            this.extractRoomTypes(table, objectPartInfo);
            this.extractPriceRange(table, objectPartInfo);
            this.extractFunctionality(table, objectPartInfo);
            this.extractTags(table, objectPartInfo);

        } catch (error) {
            console.warn('Error extracting from SimData:', error);
        }
    }

    /**
     * Extract object category from SimData table
     */
    private static extractObjectCategory(table: any, objectPartInfo: ObjectPartInfo): void {
        // Look for category indicators in the table
        // This is a simplified implementation - real implementation would need
        // detailed knowledge of object SimData structure
        
        if (table.chair || table.seating) {
            objectPartInfo.category = ObjectCategory.FURNITURE;
            objectPartInfo.subcategory = 'seating';
            objectPartInfo.functionality.push(ObjectFunction.SEATING);
        } else if (table.bed || table.sleeping) {
            objectPartInfo.category = ObjectCategory.FURNITURE;
            objectPartInfo.subcategory = 'bed';
            objectPartInfo.functionality.push(ObjectFunction.SLEEPING);
        } else if (table.light || table.lighting) {
            objectPartInfo.category = ObjectCategory.LIGHTING;
            objectPartInfo.subcategory = 'light';
            objectPartInfo.functionality.push(ObjectFunction.LIGHTING);
        } else if (table.decoration || table.decorative) {
            objectPartInfo.category = ObjectCategory.DECORATIONS;
            objectPartInfo.subcategory = 'decoration';
            objectPartInfo.functionality.push(ObjectFunction.DECORATION);
        } else {
            // Default categorization based on common patterns
            objectPartInfo.category = ObjectCategory.FURNITURE;
            objectPartInfo.subcategory = 'furniture';
        }
    }

    /**
     * Extract room types from SimData table
     */
    private static extractRoomTypes(table: any, objectPartInfo: ObjectPartInfo): void {
        // Clear default room types
        objectPartInfo.roomTypes = [];

        // Analyze object category and functionality to determine room types
        switch (objectPartInfo.category) {
            case ObjectCategory.FURNITURE:
                if (objectPartInfo.subcategory === 'bed') {
                    objectPartInfo.roomTypes.push(RoomType.BEDROOM);
                } else if (objectPartInfo.subcategory === 'seating') {
                    objectPartInfo.roomTypes.push(RoomType.LIVING, RoomType.DINING);
                } else {
                    objectPartInfo.roomTypes.push(RoomType.LIVING);
                }
                break;
            case ObjectCategory.PLUMBING:
                objectPartInfo.roomTypes.push(RoomType.BATHROOM);
                break;
            case ObjectCategory.APPLIANCES:
                objectPartInfo.roomTypes.push(RoomType.KITCHEN);
                break;
            case ObjectCategory.OUTDOOR:
                objectPartInfo.roomTypes.push(RoomType.OUTDOOR);
                break;
            default:
                objectPartInfo.roomTypes.push(RoomType.ANY);
        }
    }

    /**
     * Extract price range from SimData table
     */
    private static extractPriceRange(table: any, objectPartInfo: ObjectPartInfo): void {
        // Look for price information in the table
        if (table.price || table.cost) {
            const price = table.price || table.cost;
            if (price < 500) {
                objectPartInfo.priceRange = PriceRange.BUDGET;
            } else if (price <= 2000) {
                objectPartInfo.priceRange = PriceRange.MID;
            } else {
                objectPartInfo.priceRange = PriceRange.EXPENSIVE;
            }
        } else {
            objectPartInfo.priceRange = PriceRange.UNKNOWN;
        }
    }

    /**
     * Extract functionality from SimData table
     */
    private static extractFunctionality(table: any, objectPartInfo: ObjectPartInfo): void {
        // Functionality is often determined by category and subcategory
        // This is already handled in extractObjectCategory
    }

    /**
     * Extract tags from SimData table
     */
    private static extractTags(table: any, objectPartInfo: ObjectPartInfo): void {
        // Extract any available tags or keywords
        objectPartInfo.tags = [
            objectPartInfo.category,
            objectPartInfo.subcategory,
            ...objectPartInfo.roomTypes,
            objectPartInfo.priceRange
        ].filter(tag => tag && tag !== 'unknown');
    }

    /**
     * Fallback method for binary object data extraction
     */
    private static extractObjectDataFallback(buffer: Buffer, objectPartInfo: ObjectPartInfo): void {
        console.warn('Using fallback binary analysis for Object - limited information available');
        
        // Set basic defaults based on resource type patterns
        objectPartInfo.category = ObjectCategory.FURNITURE;
        objectPartInfo.subcategory = 'furniture';
        objectPartInfo.roomTypes = [RoomType.ANY];
        objectPartInfo.functionality = [ObjectFunction.UNKNOWN];
    }

    /**
     * Generate description for object
     */
    private static generateObjectDescription(objectPartInfo: ObjectPartInfo): string {
        const category = objectPartInfo.category;
        const subcategory = objectPartInfo.subcategory;
        const roomTypes = objectPartInfo.roomTypes.join(', ');
        
        return `${category} - ${subcategory} for ${roomTypes}`;
    }

    /**
     * Summarizes multiple object parts into a single categorization
     */
    public static summarizeObjectParts(objectPartInfos: ObjectPartInfo[]) {
        if (objectPartInfos.length === 0) {
            return {
                primaryCategory: 'none',
                subcategory: 'none',
                description: 'No objects found'
            };
        }

        // Count categories
        const categoryCount = new Map<string, number>();
        for (const info of objectPartInfos) {
            const count = categoryCount.get(info.category) || 0;
            categoryCount.set(info.category, count + 1);
        }

        // Find most common category
        let primaryCategory = 'mixed';
        let maxCount = 0;
        for (const [category, count] of categoryCount.entries()) {
            if (count > maxCount) {
                maxCount = count;
                primaryCategory = category;
            }
        }

        const totalItems = objectPartInfos.length;
        const description = `${totalItems} object${totalItems > 1 ? 's' : ''} (${primaryCategory})`;

        return {
            primaryCategory,
            subcategory: primaryCategory,
            description
        };
    }
}
