/**
 * Object Content Analyzer
 * 
 * Analyzes furniture, decorations, build items with room/category classification.
 * Follows the same patterns as CAS analyzer for consistency.
 * 
 * Covers ~40% of mod collections not handled by CAS analysis.
 */

import { SimDataResource } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';
import {
    ObjectPartInfo,
    ObjectCategory,
    RoomType,
    PriceRange,
    ObjectFunction,
    OBJECT_DEFINITION_RESOURCE_TYPE,
    OBJECT_CATALOG_RESOURCE_TYPE,
    DEFAULT_OBJECT_PART_INFO
} from './types';

/**
 * Main Object Content Analyzer
 * Following CAS analyzer patterns for consistency
 */
export class ObjectContentAnalyzer {
    
    /**
     * Analyzes object resources to extract detailed categorization information
     * Main entry point following CAS analyzer pattern
     */
    public static analyzeObjectParts(resources: ResourceEntry[]): ObjectPartInfo[] {
        const objectPartInfos: ObjectPartInfo[] = [];

        // Find all object-related resources
        const objectResources = resources.filter(resource =>
            resource.key.type === OBJECT_DEFINITION_RESOURCE_TYPE ||
            resource.key.type === OBJECT_CATALOG_RESOURCE_TYPE
        );

        console.log(`[ObjectContentAnalyzer] Found ${objectResources.length} object resources`);

        for (const objectResource of objectResources) {
            try {
                const objectPartInfo = this.analyzeSingleObjectPart(objectResource);
                objectPartInfos.push(objectPartInfo);
            } catch (error) {
                console.warn('Error analyzing Object Part:', error);
                // Add a fallback entry using default
                objectPartInfos.push({ ...DEFAULT_OBJECT_PART_INFO });
            }
        }

        return objectPartInfos;
    }

    /**
     * Analyzes a single object resource by parsing its binary structure
     * Following CAS analyzer pattern
     */
    private static analyzeSingleObjectPart(resource: ResourceEntry): ObjectPartInfo {
        // Initialize with defaults
        const objectPartInfo: ObjectPartInfo = { ...DEFAULT_OBJECT_PART_INFO };

        try {
            // Extract buffer
            const buffer = this.extractBuffer(resource.value);
            if (!buffer || !this.isValidObjectBuffer(buffer)) {
                return objectPartInfo;
            }

            // Try to parse as SimData first (many objects are SimData resources)
            if (this.isSimDataFormat(buffer)) {
                const simData = SimDataResource.from(buffer);
                if (simData && this.isValidObjectSimData(simData)) {
                    this.extractFromSimData(simData, objectPartInfo);
                } else {
                    // Fallback to binary analysis
                    this.extractObjectDataFallback(buffer, objectPartInfo);
                }
            } else {
                // Fallback to binary analysis
                this.extractObjectDataFallback(buffer, objectPartInfo);
            }

            // Generate description
            objectPartInfo.description = this.generateObjectDescription(objectPartInfo);

        } catch (error) {
            console.warn('Error parsing Object data:', error);
            // Return default info on error
        }

        return objectPartInfo;
    }

    /**
     * Extract buffer from resource value
     */
    private static extractBuffer(value: any): Buffer | null {
        try {
            if (Buffer.isBuffer(value)) {
                return value;
            }
            if (value && typeof value.getRawData === 'function') {
                return value.getRawData();
            }
            if (value && value.buffer) {
                return Buffer.from(value.buffer);
            }
            return null;
        } catch (error) {
            console.warn('Error extracting buffer:', error);
            return null;
        }
    }

    /**
     * Check if buffer is valid for object analysis
     */
    private static isValidObjectBuffer(buffer: Buffer): boolean {
        return buffer && buffer.length > 0;
    }

    /**
     * Check if buffer is in SimData format
     */
    private static isSimDataFormat(buffer: Buffer): boolean {
        // SimData resources typically start with specific headers
        if (buffer.length < 4) return false;
        
        // Check for common SimData signatures
        const header = buffer.readUInt32LE(0);
        return header === 0x44415453 || // 'STAD' 
               header === 0x53494D44;   // 'SIMD'
    }

    /**
     * Check if SimData is valid for object analysis
     */
    private static isValidObjectSimData(simData: SimDataResource): boolean {
        return simData && simData.instance && simData.instance.table;
    }

    /**
     * Extract object information from SimData
     */
    private static extractFromSimData(simData: SimDataResource, objectPartInfo: ObjectPartInfo): void {
        try {
            const table = simData.instance.table;
            
            // Extract basic object information
            this.extractObjectCategory(table, objectPartInfo);
            this.extractRoomTypes(table, objectPartInfo);
            this.extractPriceRange(table, objectPartInfo);
            this.extractFunctionality(table, objectPartInfo);
            this.extractTags(table, objectPartInfo);

        } catch (error) {
            console.warn('Error extracting from SimData:', error);
        }
    }

    /**
     * Extract object category from SimData table
     */
    private static extractObjectCategory(table: any, objectPartInfo: ObjectPartInfo): void {
        // Look for category indicators in the table
        // This is a simplified implementation - real implementation would need
        // detailed knowledge of object SimData structure
        
        if (table.chair || table.seating) {
            objectPartInfo.category = ObjectCategory.FURNITURE;
            objectPartInfo.subcategory = 'seating';
            objectPartInfo.functionality.push(ObjectFunction.SEATING);
        } else if (table.bed || table.sleeping) {
            objectPartInfo.category = ObjectCategory.FURNITURE;
            objectPartInfo.subcategory = 'bed';
            objectPartInfo.functionality.push(ObjectFunction.SLEEPING);
        } else if (table.light || table.lighting) {
            objectPartInfo.category = ObjectCategory.LIGHTING;
            objectPartInfo.subcategory = 'light';
            objectPartInfo.functionality.push(ObjectFunction.LIGHTING);
        } else if (table.decoration || table.decorative) {
            objectPartInfo.category = ObjectCategory.DECORATIONS;
            objectPartInfo.subcategory = 'decoration';
            objectPartInfo.functionality.push(ObjectFunction.DECORATION);
        } else {
            // Default categorization based on common patterns
            objectPartInfo.category = ObjectCategory.FURNITURE;
            objectPartInfo.subcategory = 'furniture';
        }
    }

    /**
     * Extract room types from SimData table
     */
    private static extractRoomTypes(table: any, objectPartInfo: ObjectPartInfo): void {
        // Clear default room types
        objectPartInfo.roomTypes = [];

        // Analyze object category and functionality to determine room types
        switch (objectPartInfo.category) {
            case ObjectCategory.FURNITURE:
                if (objectPartInfo.subcategory === 'bed') {
                    objectPartInfo.roomTypes.push(RoomType.BEDROOM);
                } else if (objectPartInfo.subcategory === 'seating') {
                    objectPartInfo.roomTypes.push(RoomType.LIVING, RoomType.DINING);
                } else {
                    objectPartInfo.roomTypes.push(RoomType.LIVING);
                }
                break;
            case ObjectCategory.PLUMBING:
                objectPartInfo.roomTypes.push(RoomType.BATHROOM);
                break;
            case ObjectCategory.APPLIANCES:
                objectPartInfo.roomTypes.push(RoomType.KITCHEN);
                break;
            case ObjectCategory.OUTDOOR:
                objectPartInfo.roomTypes.push(RoomType.OUTDOOR);
                break;
            default:
                objectPartInfo.roomTypes.push(RoomType.ANY);
        }
    }

    /**
     * Extract price range from SimData table
     */
    private static extractPriceRange(table: any, objectPartInfo: ObjectPartInfo): void {
        // Look for price information in the table
        if (table.price || table.cost) {
            const price = table.price || table.cost;
            if (price < 500) {
                objectPartInfo.priceRange = PriceRange.BUDGET;
            } else if (price <= 2000) {
                objectPartInfo.priceRange = PriceRange.MID;
            } else {
                objectPartInfo.priceRange = PriceRange.EXPENSIVE;
            }
        } else {
            objectPartInfo.priceRange = PriceRange.UNKNOWN;
        }
    }

    /**
     * Extract functionality from SimData table
     */
    private static extractFunctionality(table: any, objectPartInfo: ObjectPartInfo): void {
        // Functionality is often determined by category and subcategory
        // This is already handled in extractObjectCategory
    }

    /**
     * Extract tags from SimData table
     */
    private static extractTags(table: any, objectPartInfo: ObjectPartInfo): void {
        // Extract any available tags or keywords
        objectPartInfo.tags = [
            objectPartInfo.category,
            objectPartInfo.subcategory,
            ...objectPartInfo.roomTypes,
            objectPartInfo.priceRange
        ].filter(tag => tag && tag !== 'unknown');
    }

    /**
     * Fallback method for binary object data extraction
     */
    private static extractObjectDataFallback(buffer: Buffer, objectPartInfo: ObjectPartInfo): void {
        console.warn('Using fallback binary analysis for Object - limited information available');
        
        // Set basic defaults based on resource type patterns
        objectPartInfo.category = ObjectCategory.FURNITURE;
        objectPartInfo.subcategory = 'furniture';
        objectPartInfo.roomTypes = [RoomType.ANY];
        objectPartInfo.functionality = [ObjectFunction.UNKNOWN];
    }

    /**
     * Generate description for object
     */
    private static generateObjectDescription(objectPartInfo: ObjectPartInfo): string {
        const category = objectPartInfo.category;
        const subcategory = objectPartInfo.subcategory;
        const roomTypes = objectPartInfo.roomTypes.join(', ');
        
        return `${category} - ${subcategory} for ${roomTypes}`;
    }

    /**
     * Summarizes multiple object parts into a single categorization
     */
    public static summarizeObjectParts(objectPartInfos: ObjectPartInfo[]) {
        if (objectPartInfos.length === 0) {
            return {
                primaryCategory: 'none',
                subcategory: 'none',
                description: 'No objects found'
            };
        }

        // Count categories
        const categoryCount = new Map<string, number>();
        for (const info of objectPartInfos) {
            const count = categoryCount.get(info.category) || 0;
            categoryCount.set(info.category, count + 1);
        }

        // Find most common category
        let primaryCategory = 'mixed';
        let maxCount = 0;
        for (const [category, count] of categoryCount.entries()) {
            if (count > maxCount) {
                maxCount = count;
                primaryCategory = category;
            }
        }

        const totalItems = objectPartInfos.length;
        const description = `${totalItems} object${totalItems > 1 ? 's' : ''} (${primaryCategory})`;

        return {
            primaryCategory,
            subcategory: primaryCategory,
            description
        };
    }
}
