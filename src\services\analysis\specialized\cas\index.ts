/**
 * CAS (Create-A-Sim) Specialized Analysis Components
 * 
 * This module exports all CAS-related analysis components created during
 * Phase 2 refactoring of CASPartAnalyzer.ts.
 * 
 * Each component follows the Single Responsibility Principle and handles
 * a specific aspect of CAS part analysis.
 */

// Type definitions and constants
export * from './types';

// Core analysis components
export { BufferExtractor } from './BufferExtractor';
export { SimDataParser } from './SimDataParser';
export { AgeGenderMapper } from './AgeGenderMapper';
export { PartTypeMapper } from './PartTypeMapper';
export { BodyLocationMapper } from './BodyLocationMapper';
export { ClothingCategoryMapper } from './ClothingCategoryMapper';
export { DescriptionGenerator } from './DescriptionGenerator';

// Enhanced subcategory detection components
export { HairStyleClassifier } from './HairStyleClassifier';
export { EnhancedCASDetector } from './EnhancedCASDetector';

// Re-export commonly used types for convenience
export type {
    CASPartInfo,
    CASPartSummary
} from './types';

export {
    CASCategory,
    AgeGroup,
    Gender,
    ClothingCategory,
    BodyLocation,
    HairLength,
    HairStyle,
    HairTexture
} from './types';
