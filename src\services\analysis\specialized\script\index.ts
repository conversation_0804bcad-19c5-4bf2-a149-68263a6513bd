/**
 * Script (.ts4script) Specialized Analysis Components
 * 
 * This module exports all script-related analysis components created during
 * Phase 2 refactoring of ScriptAnalyzer.ts.
 * 
 * Each component follows the Single Responsibility Principle and handles
 * a specific aspect of script mod analysis.
 */

// Type definitions and constants
export * from './types';

// Core analysis components
export { ZipFileAnalyzer } from './ZipFileAnalyzer';
export { MetadataExtractor } from './MetadataExtractor';
export { PythonContentAnalyzer } from './PythonContentAnalyzer';
export { GameplayAreaDetector } from './GameplayAreaDetector';
export { FrameworkDetector } from './FrameworkDetector';
export { CategoryClassifier } from './CategoryClassifier';
export { DescriptionGenerator } from './DescriptionGenerator';

// Re-export commonly used types for convenience
export type {
    ScriptModInfo
} from './types';

export {
    ScriptModCategory,
    GameplayArea
} from './types';
