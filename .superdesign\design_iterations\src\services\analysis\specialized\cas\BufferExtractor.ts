/**
 * CAS Buffer Extractor
 * 
 * Specialized component for extracting buffers from various resource value formats.
 * Handles the complexity of different buffer representations in S4TK resources.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring.
 */

/**
 * Handles extraction of buffers from various resource value formats
 */
export class BufferExtractor {
    
    /**
     * Extracts buffer from various resource value formats
     * 
     * @param resourceValue - The resource value in various possible formats
     * @returns Buffer if extraction successful, null otherwise
     */
    public static extractBuffer(resourceValue: any): Buffer | null {
        if (!resourceValue) {
            return null;
        }
        
        // Handle cached buffer format
        if (resourceValue._bufferCache && resourceValue._bufferCache.buffer) {
            return resourceValue._bufferCache.buffer;
        }
        
        // Handle direct Buffer
        if (Buffer.isBuffer(resourceValue)) {
            return resourceValue;
        }
        
        // Handle object with buffer property
        if (resourceValue.buffer) {
            return resourceValue.buffer;
        }
        
        // Handle nested value with Buffer
        if (resourceValue.value && Buffer.isBuffer(resourceValue.value)) {
            return resourceValue.value;
        }
        
        // Try to convert to buffer as last resort
        try {
            return Buffer.from(resourceValue);
        } catch (error) {
            console.warn('Failed to extract buffer from resource value:', error);
            return null;
        }
    }
    
    /**
     * Validates that a buffer contains valid data for CAS analysis
     * 
     * @param buffer - Buffer to validate
     * @returns true if buffer appears to contain valid CAS data
     */
    public static isValidCASBuffer(buffer: Buffer): boolean {
        if (!buffer || buffer.length === 0) {
            return false;
        }
        
        // Check for SimData header
        if (buffer.length >= 4 && buffer.toString('utf8', 0, 4) === 'DATA') {
            return true;
        }
        
        // Check for other known CAS data patterns
        // Add more validation logic as needed
        
        return buffer.length > 10; // Minimum reasonable size
    }
    
    /**
     * Determines if buffer contains SimData format
     * 
     * @param buffer - Buffer to check
     * @returns true if buffer appears to be SimData format
     */
    public static isSimDataFormat(buffer: Buffer): boolean {
        return buffer.length >= 4 && buffer.toString('utf8', 0, 4) === 'DATA';
    }
}
