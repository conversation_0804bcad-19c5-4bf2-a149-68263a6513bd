/**
 * Test Enhanced Categorization Fix
 * 
 * Tests that the async/sync fix allows enhanced categorization to work properly
 */

import fs from 'fs';
import path from 'path';
import { DetailedAnalysisService } from '../services/analysis/core/DetailedAnalysisService';
import { QuickAnalysisService } from '../services/analysis/core/QuickAnalysisService';

/**
 * Test the categorization fix
 */
async function testCategorizationFix(): Promise<void> {
    console.log('🔧 Testing Enhanced Categorization Fix\n');
    console.log('=' .repeat(80));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    if (!fs.existsSync(modsPath)) {
        console.log('❌ Mods folder not found. Please check the path.');
        return;
    }
    
    // Find any .package files
    const files = fs.readdirSync(modsPath)
        .filter(file => file.endsWith('.package'))
        .slice(0, 3); // Test first 3 files
    
    if (files.length === 0) {
        console.log('❌ No .package files found in mods folder.');
        return;
    }
    
    console.log(`📁 Testing categorization fix with ${files.length} mod files:\n`);
    
    const quickAnalysisService = new QuickAnalysisService();
    const detailedAnalysisService = new DetailedAnalysisService();
    
    for (const fileName of files) {
        const filePath = path.join(modsPath, fileName);
        
        try {
            console.log(`🔍 Analyzing: ${fileName}`);
            
            // Read file
            const buffer = fs.readFileSync(filePath);
            
            // Quick analysis
            const quickResult = quickAnalysisService.analyze(buffer, fileName);
            
            // Detailed analysis (this should now work without async issues)
            const detailedResult = detailedAnalysisService.analyze(buffer, fileName, quickResult);
            
            // Check if we have enhanced object classification data
            const contentAnalysis = detailedResult.intelligence?.resourceIntelligence?.contentAnalysis;
            
            if (contentAnalysis) {
                console.log(`   ✅ Content Analysis Working!`);
                console.log(`   📊 Content Type: ${contentAnalysis.contentType}`);
                console.log(`   🎯 Confidence: ${contentAnalysis.confidence}%`);
                
                if (contentAnalysis.objectContent?.totalItems > 0) {
                    console.log(`   🏠 Object Content: ${contentAnalysis.objectContent.totalItems} items`);
                    
                    if (contentAnalysis.objectClassification) {
                        const objClass = contentAnalysis.objectClassification;
                        console.log(`   🎯 Enhanced Classification Found:`);
                        console.log(`      Category: ${objClass.category}`);
                        console.log(`      Specific Type: ${objClass.specificType}`);
                        console.log(`      Confidence: ${(objClass.confidence * 100).toFixed(1)}%`);
                        console.log(`      Detection Method: ${objClass.detectionMethod}`);
                        console.log(`   ✅ CATEGORIZATION FIX WORKING!`);
                    } else {
                        console.log(`   ⚠️  No enhanced object classification (but object content detected)`);
                    }
                } else if (contentAnalysis.casContent?.totalItems > 0) {
                    console.log(`   👗 CAS Content: ${contentAnalysis.casContent.totalItems} items`);
                } else {
                    console.log(`   ℹ️  No specific content detected`);
                }
            } else {
                console.log(`   ❌ Content analysis still not working`);
            }
            
            console.log('');
            
        } catch (error) {
            console.log(`   ❌ Error analyzing ${fileName}: ${error}`);
            console.log('');
        }
    }
    
    console.log('=' .repeat(80));
    console.log('📋 CATEGORIZATION FIX TEST SUMMARY');
    console.log('=' .repeat(80));
    console.log('✅ If you see "Enhanced Classification Found" above, the fix is working!');
    console.log('✅ The UI should now show proper categories instead of generic ones.');
    console.log('');
    console.log('🎯 Expected UI Results:');
    console.log('   • Furniture mods → "Furniture > [Specific Type]"');
    console.log('   • CAS mods → "Create-a-Sim > [Specific Type]"');
    console.log('   • No more generic "GENERAL" or incorrect "CREATE-A-SIM CONTENT"');
    console.log('');
    console.log('🚀 Test the actual UI now to confirm the fix!');
}

// Run the test
if (require.main === module) {
    testCategorizationFix().catch(console.error);
}

export { testCategorizationFix };
