/**
 * Test Content Analysis Service
 * 
 * Tests the new content-focused analysis system with real Sims 4 mods
 * to verify we're extracting useful information for mod organization.
 */

import * as fs from 'fs';
import * as path from 'path';
import { ContentAnalysisService, ModContentType } from '../services/analysis/content/ContentAnalysisService';

async function testContentAnalysis(): Promise<void> {
    console.log('🎯 Testing Content Analysis Service');
    console.log('=' .repeat(60));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    if (!fs.existsSync(modsPath)) {
        console.log('❌ Mods folder not found. Please check the path.');
        return;
    }
    
    // Get a variety of mod files for testing
    const files = fs.readdirSync(modsPath)
        .filter(file => file.endsWith('.package'))
        .slice(0, 10); // Test first 10 files
    
    console.log(`📁 Found ${files.length} package files to analyze`);
    console.log('');
    
    const contentAnalysisService = new ContentAnalysisService();
    const results: Array<{
        fileName: string;
        analysis: any;
        error?: string;
    }> = [];
    
    for (const fileName of files) {
        const filePath = path.join(modsPath, fileName);
        console.log(`🔍 Analyzing: ${fileName}`);
        
        try {
            const buffer = fs.readFileSync(filePath);
            const analysis = await contentAnalysisService.analyzeModContent(buffer, fileName);
            
            results.push({ fileName, analysis });
            
            // Display results
            console.log(`  📊 Content Type: ${analysis.contentType}`);
            console.log(`  🎯 Confidence: ${analysis.confidence}%`);
            console.log(`  📦 Total Items: ${analysis.totalItems}`);
            console.log(`  ⚡ Analysis Time: ${analysis.analysisTime.toFixed(2)}ms`);
            console.log(`  📋 Resources Analyzed: ${analysis.resourcesAnalyzed}`);
            
            // CAS Content Details
            if (analysis.casContent.totalItems > 0) {
                console.log(`  👗 CAS Content:`);
                console.log(`     Total: ${analysis.casContent.totalItems} items`);
                if (analysis.casContent.hair > 0) console.log(`     Hair: ${analysis.casContent.hair}`);
                if (analysis.casContent.clothing > 0) console.log(`     Clothing: ${analysis.casContent.clothing}`);
                if (analysis.casContent.accessories > 0) console.log(`     Accessories: ${analysis.casContent.accessories}`);
                if (analysis.casContent.makeup > 0) console.log(`     Makeup: ${analysis.casContent.makeup}`);
                
                // Age groups
                const ageGroups = [];
                if (analysis.casContent.ageGroups.infant > 0) ageGroups.push(`Infant: ${analysis.casContent.ageGroups.infant}`);
                if (analysis.casContent.ageGroups.toddler > 0) ageGroups.push(`Toddler: ${analysis.casContent.ageGroups.toddler}`);
                if (analysis.casContent.ageGroups.child > 0) ageGroups.push(`Child: ${analysis.casContent.ageGroups.child}`);
                if (analysis.casContent.ageGroups.teen > 0) ageGroups.push(`Teen: ${analysis.casContent.ageGroups.teen}`);
                if (analysis.casContent.ageGroups.youngAdult > 0) ageGroups.push(`YA: ${analysis.casContent.ageGroups.youngAdult}`);
                if (analysis.casContent.ageGroups.adult > 0) ageGroups.push(`Adult: ${analysis.casContent.ageGroups.adult}`);
                if (analysis.casContent.ageGroups.elder > 0) ageGroups.push(`Elder: ${analysis.casContent.ageGroups.elder}`);
                if (ageGroups.length > 0) console.log(`     Age Groups: ${ageGroups.join(', ')}`);
                
                // Genders
                const genders = [];
                if (analysis.casContent.genders.male > 0) genders.push(`Male: ${analysis.casContent.genders.male}`);
                if (analysis.casContent.genders.female > 0) genders.push(`Female: ${analysis.casContent.genders.female}`);
                if (analysis.casContent.genders.unisex > 0) genders.push(`Unisex: ${analysis.casContent.genders.unisex}`);
                if (genders.length > 0) console.log(`     Genders: ${genders.join(', ')}`);
                
                // Clothing categories
                const clothingCats = [];
                if (analysis.casContent.clothingCategories.everyday > 0) clothingCats.push(`Everyday: ${analysis.casContent.clothingCategories.everyday}`);
                if (analysis.casContent.clothingCategories.formal > 0) clothingCats.push(`Formal: ${analysis.casContent.clothingCategories.formal}`);
                if (analysis.casContent.clothingCategories.athletic > 0) clothingCats.push(`Athletic: ${analysis.casContent.clothingCategories.athletic}`);
                if (analysis.casContent.clothingCategories.sleep > 0) clothingCats.push(`Sleep: ${analysis.casContent.clothingCategories.sleep}`);
                if (analysis.casContent.clothingCategories.party > 0) clothingCats.push(`Party: ${analysis.casContent.clothingCategories.party}`);
                if (analysis.casContent.clothingCategories.swimwear > 0) clothingCats.push(`Swimwear: ${analysis.casContent.clothingCategories.swimwear}`);
                if (clothingCats.length > 0) console.log(`     Categories: ${clothingCats.join(', ')}`);
            }
            
            // Object Content Details
            if (analysis.objectContent.totalItems > 0) {
                console.log(`  🏠 Object Content:`);
                console.log(`     Total: ${analysis.objectContent.totalItems} items`);
                if (analysis.objectContent.furniture > 0) console.log(`     Furniture: ${analysis.objectContent.furniture}`);
                if (analysis.objectContent.decorations > 0) console.log(`     Decorations: ${analysis.objectContent.decorations}`);
            }
            
            // Gameplay Content Details
            if (analysis.gameplayContent.totalItems > 0) {
                console.log(`  🎮 Gameplay Content:`);
                console.log(`     Total: ${analysis.gameplayContent.totalItems} items`);
                if (analysis.gameplayContent.tuningMods > 0) console.log(`     Tuning Mods: ${analysis.gameplayContent.tuningMods}`);
                if (analysis.gameplayContent.scriptMods > 0) console.log(`     Script Mods: ${analysis.gameplayContent.scriptMods}`);
            }
            
            // Compatibility Info
            if (analysis.compatibility.gameVersion || analysis.compatibility.requiredPacks.length > 0) {
                console.log(`  🔧 Compatibility:`);
                if (analysis.compatibility.gameVersion) console.log(`     Game Version: ${analysis.compatibility.gameVersion}`);
                if (analysis.compatibility.requiredPacks.length > 0) console.log(`     Required Packs: ${analysis.compatibility.requiredPacks.join(', ')}`);
                if (analysis.compatibility.overriddenResources > 0) console.log(`     Overridden Resources: ${analysis.compatibility.overriddenResources}`);
                if (analysis.compatibility.customResources > 0) console.log(`     Custom Resources: ${analysis.compatibility.customResources}`);
            }
            
            console.log('');
            
        } catch (error) {
            console.log(`  ❌ Error: ${error.message}`);
            console.log('');
            results.push({ fileName, analysis: null, error: error.message });
        }
    }
    
    // Summary Report
    console.log('📊 Content Analysis Summary');
    console.log('=' .repeat(60));
    
    const successful = results.filter(r => !r.error);
    const withCAS = successful.filter(r => r.analysis.casContent.totalItems > 0);
    const withObjects = successful.filter(r => r.analysis.objectContent.totalItems > 0);
    const withGameplay = successful.filter(r => r.analysis.gameplayContent.totalItems > 0);
    const errors = results.filter(r => r.error);
    
    console.log(`📁 Total Files Analyzed: ${results.length}`);
    console.log(`✅ Successful Analyses: ${successful.length} (${(successful.length / results.length * 100).toFixed(1)}%)`);
    console.log(`👗 Files with CAS Content: ${withCAS.length} (${(withCAS.length / results.length * 100).toFixed(1)}%)`);
    console.log(`🏠 Files with Object Content: ${withObjects.length} (${(withObjects.length / results.length * 100).toFixed(1)}%)`);
    console.log(`🎮 Files with Gameplay Content: ${withGameplay.length} (${(withGameplay.length / results.length * 100).toFixed(1)}%)`);
    console.log(`❌ Analysis Errors: ${errors.length} (${(errors.length / results.length * 100).toFixed(1)}%)`);
    console.log('');
    
    // Content Type Distribution
    const contentTypes = new Map<string, number>();
    successful.forEach(result => {
        const type = result.analysis.contentType;
        contentTypes.set(type, (contentTypes.get(type) || 0) + 1);
    });
    
    console.log('📋 Content Type Distribution:');
    for (const [type, count] of contentTypes.entries()) {
        const percentage = (count / successful.length * 100).toFixed(1);
        console.log(`   ${type}: ${count} files (${percentage}%)`);
    }
    console.log('');
    
    // Performance Metrics
    if (successful.length > 0) {
        const avgTime = successful.reduce((sum, r) => sum + r.analysis.analysisTime, 0) / successful.length;
        const avgItems = successful.reduce((sum, r) => sum + r.analysis.totalItems, 0) / successful.length;
        const avgConfidence = successful.reduce((sum, r) => sum + r.analysis.confidence, 0) / successful.length;
        
        console.log('📈 Performance Metrics:');
        console.log(`   Average Analysis Time: ${avgTime.toFixed(2)}ms`);
        console.log(`   Average Items per Mod: ${avgItems.toFixed(1)}`);
        console.log(`   Average Confidence: ${avgConfidence.toFixed(1)}%`);
        console.log('');
    }
    
    // Best Examples
    const bestCAS = successful
        .filter(r => r.analysis.casContent.totalItems > 0)
        .sort((a, b) => b.analysis.casContent.totalItems - a.analysis.casContent.totalItems)
        .slice(0, 3);
    
    if (bestCAS.length > 0) {
        console.log('🏆 Top CAS Content Mods:');
        bestCAS.forEach((result, index) => {
            const analysis = result.analysis;
            console.log(`${index + 1}. ${result.fileName}`);
            console.log(`   ${analysis.casContent.totalItems} CAS items (${analysis.confidence}% confidence)`);
            console.log(`   Type: ${analysis.contentType}`);
            if (analysis.casContent.hair > 0) console.log(`   Hair: ${analysis.casContent.hair}`);
            if (analysis.casContent.clothing > 0) console.log(`   Clothing: ${analysis.casContent.clothing}`);
            if (analysis.casContent.accessories > 0) console.log(`   Accessories: ${analysis.casContent.accessories}`);
            console.log('');
        });
    }
    
    console.log('✅ Content Analysis Test Complete!');
}

// Run the test
if (require.main === module) {
    testContentAnalysis().catch(console.error);
}

export { testContentAnalysis };
