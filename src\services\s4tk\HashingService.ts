import { fnv32, formatResource<PERSON><PERSON>, formatResourceTGI, formatAsHexString } from '@s4tk/hashing';
import type { ResourceKey } from '@s4tk/models/types';

/**
 * Service for handling S4TK-based hashing and resource key formatting
 * Provides FNV hashing, resource key formatting, and TGI signature generation
 */
export class HashingService {
    
    /**
     * Generates a FNV-32 hash for resource content using S4TK's hashing algorithm
     * @param resource - Resource or content to hash
     * @returns Hex string representation of the hash
     */
    public generateResourceHash(resource: any): string {
        try {
            if (resource?.key) {
                // For resources with keys, create a consistent key-based hash
                const keyString = this.formatResourceKey(resource.key);
                return fnv32(keyString).toString(16);
            }
            
            if (resource?.buffer) {
                // For resources with buffers, hash the buffer content
                const bufferString = resource.buffer.toString('hex');
                return fnv32(bufferString).toString(16);
            }
            
            // Fallback: hash the string representation
            const content = typeof resource === 'string' ? resource : resource.toString();
            return fnv32(content).toString(16);
            
        } catch (error) {
            console.warn('Failed to generate FNV hash, falling back to basic hash:', error);
            // Fallback to basic hash if FNV fails
            return this.generateBasicHash(resource);
        }
    }
    
    /**
     * Formats a resource key using S4TK's formatting utilities
     * @param key - Resource key to format
     * @returns Formatted resource key string
     */
    public formatResourceKey(key: ResourceKey): string {
        try {
            return formatResourceKey(key);
        } catch (error) {
            console.warn('Failed to format resource key with S4TK, using fallback:', error);
            return `${key.type}:${key.group}:${key.instance}`;
        }
    }
    
    /**
     * Generates a TGI (Type-Group-Instance) signature string
     * @param type - Resource type
     * @param group - Resource group
     * @param instance - Resource instance
     * @returns Formatted TGI string
     */
    public generateTGISignature(type: number, group: number, instance: bigint): string {
        try {
            return formatResourceTGI(type, group, instance);
        } catch (error) {
            console.warn('Failed to format TGI with S4TK, using fallback:', error);
            return `${type.toString(16).toUpperCase()}:${group.toString(16).toUpperCase()}:${instance.toString(16).toUpperCase()}`;
        }
    }
    
    /**
     * Formats a number as a hex string with proper padding
     * @param value - Number to format
     * @param padding - Number of characters to pad to
     * @param uppercase - Whether to use uppercase hex
     * @returns Formatted hex string
     */
    public formatAsHex(value: number | bigint, padding: number = 8, uppercase: boolean = true): string {
        try {
            return formatAsHexString(value, padding, uppercase);
        } catch (error) {
            console.warn('Failed to format hex with S4TK, using fallback:', error);
            const hex = value.toString(16);
            const formatted = hex.padStart(padding, '0');
            return uppercase ? formatted.toUpperCase() : formatted;
        }
    }
    
    /**
     * Creates enhanced resource metadata with proper formatting
     * @param key - Resource key
     * @returns Enhanced metadata object
     */
    public createResourceMetadata(key: ResourceKey): {
        formattedKey: string;
        tgiString: string;
        typeHex: string;
        groupHex: string;
        instanceHex: string;
    } {
        return {
            formattedKey: this.formatResourceKey(key),
            tgiString: this.generateTGISignature(key.type, key.group, key.instance),
            typeHex: this.formatAsHex(key.type, 8),
            groupHex: this.formatAsHex(key.group, 8),
            instanceHex: this.formatAsHex(key.instance, 16)
        };
    }
    
    /**
     * Generates a hash for conflict detection
     * @param type - Resource type
     * @param group - Resource group
     * @param instance - Resource instance
     * @param content - Optional content to include in hash
     * @returns Conflict detection hash
     */
    public generateConflictHash(type: number, group: number, instance: bigint, content?: string): string {
        const tgiString = this.generateTGISignature(type, group, instance);
        const hashInput = content ? `${tgiString}:${content}` : tgiString;
        return fnv32(hashInput).toString(16);
    }
    
    /**
     * Fallback hash generation for when S4TK hashing fails
     * @param resource - Resource to hash
     * @returns Basic hash string
     */
    private generateBasicHash(resource: any): string {
        const content = resource.toString();
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
    }
}

// Export singleton instance
export const hashingService = new HashingService();