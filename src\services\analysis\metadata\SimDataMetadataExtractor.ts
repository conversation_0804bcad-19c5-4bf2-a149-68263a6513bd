/**
 * SimData Resource Metadata Extractor
 * 
 * Extracts structured metadata from SimData resources which often contain
 * more reliable mod information than StringTables.
 */

import { SimDataResource } from '@s4tk/models';
import { DEFAULT_METADATA_CONFIG, type MetadataExtractionConfig } from '../../../config/metadata-extraction.config';

export interface SimDataMetadata {
    readonly author?: string;
    readonly modName?: string;
    readonly version?: string;
    readonly description?: string;
    readonly confidence: number;
    readonly source: 'simdata';
    readonly dataType?: string;
    readonly schemaHash?: string;
    readonly fieldCount: number;
    readonly processingTime: number;
}

export interface SimDataField {
    readonly name: string;
    readonly value: any;
    readonly type: string;
}

/**
 * SimData resource metadata extractor
 */
export class SimDataMetadataExtractor {
    private readonly config: MetadataExtractionConfig;
    
    constructor(config: Partial<MetadataExtractionConfig> = {}) {
        this.config = {
            ...DEFAULT_METADATA_CONFIG,
            ...config
        };
    }
    
    /**
     * Extracts metadata from SimData resource
     */
    async extractMetadata(simDataBuffer: Buffer): Promise<SimDataMetadata | null> {
        const startTime = performance.now();
        
        try {
            // Parse SimData using S4TK
            const simData = SimDataResource.from(simDataBuffer);
            
            if (!simData || !simData.instance) {
                return null;
            }
            
            // Extract fields from SimData
            const fields = this.extractFields(simData);
            
            // Extract metadata from fields
            const metadata = this.extractMetadataFromFields(fields);
            
            const processingTime = performance.now() - startTime;
            
            if (metadata) {
                return {
                    ...metadata,
                    source: 'simdata',
                    schemaHash: simData.instance.schema?.hash?.toString(16),
                    fieldCount: fields.length,
                    processingTime
                };
            }
            
            return null;
            
        } catch (error) {
            console.warn('[SimDataMetadataExtractor] Extraction failed:', error);
            return null;
        }
    }
    
    /**
     * Extracts fields from SimData instance
     */
    private extractFields(simData: SimDataResource): SimDataField[] {
        const fields: SimDataField[] = [];
        
        try {
            if (!simData.instance || !simData.instance.row) {
                return fields;
            }
            
            const row = simData.instance.row;
            
            // Extract all fields from the row
            for (const [key, value] of Object.entries(row)) {
                if (key && value !== undefined && value !== null) {
                    fields.push({
                        name: key.toString(),
                        value: value,
                        type: this.getValueType(value)
                    });
                }
            }
            
        } catch (error) {
            console.warn('[SimDataMetadataExtractor] Error extracting fields:', error);
        }
        
        return fields;
    }
    
    /**
     * Extracts metadata from SimData fields
     */
    private extractMetadataFromFields(fields: SimDataField[]): Omit<SimDataMetadata, 'source' | 'schemaHash' | 'fieldCount' | 'processingTime'> | null {
        const metadata: {
            author?: string;
            modName?: string;
            version?: string;
            description?: string;
            confidence: number;
            dataType?: string;
        } = {
            confidence: 0
        };
        
        // Look for metadata in known field names
        for (const field of fields) {
            const fieldName = field.name.toLowerCase();
            const fieldValue = this.extractStringValue(field.value);
            
            if (!fieldValue || !this.isValidMetadataValue(fieldValue)) {
                continue;
            }
            
            // Check against known metadata keys
            for (const metadataKey of this.config.simData.metadataKeys) {
                if (fieldName.includes(metadataKey.toLowerCase())) {
                    this.assignMetadataValue(metadata, metadataKey, fieldValue);
                    break;
                }
            }
            
            // Special handling for common field patterns
            if (fieldName.includes('creator') || fieldName.includes('author')) {
                if (!metadata.author && this.looksLikeAuthor(fieldValue)) {
                    metadata.author = this.sanitizeValue(fieldValue);
                }
            }
            
            if (fieldName.includes('title') || fieldName.includes('name')) {
                if (!metadata.modName && this.looksLikeModName(fieldValue)) {
                    metadata.modName = this.sanitizeValue(fieldValue);
                }
            }
            
            if (fieldName.includes('version') || fieldName.includes('ver')) {
                if (!metadata.version && this.looksLikeVersion(fieldValue)) {
                    metadata.version = this.sanitizeValue(fieldValue);
                }
            }
            
            if (fieldName.includes('description') || fieldName.includes('desc')) {
                if (!metadata.description && this.looksLikeDescription(fieldValue)) {
                    metadata.description = this.sanitizeValue(fieldValue);
                }
            }
        }
        
        // Try to infer data type from field names
        metadata.dataType = this.inferDataType(fields);
        
        // Calculate confidence
        metadata.confidence = this.calculateSimDataConfidence(metadata, fields.length);
        
        // Only return if we found meaningful metadata
        if (metadata.author || metadata.modName || metadata.version || metadata.description) {
            return metadata;
        }
        
        return null;
    }
    
    /**
     * Assigns metadata value based on key type
     */
    private assignMetadataValue(
        metadata: { author?: string; modName?: string; version?: string; description?: string },
        key: string,
        value: string
    ): void {
        const sanitizedValue = this.sanitizeValue(value);
        
        switch (key.toLowerCase()) {
            case 'author':
            case 'creator':
                if (!metadata.author && this.looksLikeAuthor(sanitizedValue)) {
                    metadata.author = sanitizedValue;
                }
                break;
                
            case 'mod_name':
            case 'title':
            case 'name':
                if (!metadata.modName && this.looksLikeModName(sanitizedValue)) {
                    metadata.modName = sanitizedValue;
                }
                break;
                
            case 'version':
                if (!metadata.version && this.looksLikeVersion(sanitizedValue)) {
                    metadata.version = sanitizedValue;
                }
                break;
                
            case 'description':
                if (!metadata.description && this.looksLikeDescription(sanitizedValue)) {
                    metadata.description = sanitizedValue;
                }
                break;
        }
    }
    
    /**
     * Extracts string value from various data types
     */
    private extractStringValue(value: any): string | null {
        if (typeof value === 'string') {
            return value;
        }
        
        if (typeof value === 'number') {
            return value.toString();
        }
        
        if (typeof value === 'boolean') {
            return value.toString();
        }
        
        if (value && typeof value === 'object') {
            // Handle LocalizedString or similar objects
            if (value.value && typeof value.value === 'string') {
                return value.value;
            }
            
            if (value.text && typeof value.text === 'string') {
                return value.text;
            }
            
            // Try to stringify simple objects
            try {
                const stringified = JSON.stringify(value);
                if (stringified.length < 200) {
                    return stringified;
                }
            } catch {
                // Ignore JSON errors
            }
        }
        
        return null;
    }
    
    /**
     * Gets the type of a value
     */
    private getValueType(value: any): string {
        if (value === null) return 'null';
        if (value === undefined) return 'undefined';
        
        const type = typeof value;
        if (type !== 'object') return type;
        
        if (Array.isArray(value)) return 'array';
        if (value.constructor && value.constructor.name) return value.constructor.name;
        
        return 'object';
    }
    
    /**
     * Infers data type from field names
     */
    private inferDataType(fields: SimDataField[]): string | undefined {
        const fieldNames = fields.map(f => f.name.toLowerCase());
        
        // Common SimData types based on field patterns
        if (fieldNames.some(name => name.includes('buff') || name.includes('effect'))) {
            return 'buff';
        }
        
        if (fieldNames.some(name => name.includes('trait') || name.includes('personality'))) {
            return 'trait';
        }
        
        if (fieldNames.some(name => name.includes('interaction') || name.includes('social'))) {
            return 'interaction';
        }
        
        if (fieldNames.some(name => name.includes('object') || name.includes('catalog'))) {
            return 'object';
        }
        
        if (fieldNames.some(name => name.includes('cas') || name.includes('outfit'))) {
            return 'cas';
        }
        
        return undefined;
    }
    
    /**
     * Validates metadata value
     */
    private isValidMetadataValue(value: string): boolean {
        return value && 
               value.length > 0 && 
               value.length <= this.config.simData.maxValueLength &&
               value.trim().length > 0;
    }
    
    /**
     * Checks if value looks like an author name
     */
    private looksLikeAuthor(value: string): boolean {
        return value.length >= 2 && 
               value.length <= 30 &&
               /[a-zA-Z]/.test(value) &&
               !/^\d+$/.test(value);
    }
    
    /**
     * Checks if value looks like a mod name
     */
    private looksLikeModName(value: string): boolean {
        return value.length >= 3 && 
               value.length <= 100 &&
               /[a-zA-Z]/.test(value) &&
               !/^\d+$/.test(value);
    }
    
    /**
     * Checks if value looks like a version
     */
    private looksLikeVersion(value: string): boolean {
        return /^\d+(\.\d+)*$/.test(value) || 
               /^v?\d+(\.\d+)*/.test(value);
    }
    
    /**
     * Checks if value looks like a description
     */
    private looksLikeDescription(value: string): boolean {
        return value.length >= 10 && 
               value.length <= 500 &&
               /[a-zA-Z]/.test(value);
    }
    
    /**
     * Sanitizes metadata value
     */
    private sanitizeValue(value: string): string {
        return value
            .trim()
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control chars
            .substring(0, this.config.simData.maxValueLength);
    }
    
    /**
     * Calculates confidence for SimData metadata
     */
    private calculateSimDataConfidence(
        metadata: { author?: string; modName?: string; version?: string; description?: string },
        fieldCount: number
    ): number {
        let confidence = 30; // Base confidence for successful SimData parsing
        
        if (metadata.author) confidence += 25;
        if (metadata.modName) confidence += 25;
        if (metadata.version) confidence += 20;
        if (metadata.description) confidence += 15;
        
        // Boost for having more fields (more structured data)
        if (fieldCount > 5) confidence += 10;
        if (fieldCount > 15) confidence += 10;
        
        return Math.min(confidence, 100);
    }
}
