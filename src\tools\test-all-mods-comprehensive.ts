#!/usr/bin/env node

/**
 * Comprehensive All Mods Test
 * 
 * Tests Phase 4A intelligence integration against ALL mods in the assets folder
 * to provide a comprehensive accuracy assessment of the system.
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface ComprehensiveTestResult {
    filePath: string;
    fileType: string;
    fileExtension: string;
    
    // Phase 3A: Metadata
    author?: string;
    version?: string;
    modName?: string;
    metadataSource?: string;
    metadataConfidence?: number;
    
    // Phase 4A: Intelligence
    hasIntelligence: boolean;
    qualityScore?: number;
    riskLevel?: string;
    dependencyCount?: number;
    hasResourceIntelligence: boolean;
    
    // Performance
    processingTime: number;
    error?: string;
}

class ComprehensiveModTester {
    private analysisService: PackageAnalysisService;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
    }
    
    /**
     * Tests all mods in the assets folder
     */
    public async testAllMods(): Promise<void> {
        console.log('🔍 COMPREHENSIVE MOD ANALYSIS - ALL ASSETS');
        console.log('Testing Phase 3A Metadata + Phase 4A Intelligence on ALL mods\n');
        
        const assetsDir = path.join(process.cwd(), 'assets');
        
        if (!fs.existsSync(assetsDir)) {
            console.error('❌ Assets directory not found:', assetsDir);
            return;
        }
        
        const files = fs.readdirSync(assetsDir)
            .filter(file => file.endsWith('.package') || file.endsWith('.ts4script'))
            .sort(); // Sort for consistent output
        
        console.log(`📁 Found ${files.length} mod files to analyze\n`);
        
        const results: ComprehensiveTestResult[] = [];
        let processedCount = 0;
        
        for (const file of files) {
            const filePath = path.join(assetsDir, file);
            processedCount++;
            
            console.log(`[${processedCount}/${files.length}] Analyzing: ${file}`);
            const result = await this.testFile(filePath);
            results.push(result);
        }
        
        console.log('\n' + '='.repeat(80));
        this.generateComprehensiveReport(results);
    }
    
    /**
     * Tests a single mod file
     */
    private async testFile(filePath: string): Promise<ComprehensiveTestResult> {
        const fileName = path.basename(filePath);
        const fileExtension = path.extname(fileName);
        
        const startTime = Date.now();
        
        try {
            const buffer = fs.readFileSync(filePath);
            const analysis = await this.analysisService.detailedAnalyzeAsync(buffer, filePath);
            
            const processingTime = Date.now() - startTime;
            
            const result: ComprehensiveTestResult = {
                filePath: fileName,
                fileType: analysis.fileType,
                fileExtension,
                
                // Phase 3A: Metadata
                author: analysis.metadata.author,
                version: analysis.metadata.version,
                modName: analysis.metadata.modName,
                metadataSource: analysis.metadata.metadataSource,
                metadataConfidence: analysis.metadata.metadataConfidence,
                
                // Phase 4A: Intelligence
                hasIntelligence: !!analysis.intelligence,
                qualityScore: analysis.intelligence?.qualityAssessment?.overallScore,
                riskLevel: analysis.intelligence?.dependencies?.riskLevel,
                dependencyCount: analysis.intelligence?.dependencies?.dependencies?.length || 0,
                hasResourceIntelligence: !!analysis.intelligence?.resourceIntelligence,
                
                processingTime
            };
            
            // Brief status output
            const authorStatus = result.author ? '✅' : '❌';
            const intelligenceStatus = result.hasIntelligence ? '✅' : '❌';
            const qualityScore = result.qualityScore ? `${result.qualityScore}/100` : 'N/A';
            
            console.log(`   📝 Author: ${authorStatus} | 🧠 Intelligence: ${intelligenceStatus} | ⭐ Quality: ${qualityScore} | ⏱️ ${result.processingTime}ms`);
            
            return result;
            
        } catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            console.log(`   ❌ Error: ${errorMessage} | ⏱️ ${processingTime}ms`);
            
            return {
                filePath: fileName,
                fileType: 'unknown',
                fileExtension,
                hasIntelligence: false,
                hasResourceIntelligence: false,
                processingTime,
                error: errorMessage
            };
        }
    }
    
    /**
     * Generates comprehensive analysis report
     */
    private generateComprehensiveReport(results: ComprehensiveTestResult[]): void {
        console.log('🔍 COMPREHENSIVE MOD ANALYSIS REPORT');
        console.log('==================================================\n');
        
        // Basic statistics
        const totalFiles = results.length;
        const successfulFiles = results.filter(r => !r.error).length;
        const scriptFiles = results.filter(r => r.fileExtension === '.ts4script');
        const packageFiles = results.filter(r => r.fileExtension === '.package');
        
        console.log('📊 OVERVIEW:');
        console.log(`   Total Files Analyzed: ${totalFiles}`);
        console.log(`   Successful Analysis: ${successfulFiles}/${totalFiles} (${Math.round((successfulFiles/totalFiles)*100)}%)`);
        console.log(`   Script Files (.ts4script): ${scriptFiles.length}`);
        console.log(`   Package Files (.package): ${packageFiles.length}\n`);
        
        // Phase 3A: Metadata Analysis
        this.analyzeMetadataAccuracy(results, scriptFiles, packageFiles);
        
        // Phase 4A: Intelligence Analysis
        this.analyzeIntelligenceAccuracy(results, scriptFiles, packageFiles);
        
        // Performance Analysis
        this.analyzePerformance(results);
        
        // Quality Assessment
        this.analyzeQualityScores(results);
        
        // Error Analysis
        this.analyzeErrors(results);
        
        // Overall Assessment
        this.generateOverallAssessment(results);
    }
    
    private analyzeMetadataAccuracy(results: ComprehensiveTestResult[], scriptFiles: ComprehensiveTestResult[], packageFiles: ComprehensiveTestResult[]): void {
        console.log('📝 PHASE 3A - METADATA EXTRACTION ACCURACY:');
        
        // Overall metadata stats
        const filesWithAuthor = results.filter(r => r.author).length;
        const filesWithVersion = results.filter(r => r.version).length;
        const filesWithModName = results.filter(r => r.modName).length;
        
        const authorPercentage = Math.round((filesWithAuthor / results.length) * 100);
        const versionPercentage = Math.round((filesWithVersion / results.length) * 100);
        const modNamePercentage = Math.round((filesWithModName / results.length) * 100);
        
        console.log(`   Overall Author Detection: ${filesWithAuthor}/${results.length} (${authorPercentage}%)`);
        console.log(`   Overall Version Detection: ${filesWithVersion}/${results.length} (${versionPercentage}%)`);
        console.log(`   Overall Mod Name Detection: ${filesWithModName}/${results.length} (${modNamePercentage}%)`);
        
        // Script files breakdown
        const scriptWithAuthor = scriptFiles.filter(r => r.author).length;
        const scriptWithVersion = scriptFiles.filter(r => r.version).length;
        console.log(`   Script Author Detection: ${scriptWithAuthor}/${scriptFiles.length} (${Math.round((scriptWithAuthor/scriptFiles.length)*100)}%)`);
        console.log(`   Script Version Detection: ${scriptWithVersion}/${scriptFiles.length} (${Math.round((scriptWithVersion/scriptFiles.length)*100)}%)`);
        
        // Package files breakdown
        const packageWithAuthor = packageFiles.filter(r => r.author).length;
        const packageWithVersion = packageFiles.filter(r => r.version).length;
        console.log(`   Package Author Detection: ${packageWithAuthor}/${packageFiles.length} (${Math.round((packageWithAuthor/packageFiles.length)*100)}%)`);
        console.log(`   Package Version Detection: ${packageWithVersion}/${packageFiles.length} (${Math.round((packageWithVersion/packageFiles.length)*100)}%)\n`);
    }
    
    private analyzeIntelligenceAccuracy(results: ComprehensiveTestResult[], scriptFiles: ComprehensiveTestResult[], packageFiles: ComprehensiveTestResult[]): void {
        console.log('🧠 PHASE 4A - INTELLIGENCE INTEGRATION ACCURACY:');
        
        const filesWithIntelligence = results.filter(r => r.hasIntelligence).length;
        const filesWithResourceIntelligence = results.filter(r => r.hasResourceIntelligence).length;
        const filesWithQuality = results.filter(r => r.qualityScore !== undefined).length;
        
        const intelligencePercentage = Math.round((filesWithIntelligence / results.length) * 100);
        const resourceIntelligencePercentage = Math.round((filesWithResourceIntelligence / results.length) * 100);
        const qualityPercentage = Math.round((filesWithQuality / results.length) * 100);
        
        console.log(`   Intelligence Data: ${filesWithIntelligence}/${results.length} (${intelligencePercentage}%)`);
        console.log(`   Resource Intelligence: ${filesWithResourceIntelligence}/${results.length} (${resourceIntelligencePercentage}%)`);
        console.log(`   Quality Assessment: ${filesWithQuality}/${results.length} (${qualityPercentage}%)`);
        
        // Breakdown by file type
        const scriptIntelligence = scriptFiles.filter(r => r.hasIntelligence).length;
        const packageIntelligence = packageFiles.filter(r => r.hasIntelligence).length;
        console.log(`   Script Intelligence: ${scriptIntelligence}/${scriptFiles.length} (${Math.round((scriptIntelligence/scriptFiles.length)*100)}%)`);
        console.log(`   Package Intelligence: ${packageIntelligence}/${packageFiles.length} (${Math.round((packageIntelligence/packageFiles.length)*100)}%)\n`);
    }
    
    private analyzePerformance(results: ComprehensiveTestResult[]): void {
        console.log('⏱️ PERFORMANCE ANALYSIS:');
        
        const processingTimes = results.map(r => r.processingTime);
        const avgTime = Math.round(processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length);
        const maxTime = Math.max(...processingTimes);
        const minTime = Math.min(...processingTimes);
        
        console.log(`   Average Processing Time: ${avgTime}ms`);
        console.log(`   Maximum Processing Time: ${maxTime}ms`);
        console.log(`   Minimum Processing Time: ${minTime}ms`);
        console.log(`   Target: <10ms average ✅ ${avgTime <= 10 ? 'ACHIEVED' : 'NEEDS OPTIMIZATION'}\n`);
    }
    
    private analyzeQualityScores(results: ComprehensiveTestResult[]): void {
        const qualityScores = results.filter(r => r.qualityScore !== undefined).map(r => r.qualityScore!);
        
        if (qualityScores.length > 0) {
            console.log('⭐ QUALITY ASSESSMENT ANALYSIS:');
            
            const avgQuality = Math.round(qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length);
            const maxQuality = Math.max(...qualityScores);
            const minQuality = Math.min(...qualityScores);
            
            console.log(`   Average Quality Score: ${avgQuality}/100`);
            console.log(`   Quality Range: ${minQuality} - ${maxQuality}`);
            console.log(`   Files with Quality Data: ${qualityScores.length}/${results.length}\n`);
        }
    }
    
    private analyzeErrors(results: ComprehensiveTestResult[]): void {
        const errors = results.filter(r => r.error);
        
        if (errors.length > 0) {
            console.log('❌ ERROR ANALYSIS:');
            console.log(`   Files with Errors: ${errors.length}/${results.length} (${Math.round((errors.length/results.length)*100)}%)`);
            
            errors.forEach(error => {
                console.log(`   • ${error.filePath}: ${error.error}`);
            });
            console.log();
        }
    }
    
    private generateOverallAssessment(results: ComprehensiveTestResult[]): void {
        console.log('🎯 OVERALL SYSTEM ASSESSMENT:');
        
        const successRate = Math.round((results.filter(r => !r.error).length / results.length) * 100);
        const authorRate = Math.round((results.filter(r => r.author).length / results.length) * 100);
        const intelligenceRate = Math.round((results.filter(r => r.hasIntelligence).length / results.length) * 100);
        const avgTime = Math.round(results.reduce((sum, r) => sum + r.processingTime, 0) / results.length);
        
        console.log(`   System Reliability: ${successRate}%`);
        console.log(`   Metadata Accuracy: ${authorRate}% author detection`);
        console.log(`   Intelligence Coverage: ${intelligenceRate}%`);
        console.log(`   Performance: ${avgTime}ms average`);
        
        const overallSuccess = successRate >= 95 && authorRate >= 80 && intelligenceRate >= 90 && avgTime <= 10;
        
        if (overallSuccess) {
            console.log('\n🏆 SYSTEM STATUS: EXCELLENT');
            console.log('   ✅ High reliability and accuracy');
            console.log('   ✅ Comprehensive intelligence coverage');
            console.log('   ✅ Excellent performance');
            console.log('   🚀 Ready for production use');
        } else {
            console.log('\n📈 SYSTEM STATUS: GOOD WITH AREAS FOR IMPROVEMENT');
            if (successRate < 95) console.log('   📋 Improve system reliability');
            if (authorRate < 80) console.log('   📋 Enhance metadata extraction');
            if (intelligenceRate < 90) console.log('   📋 Improve intelligence coverage');
            if (avgTime > 10) console.log('   📋 Optimize performance');
        }
    }
}

// Run the comprehensive test
async function main() {
    const tester = new ComprehensiveModTester();
    await tester.testAllMods();
}

if (require.main === module) {
    main().catch(console.error);
}
