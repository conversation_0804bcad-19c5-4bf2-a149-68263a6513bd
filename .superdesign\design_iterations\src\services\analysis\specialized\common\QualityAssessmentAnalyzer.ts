/**
 * Quality Assessment Analyzer
 * 
 * Comprehensive quality analysis inspired by Llama-Logic's integrity verification
 * and professional mod development standards.
 */

import * as crypto from 'crypto';
import { Package } from '@s4tk/models';

export interface QualityAssessment {
    overallScore: number; // 0-100
    integrity: IntegrityCheck;
    structure: StructureAnalysis;
    standards: StandardsCompliance;
    performance: PerformanceAnalysis;
    security: SecurityAnalysis;
    recommendations: string[];
    warnings: string[];
    errors: string[];
}

export interface IntegrityCheck {
    fileHash: string;
    isCorrupted: boolean;
    hasValidStructure: boolean;
    resourceIntegrity: number; // 0-100
    checksumValid: boolean;
}

export interface StructureAnalysis {
    hasProperNaming: boolean;
    followsConventions: boolean;
    organizationScore: number; // 0-100
    resourceDistribution: 'balanced' | 'unbalanced' | 'unknown';
    duplicateResources: number;
}

export interface StandardsCompliance {
    followsS4Standards: boolean;
    hasManifest: boolean;
    hasDocumentation: boolean;
    localizationSupport: boolean;
    versioningScheme: 'semantic' | 'numeric' | 'date' | 'none';
    complianceScore: number; // 0-100
}

export interface PerformanceAnalysis {
    estimatedLoadTime: number; // milliseconds
    memoryFootprint: number; // bytes
    resourceEfficiency: number; // 0-100
    optimizationLevel: 'poor' | 'fair' | 'good' | 'excellent';
    bottlenecks: string[];
}

export interface SecurityAnalysis {
    riskLevel: 'low' | 'medium' | 'high';
    suspiciousPatterns: string[];
    codeComplexity: number; // 0-100
    injectionRisk: number; // 0-100
    trustScore: number; // 0-100
}

/**
 * Provides comprehensive quality assessment for mods
 */
export class QualityAssessmentAnalyzer {
    
    /**
     * Perform comprehensive quality assessment
     */
    public static assessQuality(buffer: Buffer, fileName: string, isPackage: boolean): QualityAssessment {
        const assessment: QualityAssessment = {
            overallScore: 0,
            integrity: this.checkIntegrity(buffer, fileName),
            structure: this.analyzeStructure(buffer, fileName, isPackage),
            standards: this.checkStandardsCompliance(buffer, fileName, isPackage),
            performance: this.analyzePerformance(buffer, isPackage),
            security: this.analyzeSecurity(buffer, fileName, isPackage),
            recommendations: [],
            warnings: [],
            errors: []
        };
        
        // Calculate overall score
        assessment.overallScore = this.calculateOverallScore(assessment);
        
        // Generate recommendations
        assessment.recommendations = this.generateRecommendations(assessment);
        
        return assessment;
    }
    
    /**
     * Check file integrity
     */
    private static checkIntegrity(buffer: Buffer, fileName: string): IntegrityCheck {
        const hash = crypto.createHash('sha256').update(buffer).digest('hex');
        
        let isCorrupted = false;
        let hasValidStructure = true;
        let resourceIntegrity = 100;
        
        try {
            if (fileName.endsWith('.package')) {
                // Try to parse as S4TK package
                Package.from(buffer);
            } else if (fileName.endsWith('.ts4script')) {
                // Check ZIP structure
                const zipSignature = buffer.subarray(0, 4);
                if (!zipSignature.equals(Buffer.from([0x50, 0x4B, 0x03, 0x04]))) {
                    isCorrupted = true;
                    hasValidStructure = false;
                }
            }
        } catch (error) {
            isCorrupted = true;
            hasValidStructure = false;
            resourceIntegrity = 0;
        }
        
        return {
            fileHash: hash,
            isCorrupted,
            hasValidStructure,
            resourceIntegrity,
            checksumValid: !isCorrupted
        };
    }
    
    /**
     * Analyze file structure
     */
    private static analyzeStructure(buffer: Buffer, fileName: string, isPackage: boolean): StructureAnalysis {
        const structure: StructureAnalysis = {
            hasProperNaming: this.checkNamingConventions(fileName),
            followsConventions: false,
            organizationScore: 50,
            resourceDistribution: 'unknown',
            duplicateResources: 0
        };
        
        if (isPackage) {
            try {
                const s4tkPackage = Package.from(buffer);
                structure.followsConventions = this.checkPackageConventions(s4tkPackage);
                structure.organizationScore = this.calculateOrganizationScore(s4tkPackage);
                structure.duplicateResources = this.findDuplicateResources(s4tkPackage);
                structure.resourceDistribution = this.analyzeResourceDistribution(s4tkPackage);
            } catch (error) {
                structure.organizationScore = 0;
            }
        } else {
            structure.followsConventions = this.checkScriptConventions(buffer);
            structure.organizationScore = this.calculateScriptOrganizationScore(buffer);
        }
        
        return structure;
    }
    
    /**
     * Check naming conventions
     */
    private static checkNamingConventions(fileName: string): boolean {
        // Check for proper naming patterns
        const goodPatterns = [
            /^[A-Za-z][A-Za-z0-9_]*_[A-Za-z][A-Za-z0-9_]*/, // Author_ModName
            /^\[[A-Za-z][A-Za-z0-9_]*\]/, // [Author] ModName
        ];
        
        const badPatterns = [
            /\s/, // No spaces
            /[<>:"|?*]/, // No invalid characters
            /^[0-9]/, // Don't start with numbers
        ];
        
        const hasGoodPattern = goodPatterns.some(pattern => pattern.test(fileName));
        const hasBadPattern = badPatterns.some(pattern => pattern.test(fileName));
        
        return hasGoodPattern && !hasBadPattern;
    }
    
    /**
     * Check package conventions
     */
    private static checkPackageConventions(s4tkPackage: Package): boolean {
        // Check for proper resource organization
        let score = 0;
        const totalChecks = 5;
        
        // Check 1: Reasonable resource count
        if (s4tkPackage.size > 0 && s4tkPackage.size < 1000) score++;
        
        // Check 2: No duplicate resource keys
        const resourceKeys = new Set();
        let hasDuplicates = false;
        for (const entry of s4tkPackage.entries.values()) {
            const keyStr = entry.key.toString();
            if (resourceKeys.has(keyStr)) {
                hasDuplicates = true;
                break;
            }
            resourceKeys.add(keyStr);
        }
        if (!hasDuplicates) score++;
        
        // Check 3: Proper resource types
        const hasValidTypes = Array.from(s4tkPackage.entries.values())
            .every(entry => entry.key.type > 0);
        if (hasValidTypes) score++;
        
        // Check 4: Reasonable file sizes
        const hasReasonableSizes = Array.from(s4tkPackage.entries.values())
            .every(entry => entry.value.byteLength < 50 * 1024 * 1024); // 50MB max per resource
        if (hasReasonableSizes) score++;
        
        // Check 5: No empty resources
        const hasNoEmptyResources = Array.from(s4tkPackage.entries.values())
            .every(entry => entry.value.byteLength > 0);
        if (hasNoEmptyResources) score++;
        
        return score >= totalChecks * 0.8; // 80% pass rate
    }
    
    /**
     * Check script conventions
     */
    private static checkScriptConventions(buffer: Buffer): boolean {
        // Check ZIP structure and Python file organization
        const bufferStr = buffer.toString('binary');
        
        // Check for proper Python file structure
        const hasPythonFiles = /\.py[co]?/.test(bufferStr);
        const hasReasonableStructure = bufferStr.length > 100; // Not empty
        const hasValidZipStructure = bufferStr.startsWith('PK');
        
        return hasPythonFiles && hasReasonableStructure && hasValidZipStructure;
    }
    
    /**
     * Calculate organization score
     */
    private static calculateOrganizationScore(s4tkPackage: Package): number {
        let score = 50; // Base score
        
        // Factor 1: Resource count balance
        const resourceCount = s4tkPackage.size;
        if (resourceCount > 5 && resourceCount < 200) score += 20;
        else if (resourceCount >= 200) score -= 10;
        
        // Factor 2: Resource type diversity
        const resourceTypes = new Set();
        for (const entry of s4tkPackage.entries.values()) {
            resourceTypes.add(entry.key.type);
        }
        
        if (resourceTypes.size > 1 && resourceTypes.size < 10) score += 15;
        
        // Factor 3: Size distribution
        const sizes = Array.from(s4tkPackage.entries.values())
            .map(entry => entry.value.byteLength);
        const avgSize = sizes.reduce((sum, size) => sum + size, 0) / sizes.length;
        const variance = sizes.reduce((sum, size) => sum + Math.pow(size - avgSize, 2), 0) / sizes.length;
        
        if (variance < avgSize * 2) score += 15; // Low variance is good
        
        return Math.max(0, Math.min(100, score));
    }
    
    /**
     * Calculate script organization score
     */
    private static calculateScriptOrganizationScore(buffer: Buffer): number {
        const bufferStr = buffer.toString('binary');
        let score = 50;
        
        // Check for multiple Python files (good organization)
        const pythonFileCount = (bufferStr.match(/\.py[co]?/g) || []).length;
        if (pythonFileCount > 1 && pythonFileCount < 50) score += 20;
        
        // Check for reasonable file size
        if (buffer.length > 1000 && buffer.length < 10 * 1024 * 1024) score += 15;
        
        // Check for directory structure
        if (bufferStr.includes('/') || bufferStr.includes('\\')) score += 15;
        
        return Math.max(0, Math.min(100, score));
    }
    
    /**
     * Find duplicate resources
     */
    private static findDuplicateResources(s4tkPackage: Package): number {
        const resourceKeys = new Map<string, number>();
        
        for (const entry of s4tkPackage.entries.values()) {
            const keyStr = entry.key.toString();
            resourceKeys.set(keyStr, (resourceKeys.get(keyStr) || 0) + 1);
        }
        
        return Array.from(resourceKeys.values()).filter(count => count > 1).length;
    }
    
    /**
     * Analyze resource distribution
     */
    private static analyzeResourceDistribution(s4tkPackage: Package): 'balanced' | 'unbalanced' | 'unknown' {
        const typeCount = new Map<number, number>();
        
        for (const entry of s4tkPackage.entries.values()) {
            const type = entry.key.type;
            typeCount.set(type, (typeCount.get(type) || 0) + 1);
        }
        
        const counts = Array.from(typeCount.values());
        const maxCount = Math.max(...counts);
        const totalCount = counts.reduce((sum, count) => sum + count, 0);
        
        // If one type dominates (>80%), it's unbalanced
        if (maxCount / totalCount > 0.8) return 'unbalanced';
        
        // If reasonably distributed, it's balanced
        if (typeCount.size > 1 && maxCount / totalCount < 0.6) return 'balanced';
        
        return 'unknown';
    }
    
    /**
     * Check standards compliance
     */
    private static checkStandardsCompliance(buffer: Buffer, fileName: string, isPackage: boolean): StandardsCompliance {
        const compliance: StandardsCompliance = {
            followsS4Standards: true,
            hasManifest: false,
            hasDocumentation: false,
            localizationSupport: false,
            versioningScheme: 'none',
            complianceScore: 0
        };
        
        // Check for manifest
        if (isPackage) {
            try {
                const s4tkPackage = Package.from(buffer);
                compliance.hasManifest = this.hasManifestResource(s4tkPackage);
                compliance.localizationSupport = this.hasLocalizationSupport(s4tkPackage);
            } catch (error) {
                compliance.followsS4Standards = false;
            }
        } else {
            const bufferStr = buffer.toString('binary');
            compliance.hasManifest = /llamalogic\.modfilemanifest\.yml/i.test(bufferStr);
            compliance.hasDocumentation = /readme\.(txt|md)/i.test(bufferStr);
        }
        
        // Check versioning scheme
        compliance.versioningScheme = this.detectVersioningScheme(fileName);
        
        // Calculate compliance score
        compliance.complianceScore = this.calculateComplianceScore(compliance);
        
        return compliance;
    }
    
    /**
     * Check for manifest resource
     */
    private static hasManifestResource(s4tkPackage: Package): boolean {
        // Check for SnippetTuning resources that might be manifests
        for (const entry of s4tkPackage.entries.values()) {
            if (entry.key.type === 0x03B33DDF) { // SnippetTuning
                try {
                    const content = entry.value.toString();
                    if (content.includes('ModFileManifest') || content.includes('llamalogic')) {
                        return true;
                    }
                } catch {
                    continue;
                }
            }
        }
        return false;
    }
    
    /**
     * Check for localization support
     */
    private static hasLocalizationSupport(s4tkPackage: Package): boolean {
        // Check for StringTable resources
        for (const entry of s4tkPackage.entries.values()) {
            if (entry.key.type === 0x220557DA) { // StringTable
                return true;
            }
        }
        return false;
    }
    
    /**
     * Detect versioning scheme
     */
    private static detectVersioningScheme(fileName: string): 'semantic' | 'numeric' | 'date' | 'none' {
        const semanticPattern = /v?\d+\.\d+\.\d+/i;
        const numericPattern = /v?\d+(\.\d+)?/i;
        const datePattern = /\d{4}[-_]\d{2}[-_]\d{2}/;
        
        if (semanticPattern.test(fileName)) return 'semantic';
        if (datePattern.test(fileName)) return 'date';
        if (numericPattern.test(fileName)) return 'numeric';
        
        return 'none';
    }
    
    /**
     * Calculate compliance score
     */
    private static calculateComplianceScore(compliance: StandardsCompliance): number {
        let score = 0;
        
        if (compliance.followsS4Standards) score += 30;
        if (compliance.hasManifest) score += 25;
        if (compliance.hasDocumentation) score += 20;
        if (compliance.localizationSupport) score += 15;
        if (compliance.versioningScheme !== 'none') score += 10;
        
        return score;
    }
    
    /**
     * Analyze performance characteristics
     */
    private static analyzePerformance(buffer: Buffer, isPackage: boolean): PerformanceAnalysis {
        const fileSize = buffer.length;
        
        // Estimate load time based on file size and complexity
        const estimatedLoadTime = Math.max(10, fileSize / 1024 / 1024 * 50); // ~50ms per MB
        
        let optimizationLevel: 'poor' | 'fair' | 'good' | 'excellent' = 'good';
        if (fileSize > 50 * 1024 * 1024) optimizationLevel = 'poor'; // >50MB
        else if (fileSize > 10 * 1024 * 1024) optimizationLevel = 'fair'; // >10MB
        else if (fileSize < 1024 * 1024) optimizationLevel = 'excellent'; // <1MB
        
        const resourceEfficiency = Math.max(0, 100 - (fileSize / 1024 / 1024 * 5)); // Penalty for large files
        
        return {
            estimatedLoadTime,
            memoryFootprint: fileSize * 1.5, // Estimate 1.5x memory usage
            resourceEfficiency: Math.min(100, resourceEfficiency),
            optimizationLevel,
            bottlenecks: fileSize > 10 * 1024 * 1024 ? ['Large file size'] : []
        };
    }
    
    /**
     * Analyze security characteristics
     */
    private static analyzeSecurity(buffer: Buffer, fileName: string, isPackage: boolean): SecurityAnalysis {
        let riskLevel: 'low' | 'medium' | 'high' = 'low';
        const suspiciousPatterns: string[] = [];
        
        if (!isPackage) {
            // Analyze script content for suspicious patterns
            const bufferStr = buffer.toString('binary');
            
            // Check for potentially dangerous patterns
            const dangerousPatterns = [
                /eval\s*\(/gi,
                /exec\s*\(/gi,
                /import\s+os/gi,
                /import\s+subprocess/gi,
                /\.system\s*\(/gi
            ];
            
            for (const pattern of dangerousPatterns) {
                if (pattern.test(bufferStr)) {
                    suspiciousPatterns.push(`Potentially dangerous pattern: ${pattern.source}`);
                    riskLevel = 'medium';
                }
            }
        }
        
        // Calculate trust score based on various factors
        let trustScore = 80; // Base trust score
        
        if (suspiciousPatterns.length > 0) trustScore -= 30;
        if (buffer.length > 100 * 1024 * 1024) trustScore -= 20; // Very large files
        
        return {
            riskLevel,
            suspiciousPatterns,
            codeComplexity: Math.min(100, buffer.length / 1024 / 10), // Rough complexity estimate
            injectionRisk: suspiciousPatterns.length * 20,
            trustScore: Math.max(0, trustScore)
        };
    }
    
    /**
     * Calculate overall quality score
     */
    private static calculateOverallScore(assessment: QualityAssessment): number {
        const weights = {
            integrity: 0.25,
            structure: 0.20,
            standards: 0.20,
            performance: 0.20,
            security: 0.15
        };
        
        let score = 0;
        score += assessment.integrity.resourceIntegrity * weights.integrity;
        score += assessment.structure.organizationScore * weights.structure;
        score += assessment.standards.complianceScore * weights.standards;
        score += assessment.performance.resourceEfficiency * weights.performance;
        score += assessment.security.trustScore * weights.security;
        
        return Math.round(score);
    }
    
    /**
     * Generate recommendations based on assessment
     */
    private static generateRecommendations(assessment: QualityAssessment): string[] {
        const recommendations: string[] = [];
        
        if (assessment.integrity.resourceIntegrity < 100) {
            recommendations.push('File integrity issues detected - consider re-downloading');
        }
        
        if (assessment.structure.organizationScore < 70) {
            recommendations.push('Improve file organization and naming conventions');
        }
        
        if (!assessment.standards.hasManifest) {
            recommendations.push('Consider adding a manifest file for better mod management');
        }
        
        if (assessment.performance.optimizationLevel === 'poor') {
            recommendations.push('File size is large - consider optimization');
        }
        
        if (assessment.security.riskLevel !== 'low') {
            recommendations.push('Security concerns detected - review mod source');
        }
        
        return recommendations;
    }
}
