/**
 * Simple debug test to isolate extraction issues
 */

import { FilenameMetadataExtractor } from '../services/analysis/metadata/FilenameMetadataExtractor';

async function debugSimpleExtraction(): Promise<void> {
    console.log('🔍 Debug Simple Extraction');
    console.log('=' .repeat(40));
    
    // Test filename extraction directly
    const filenameExtractor = new FilenameMetadataExtractor();
    
    const testFilenames = [
        '!LittleMsSam_SimDaDatingApp_NoFWord.package',
        'Aurum_HairstyleFMM_145_Daiki.package',
        'TestMod_v1.2.3.package',
        'Author-ModName-v2.0.package'
    ];
    
    console.log('📝 Testing Filename Extraction:');
    
    for (const filename of testFilenames) {
        console.log(`\n🔍 Testing: "${filename}"`);
        
        try {
            const result = await filenameExtractor.extractMetadata(filename);
            
            if (result) {
                console.log(`   ✅ Success!`);
                console.log(`      Author: ${result.author || 'None'}`);
                console.log(`      Mod Name: ${result.modName || 'None'}`);
                console.log(`      Version: ${result.version || 'None'}`);
                console.log(`      Description: ${result.description || 'None'}`);
                console.log(`      Confidence: ${result.confidence}%`);
                console.log(`      Pattern: ${result.pattern}`);
                console.log(`      Processing Time: ${result.processingTime.toFixed(2)}ms`);
            } else {
                console.log(`   ❌ No metadata extracted`);
                
                // Debug pattern matching
                console.log(`   🔍 Debug pattern matching:`);
                const patterns = [
                    {
                        name: 'author_modname_version',
                        pattern: /^([^_\-\s]+)[-_]([^_\-\s]+?)[-_]v?(\d+(?:\.\d+)*(?:\.\d+)?)(?:[-_](.+?))?\.package$/i
                    },
                    {
                        name: 'bracketed_author_modname_version',
                        pattern: /^\[([^\]]+)\][-_\s]*([^_\-\s]+?)[-_\s]*v?(\d+(?:\.\d+)*(?:\.\d+)?)(?:[-_\s](.+?))?\.package$/i
                    },
                    {
                        name: 'simple_test',
                        pattern: /^(.+?)[-_](.+?)[-_](.+?)\.package$/i
                    }
                ];
                
                for (const pattern of patterns) {
                    const match = filename.match(pattern.pattern);
                    console.log(`      ${pattern.name}: ${match ? '✅ Match' : '❌ No match'}`);
                    if (match) {
                        console.log(`         Groups: ${match.slice(1).join(', ')}`);
                    }
                }
            }
            
        } catch (error) {
            console.log(`   ❌ Error: ${error.message}`);
        }
    }
}

// Run the debug
if (require.main === module) {
    debugSimpleExtraction().catch(console.error);
}

export { debugSimpleExtraction };
