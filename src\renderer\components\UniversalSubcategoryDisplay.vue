<template>
  <div class="universal-subcategory" v-if="classificationResult">
    <div class="universal-subcategory__header">
      <div class="universal-subcategory__icon">
        {{ getCategoryIcon() }}
      </div>
      <h4 class="universal-subcategory__title">{{ getCategoryTitle() }}</h4>
      <div class="universal-subcategory__confidence">
        <span class="confidence-badge" :class="getConfidenceClass()">
          {{ Math.round(classificationResult.confidence * 100) }}% confidence
        </span>
      </div>
    </div>

    <div class="universal-subcategory__content">
      <!-- Primary Subcategories -->
      <div class="subcategory-attribute" v-if="classificationResult.subcategories.length > 0">
        <div class="subcategory-attribute__label">
          <span class="subcategory-attribute__icon">🏷️</span>
          {{ getSubcategoryLabel() }}
        </div>
        <div class="subcategory-attribute__value">
          <div class="subcategory-badges">
            <span 
              v-for="subcategory in classificationResult.subcategories" 
              :key="subcategory"
              class="subcategory-badge"
              :class="getSubcategoryClass(subcategory)"
            >
              {{ formatSubcategory(subcategory) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Tags -->
      <div class="subcategory-attribute" v-if="classificationResult.tags.length > 0">
        <div class="subcategory-attribute__label">
          <span class="subcategory-attribute__icon">🏷️</span>
          Tags
        </div>
        <div class="subcategory-attribute__value">
          <div class="tag-badges">
            <span 
              v-for="tag in classificationResult.tags.slice(0, 5)" 
              :key="tag"
              class="tag-badge"
            >
              {{ formatTag(tag) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Detection Method -->
      <div class="universal-subcategory__meta">
        <span class="detection-method">
          <span class="detection-method__icon">🔍</span>
          Detected via {{ formatDetectionMethod(classificationResult.detectionMethod) }}
        </span>
        <div class="keywords" v-if="classificationResult.keywords && classificationResult.keywords.length > 0">
          <span class="keywords-label">Keywords:</span>
          <span class="keyword-tag" v-for="keyword in classificationResult.keywords.slice(0, 3)" :key="keyword">
            {{ keyword }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface UniversalClassificationResult {
  category: string;
  subcategories: string[];
  confidence: number;
  detectionMethod: string;
  keywords: string[];
  metadata: Record<string, any>;
  tags: string[];
}

interface Props {
  classificationResult: UniversalClassificationResult | null;
}

const props = defineProps<Props>();

const getCategoryIcon = (): string => {
  if (!props.classificationResult) return '❓';
  
  const iconMap: Record<string, string> = {
    hair: '✂️',
    clothing: '👕',
    makeup: '💄',
    accessories: '💍',
    skin_details: '🎨',
    furniture: '🪑',
    decorations: '🖼️',
    appliances: '🔌',
    build_items: '🏗️'
  };
  
  return iconMap[props.classificationResult.category] || '📦';
};

const getCategoryTitle = (): string => {
  if (!props.classificationResult) return 'Unknown';
  
  const titleMap: Record<string, string> = {
    hair: 'Hair Style Details',
    clothing: 'Clothing Details',
    makeup: 'Makeup Details',
    accessories: 'Accessory Details',
    skin_details: 'Skin Detail Information',
    furniture: 'Furniture Classification',
    decorations: 'Decoration Details',
    appliances: 'Appliance Information',
    build_items: 'Build Item Details'
  };
  
  return titleMap[props.classificationResult.category] || 'Content Details';
};

const getSubcategoryLabel = (): string => {
  if (!props.classificationResult) return 'Type';
  
  const labelMap: Record<string, string> = {
    hair: 'Style & Length',
    clothing: 'Type & Style',
    makeup: 'Type',
    accessories: 'Type',
    skin_details: 'Type',
    furniture: 'Type & Style',
    decorations: 'Type',
    appliances: 'Category',
    build_items: 'Type'
  };
  
  return labelMap[props.classificationResult.category] || 'Type';
};

const getConfidenceClass = () => {
  if (!props.classificationResult) return 'confidence--low';
  const confidence = props.classificationResult.confidence;
  if (confidence >= 0.8) return 'confidence--high';
  if (confidence >= 0.6) return 'confidence--medium';
  return 'confidence--low';
};

const getSubcategoryClass = (subcategory: string): string => {
  const category = props.classificationResult?.category || '';
  return `subcategory--${category}--${subcategory.replace(/[^a-zA-Z0-9]/g, '_')}`;
};

const formatSubcategory = (subcategory: string): string => {
  return subcategory.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatTag = (tag: string): string => {
  return tag.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatDetectionMethod = (method: string): string => {
  switch (method) {
    case 'filename': return 'filename analysis';
    case 'resource_analysis': return 'resource analysis';
    case 'hybrid': return 'hybrid detection';
    default: return method;
  }
};
</script>

<style scoped>
.universal-subcategory {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 1rem;
  margin-top: 0.75rem;
}

.universal-subcategory__header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--gray-200);
}

.universal-subcategory__icon {
  font-size: 1.25rem;
}

.universal-subcategory__title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700);
  margin: 0;
  flex: 1;
}

.confidence-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.confidence--high {
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green-dark);
  border: 1px solid var(--plumbob-green-light);
}

.confidence--medium {
  background: var(--sims-orange-bg);
  color: var(--sims-orange);
  border: 1px solid var(--sims-orange-light);
}

.confidence--low {
  background: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--gray-300);
}

.universal-subcategory__content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.subcategory-attribute {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.subcategory-attribute__label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 5rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.subcategory-attribute__icon {
  font-size: 0.875rem;
}

.subcategory-attribute__value {
  flex: 1;
}

.subcategory-badges,
.tag-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.subcategory-badge,
.tag-badge {
  display: inline-block;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  text-transform: capitalize;
}

/* Universal subcategory styles with category-specific colors */
.subcategory-badge {
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green-dark);
}

.tag-badge {
  background: var(--gray-100);
  color: var(--gray-600);
}

.universal-subcategory__meta {
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detection-method {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--gray-500);
}

.detection-method__icon {
  font-size: 0.75rem;
}

.keywords {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.keywords-label {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.keyword-tag {
  font-size: 0.625rem;
  background: var(--gray-100);
  color: var(--gray-600);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
}
</style>
