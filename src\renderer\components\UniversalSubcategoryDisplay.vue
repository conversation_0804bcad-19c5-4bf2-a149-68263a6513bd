<template>
  <div class="universal-subcategory" v-if="classificationResult">
    <div class="universal-subcategory__header">
      <div class="universal-subcategory__icon">
        {{ getCategoryIcon() }}
      </div>
      <h4 class="universal-subcategory__title">{{ getCategoryTitle() }}</h4>
      <div class="universal-subcategory__confidence">
        <span class="confidence-badge" :class="getConfidenceClass()">
          {{ Math.round(classificationResult.confidence * 100) }}% confidence
        </span>
      </div>
    </div>

    <div class="universal-subcategory__content">
      <!-- Primary Subcategories -->
      <div class="subcategory-attribute" v-if="classificationResult.subcategories.length > 0">
        <div class="subcategory-attribute__label">
          <span class="subcategory-attribute__icon">🏷️</span>
          {{ getSubcategoryLabel() }}
        </div>
        <div class="subcategory-attribute__value">
          <div class="subcategory-badges">
            <span 
              v-for="subcategory in classificationResult.subcategories" 
              :key="subcategory"
              class="subcategory-badge"
              :class="getSubcategoryClass(subcategory)"
            >
              {{ formatSubcategory(subcategory) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Tags -->
      <div class="subcategory-attribute" v-if="classificationResult.tags.length > 0">
        <div class="subcategory-attribute__label">
          <span class="subcategory-attribute__icon">🏷️</span>
          Tags
        </div>
        <div class="subcategory-attribute__value">
          <div class="tag-badges">
            <span 
              v-for="tag in classificationResult.tags.slice(0, 5)" 
              :key="tag"
              class="tag-badge"
            >
              {{ formatTag(tag) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Detection Method -->
      <div class="universal-subcategory__meta">
        <span class="detection-method">
          <span class="detection-method__icon">🔍</span>
          Detected via {{ formatDetectionMethod(classificationResult.detectionMethod) }}
        </span>
        <div class="keywords" v-if="classificationResult.keywords && classificationResult.keywords.length > 0">
          <span class="keywords-label">Keywords:</span>
          <span class="keyword-tag" v-for="keyword in classificationResult.keywords.slice(0, 3)" :key="keyword">
            {{ keyword }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface UniversalClassificationResult {
  category: string;
  subcategories: string[];
  confidence: number;
  detectionMethod: string;
  keywords: string[];
  metadata: Record<string, any>;
  tags: string[];
}

interface Props {
  classificationResult: UniversalClassificationResult | null;
}

const props = defineProps<Props>();

const getCategoryIcon = (): string => {
  if (!props.classificationResult) return '❓';
  
  const iconMap: Record<string, string> = {
    hair: '✂️',
    clothing: '👕',
    makeup: '💄',
    accessories: '💍',
    skin_details: '🎨',
    furniture: '🪑',
    decorations: '🖼️',
    appliances: '🔌',
    build_items: '🏗️'
  };
  
  return iconMap[props.classificationResult.category] || '📦';
};

const getCategoryTitle = (): string => {
  if (!props.classificationResult) return 'Unknown';
  
  const titleMap: Record<string, string> = {
    hair: 'Hair Style Details',
    clothing: 'Clothing Details',
    makeup: 'Makeup Details',
    accessories: 'Accessory Details',
    skin_details: 'Skin Detail Information',
    furniture: 'Furniture Classification',
    decorations: 'Decoration Details',
    appliances: 'Appliance Information',
    build_items: 'Build Item Details'
  };
  
  return titleMap[props.classificationResult.category] || 'Content Details';
};

const getSubcategoryLabel = (): string => {
  if (!props.classificationResult) return 'Type';
  
  const labelMap: Record<string, string> = {
    hair: 'Style & Length',
    clothing: 'Type & Style',
    makeup: 'Type',
    accessories: 'Type',
    skin_details: 'Type',
    furniture: 'Type & Style',
    decorations: 'Type',
    appliances: 'Category',
    build_items: 'Type'
  };
  
  return labelMap[props.classificationResult.category] || 'Type';
};

const getConfidenceClass = () => {
  if (!props.classificationResult) return 'confidence--low';
  const confidence = props.classificationResult.confidence;
  if (confidence >= 0.8) return 'confidence--high';
  if (confidence >= 0.6) return 'confidence--medium';
  return 'confidence--low';
};

const getSubcategoryClass = (subcategory: string): string => {
  const category = props.classificationResult?.category || '';
  return `subcategory--${category}--${subcategory.replace(/[^a-zA-Z0-9]/g, '_')}`;
};

const formatSubcategory = (subcategory: string): string => {
  return subcategory.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatTag = (tag: string): string => {
  return tag.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatDetectionMethod = (method: string): string => {
  switch (method) {
    case 'filename': return 'filename analysis';
    case 'resource_analysis': return 'resource analysis';
    case 'hybrid': return 'hybrid detection';
    default: return method;
  }
};
</script>

<style scoped>
.universal-subcategory {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--bg-secondary) 100%);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  margin-top: var(--space-4);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-200) var(--ease-out);
}

.universal-subcategory:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-medium);
}

.universal-subcategory__header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-light);
}

.universal-subcategory__icon {
  font-size: var(--text-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--plumbob-green-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--plumbob-green-light);
}

.universal-subcategory__title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  flex: 1;
  line-height: var(--leading-tight);
}

.confidence-badge {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 1px solid;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.confidence--high {
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green-dark);
  border-color: var(--plumbob-green-light);
}

.confidence--high::before {
  content: "●";
  color: var(--plumbob-green);
}

.confidence--medium {
  background: var(--sims-orange-bg);
  color: var(--sims-orange);
  border-color: var(--sims-orange-light);
}

.confidence--medium::before {
  content: "●";
  color: var(--sims-orange);
}

.confidence--low {
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  border-color: var(--border-medium);
}

.confidence--low::before {
  content: "●";
  color: var(--text-tertiary);
}

.universal-subcategory__content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.subcategory-attribute {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.subcategory-attribute__label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 5rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.subcategory-attribute__icon {
  font-size: 0.875rem;
}

.subcategory-attribute__value {
  flex: 1;
}

.subcategory-badges,
.tag-badges {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.subcategory-badge,
.tag-badge {
  display: inline-flex;
  align-items: center;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  text-transform: capitalize;
  border: 1px solid;
  transition: all var(--duration-150) var(--ease-out);
  cursor: default;
}

.subcategory-badge:hover,
.tag-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Category-specific subcategory badge colors */
.subcategory-badge {
  background: var(--sims-blue-bg);
  color: var(--sims-blue-dark);
  border-color: var(--sims-blue-light);
}

.tag-badge {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-color: var(--border-medium);
}

.universal-subcategory__meta {
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detection-method {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--gray-500);
}

.detection-method__icon {
  font-size: 0.75rem;
}

.keywords {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.keywords-label {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.keyword-tag {
  font-size: var(--text-xs);
  background: var(--bg-tertiary);
  color: var(--text-tertiary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-weight: var(--font-medium);
  border: 1px solid var(--border-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .universal-subcategory {
    padding: var(--space-4);
    margin-top: var(--space-3);
  }

  .universal-subcategory__header {
    gap: var(--space-2);
    margin-bottom: var(--space-3);
    padding-bottom: var(--space-2);
  }

  .universal-subcategory__icon {
    width: 28px;
    height: 28px;
    font-size: var(--text-lg);
  }

  .universal-subcategory__title {
    font-size: var(--text-sm);
  }

  .subcategory-attribute {
    gap: var(--space-3);
  }

  .subcategory-badges,
  .tag-badges {
    gap: var(--space-1);
  }

  .subcategory-badge,
  .tag-badge {
    font-size: 10px;
    padding: var(--space-1) var(--space-2);
  }
}

@media (max-width: 480px) {
  .universal-subcategory {
    padding: var(--space-3);
  }

  .universal-subcategory__header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .subcategory-attribute {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .subcategory-attribute__label {
    min-width: auto;
  }

  .keywords {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}
</style>
