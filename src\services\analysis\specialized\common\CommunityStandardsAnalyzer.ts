/**
 * Community Standards Analyzer
 * 
 * Specialized component for recognizing ModTheSims, TSR, Patreon naming conventions
 * and extracting metadata from README files and community-specific patterns.
 * 
 * Based on comprehensive research of Sims 4 modding community practices.
 */

import * as path from 'path';

export interface CommunityMetadata {
    author?: string;
    version?: string;
    modName?: string;
    category?: string;
    tags?: string[];
    platform?: 'ModTheSims' | 'TSR' | 'Patreon' | 'SimsFileShare' | 'Unknown';
    confidence: number; // 0-100 confidence score
    pattern?: string; // Which pattern was matched
}

/**
 * Analyzes files using community-established patterns and standards
 */
export class CommunityStandardsAnalyzer {
    
    /**
     * Extract metadata using community-established patterns
     * 
     * @param filePath - Full file path or just filename
     * @param content - File content (for README analysis)
     * @returns Extracted metadata with confidence score
     */
    public static extractCommunityMetadata(filePath: string, content?: string): CommunityMetadata {
        const filename = path.basename(filePath);
        
        // Try platform-specific patterns in order of specificity
        const patterns = [
            () => this.analyzeModTheSimsPattern(filename),
            () => this.analyzeTSRPattern(filename),
            () => this.analyzePatreonPattern(filename),
            () => this.analyzeSimsFileSharePattern(filename),
            () => this.analyzeGenericCommunityPattern(filename),
            () => content ? this.analyzeReadmeContent(content) : { confidence: 0 }
        ];
        
        let bestResult: CommunityMetadata = { confidence: 0 };
        
        for (const patternFunc of patterns) {
            const result = patternFunc();
            if (result.confidence > bestResult.confidence) {
                bestResult = result;
                
                // If we found high-confidence metadata, we can stop
                if (result.confidence >= 85) break;
            }
        }
        
        return bestResult;
    }
    
    /**
     * Analyze ModTheSims.info naming conventions
     * Common patterns: Creator_ModName_Version, Creator-ModName-v1.2
     */
    private static analyzeModTheSimsPattern(filename: string): CommunityMetadata {
        const metadata: CommunityMetadata = { confidence: 0, platform: 'ModTheSims' };
        
        // ModTheSims patterns
        const patterns = [
            // Creator_ModName_v1.2.3
            /^(\w+)[-_](.+?)[-_]v\.?(\d+(?:\.\d+)*(?:[a-z]?)?)$/i,
            // Creator_ModName_Version (without v prefix)
            /^(\w+)[-_](.+?)[-_](\d+(?:\.\d+)*(?:[a-z]?)?)$/i,
            // Creator_ModName (no version)
            /^(\w+)[-_](.+)$/
        ];
        
        for (let i = 0; i < patterns.length; i++) {
            const match = filename.match(patterns[i]);
            if (match && this.isLikelyCreatorName(match[1])) {
                metadata.author = this.cleanValue(match[1]);
                metadata.modName = this.cleanValue(match[2]);
                if (match[3]) metadata.version = match[3];
                
                // Higher confidence for versioned mods
                metadata.confidence = match[3] ? 85 : 75;
                metadata.pattern = 'ModTheSims_Standard';
                
                // Add category hints based on mod name
                metadata.category = this.inferCategoryFromName(match[2]);
                metadata.tags = ['modthesims'];
                
                return metadata;
            }
        }
        
        return metadata;
    }
    
    /**
     * Analyze The Sims Resource (TSR) patterns
     * Common patterns: TSR_Creator_ModName, Creator_TSR_ModName
     */
    private static analyzeTSRPattern(filename: string): CommunityMetadata {
        const metadata: CommunityMetadata = { confidence: 0, platform: 'TSR' };
        
        const patterns = [
            // TSR_Creator_ModName
            /^TSR[-_](\w+)[-_](.+)/i,
            // Creator_TSR_ModName
            /^(\w+)[-_]TSR[-_](.+)/i,
            // TSR in filename (lower confidence)
            /TSR/i
        ];
        
        for (let i = 0; i < patterns.length; i++) {
            const match = filename.match(patterns[i]);
            if (match) {
                if (i < 2) { // Specific TSR patterns
                    metadata.author = this.cleanValue(match[1]);
                    metadata.modName = this.cleanValue(match[2]);
                    metadata.confidence = 80;
                    metadata.pattern = 'TSR_Standard';
                } else { // Generic TSR mention
                    metadata.confidence = 40;
                    metadata.pattern = 'TSR_Generic';
                }
                
                metadata.tags = ['tsr', 'the-sims-resource'];
                metadata.category = this.inferCategoryFromName(filename);
                
                return metadata;
            }
        }
        
        return metadata;
    }
    
    /**
     * Analyze Patreon creator patterns
     * Common patterns: Creator_Patreon_ModName, Creator_Early_Access_ModName
     */
    private static analyzePatreonPattern(filename: string): CommunityMetadata {
        const metadata: CommunityMetadata = { confidence: 0, platform: 'Patreon' };
        
        const patterns = [
            // Creator_Patreon_ModName
            /^(\w+)[-_]Patreon[-_](.+)/i,
            // Creator_Early_Access_ModName
            /^(\w+)[-_]Early[-_]Access[-_](.+)/i,
            // Creator_EA_ModName (Early Access abbreviation)
            /^(\w+)[-_]EA[-_](.+)/i,
            // Patreon in filename (lower confidence)
            /Patreon|Early[-_]Access/i
        ];
        
        for (let i = 0; i < patterns.length; i++) {
            const match = filename.match(patterns[i]);
            if (match) {
                if (i < 3) { // Specific Patreon patterns
                    metadata.author = this.cleanValue(match[1]);
                    metadata.modName = this.cleanValue(match[2]);
                    metadata.confidence = 80;
                    metadata.pattern = 'Patreon_Standard';
                } else { // Generic Patreon mention
                    metadata.confidence = 50;
                    metadata.pattern = 'Patreon_Generic';
                }
                
                metadata.tags = ['patreon', 'early-access', 'exclusive'];
                metadata.category = this.inferCategoryFromName(filename);
                
                return metadata;
            }
        }
        
        return metadata;
    }
    
    /**
     * Analyze SimsFileShare patterns
     * Common patterns: SFS_Creator_ModName, Creator_SFS_ModName
     */
    private static analyzeSimsFileSharePattern(filename: string): CommunityMetadata {
        const metadata: CommunityMetadata = { confidence: 0, platform: 'SimsFileShare' };
        
        const patterns = [
            // SFS_Creator_ModName
            /^SFS[-_](\w+)[-_](.+)/i,
            // Creator_SFS_ModName
            /^(\w+)[-_]SFS[-_](.+)/i,
            // SFS in filename
            /SFS/i
        ];
        
        for (let i = 0; i < patterns.length; i++) {
            const match = filename.match(patterns[i]);
            if (match) {
                if (i < 2) { // Specific SFS patterns
                    metadata.author = this.cleanValue(match[1]);
                    metadata.modName = this.cleanValue(match[2]);
                    metadata.confidence = 75;
                    metadata.pattern = 'SFS_Standard';
                } else { // Generic SFS mention
                    metadata.confidence = 40;
                    metadata.pattern = 'SFS_Generic';
                }
                
                metadata.tags = ['simsfileshare', 'sfs'];
                metadata.category = this.inferCategoryFromName(filename);
                
                return metadata;
            }
        }
        
        return metadata;
    }
    
    /**
     * Analyze generic community patterns
     * Fallback for common community naming conventions
     */
    private static analyzeGenericCommunityPattern(filename: string): CommunityMetadata {
        const metadata: CommunityMetadata = { confidence: 0, platform: 'Unknown' };
        
        // Look for common community indicators
        const communityIndicators = [
            'cc', 'custom', 'content', 'mod', 'override', 'replacement',
            'recolor', 'mesh', 'texture', 'tuning', 'script'
        ];
        
        const lowerFilename = filename.toLowerCase();
        const hasIndicators = communityIndicators.some(indicator => 
            lowerFilename.includes(indicator)
        );
        
        if (hasIndicators) {
            metadata.confidence = 30;
            metadata.pattern = 'Generic_Community';
            metadata.category = this.inferCategoryFromName(filename);
            metadata.tags = ['community-content'];
        }
        
        return metadata;
    }
    
    /**
     * Extract metadata from README content
     * Analyzes README files for author, version, and description info
     */
    private static analyzeReadmeContent(content: string): CommunityMetadata {
        const metadata: CommunityMetadata = { confidence: 0 };
        const lines = content.split('\n');
        
        for (const line of lines) {
            const cleanLine = line.trim();
            
            // Author patterns
            if (!metadata.author) {
                const authorMatch = cleanLine.match(/(?:author|creator|made by|by):?\s*(.+)/i);
                if (authorMatch) {
                    metadata.author = this.cleanValue(authorMatch[1]);
                    metadata.confidence = Math.max(metadata.confidence, 70);
                }
            }
            
            // Version patterns
            if (!metadata.version) {
                const versionMatch = cleanLine.match(/(?:version|ver):?\s*(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/i);
                if (versionMatch) {
                    metadata.version = versionMatch[1];
                    metadata.confidence = Math.max(metadata.confidence, 70);
                }
            }
            
            // Platform detection
            if (!metadata.platform || metadata.platform === 'Unknown') {
                if (/modthesims|mts/i.test(cleanLine)) {
                    metadata.platform = 'ModTheSims';
                    metadata.tags = ['modthesims'];
                } else if (/thesimsresource|tsr/i.test(cleanLine)) {
                    metadata.platform = 'TSR';
                    metadata.tags = ['tsr'];
                } else if (/patreon/i.test(cleanLine)) {
                    metadata.platform = 'Patreon';
                    metadata.tags = ['patreon'];
                }
            }
            
            // Requirements/dependencies
            const requiresMatch = cleanLine.match(/(?:requires|dependencies|needs):?\s*(.+)/i);
            if (requiresMatch) {
                const deps = requiresMatch[1].split(/[,;]/).map(dep => dep.trim());
                metadata.tags = [...(metadata.tags || []), ...deps.map(dep => `requires-${dep.toLowerCase()}`)];
            }
        }
        
        if (metadata.confidence > 0) {
            metadata.pattern = 'README_Analysis';
        }
        
        return metadata;
    }
    
    /**
     * Infer category from mod name or filename
     */
    private static inferCategoryFromName(name: string): string {
        const lowerName = name.toLowerCase();
        
        const categoryPatterns = {
            'cas': /\b(cas|create[-_]a[-_]sim|clothing|hair|makeup|accessories|skin|eyes)\b/,
            'build_buy': /\b(build|buy|furniture|decor|objects|room|house|lot)\b/,
            'gameplay': /\b(gameplay|traits|careers|skills|interactions|mod|script)\b/,
            'override': /\b(override|replacement|tuning|xml)\b/,
            'recolor': /\b(recolor|retexture|palette|color)\b/
        };
        
        for (const [category, pattern] of Object.entries(categoryPatterns)) {
            if (pattern.test(lowerName)) {
                return category;
            }
        }
        
        return 'unknown';
    }
    
    /**
     * Check if a string looks like a creator name
     */
    private static isLikelyCreatorName(name: string): boolean {
        // Creator names typically:
        // - Are 3-25 characters long
        // - Don't contain common file extensions or technical terms
        // - May contain numbers but aren't all numbers
        
        if (name.length < 3 || name.length > 25) return false;
        
        const technicalTerms = [
            'file', 'data', 'temp', 'backup', 'copy', 'new', 'old',
            'test', 'debug', 'final', 'version', 'v1', 'v2'
        ];
        
        const lowerName = name.toLowerCase();
        if (technicalTerms.some(term => lowerName === term)) return false;
        
        // Must contain at least one letter
        if (!/[a-zA-Z]/.test(name)) return false;
        
        return true;
    }
    
    /**
     * Clean and normalize metadata values
     */
    private static cleanValue(value: string): string {
        return value
            .trim()
            .replace(/['"]/g, '') // Remove quotes
            .replace(/[-_]/g, ' ') // Convert separators to spaces
            .replace(/\s+/g, ' ') // Normalize whitespace
            .substring(0, 100); // Limit length
    }
}
