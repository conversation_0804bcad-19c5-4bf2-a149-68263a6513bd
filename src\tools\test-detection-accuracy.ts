/**
 * Test Detection Accuracy
 * 
 * Verifies that our conflict detection is actually working by testing
 * with known conflicts and edge cases.
 */

import { KnownConflictDatabase } from '../services/analysis/conflicts/KnownConflictDatabase';
import { ModContentAnalysis, ModContentType } from '../services/analysis/content/ContentAnalysisService';

/**
 * Test known conflict detection with simulated mod names
 */
async function testKnownConflictAccuracy() {
    console.log('🔍 TESTING CONFLICT DETECTION ACCURACY');
    console.log('=====================================\n');
    
    // Test 1: MCCC Conflicts
    console.log('📋 Test 1: MCCC Version Conflicts');
    const mcccMods = new Map();
    mcccMods.set('MC_CommandCenter_v2023.1.package', {} as ModContentAnalysis);
    mcccMods.set('MC_Woohoo_v2023.1.package', {} as ModContentAnalysis);
    mcccMods.set('mccc_settings_v2022.package', {} as ModContentAnalysis);
    
    const mcccConflicts = await KnownConflictDatabase.checkKnownConflicts(mcccMods);
    console.log(`   Expected: 1+ conflicts | Detected: ${mcccConflicts.length}`);
    if (mcccConflicts.length > 0) {
        console.log(`   ✅ MCCC conflict detection working`);
        mcccConflicts.forEach(c => console.log(`      - ${c.title}: ${c.affectedMods.join(', ')}`));
    } else {
        console.log(`   ❌ MCCC conflict detection FAILED`);
    }
    
    // Test 2: UI Cheats Conflicts  
    console.log('\n📋 Test 2: UI Cheats Conflicts');
    const uiMods = new Map();
    uiMods.set('UI_Cheats_Extension_v1.41.package', {} as ModContentAnalysis);
    uiMods.set('ui_cheat_menu_v1.40.package', {} as ModContentAnalysis);
    
    const uiConflicts = await KnownConflictDatabase.checkKnownConflicts(uiMods);
    console.log(`   Expected: 1+ conflicts | Detected: ${uiConflicts.length}`);
    if (uiConflicts.length > 0) {
        console.log(`   ✅ UI Cheats conflict detection working`);
        uiConflicts.forEach(c => console.log(`      - ${c.title}: ${c.affectedMods.join(', ')}`));
    } else {
        console.log(`   ❌ UI Cheats conflict detection FAILED`);
    }
    
    // Test 3: Child Slider Conflicts
    console.log('\n📋 Test 3: Child Slider Conflicts');
    const sliderMods = new Map();
    sliderMods.set('toddler_slider_height_v2.package', {} as ModContentAnalysis);
    sliderMods.set('child_slider_body_v1.package', {} as ModContentAnalysis);
    
    const sliderConflicts = await KnownConflictDatabase.checkKnownConflicts(sliderMods);
    console.log(`   Expected: 1+ conflicts | Detected: ${sliderConflicts.length}`);
    if (sliderConflicts.length > 0) {
        console.log(`   ✅ Child slider conflict detection working`);
        sliderConflicts.forEach(c => console.log(`      - ${c.title}: ${c.affectedMods.join(', ')}`));
    } else {
        console.log(`   ❌ Child slider conflict detection FAILED`);
    }
    
    // Test 4: Real User Mods (Pregnancy/Risky)
    console.log('\n📋 Test 4: User\'s Pregnancy Mods');
    const userMods = new Map();
    userMods.set('!LittleMsSam_SpendWeekendWith_Addon_Risky.package', {} as ModContentAnalysis);
    userMods.set('1_Pandasama_ChildBirth_mod_v1.67_SPA_ES_MitsukoSimmer.package', {} as ModContentAnalysis);
    
    const userConflicts = await KnownConflictDatabase.checkKnownConflicts(userMods);
    console.log(`   Expected: 0-1 conflicts | Detected: ${userConflicts.length}`);
    if (userConflicts.length > 0) {
        console.log(`   ⚠️  User pregnancy mod conflicts detected`);
        userConflicts.forEach(c => console.log(`      - ${c.title}: ${c.affectedMods.join(', ')}`));
    } else {
        console.log(`   ✅ No pregnancy conflicts (may be correct)`);
    }
    
    // Test 5: No Conflicts (Control)
    console.log('\n📋 Test 5: Control Test (No Conflicts Expected)');
    const cleanMods = new Map();
    cleanMods.set('some_hair_mod.package', {} as ModContentAnalysis);
    cleanMods.set('furniture_pack.package', {} as ModContentAnalysis);
    cleanMods.set('clothing_recolor.package', {} as ModContentAnalysis);
    
    const cleanConflicts = await KnownConflictDatabase.checkKnownConflicts(cleanMods);
    console.log(`   Expected: 0 conflicts | Detected: ${cleanConflicts.length}`);
    if (cleanConflicts.length === 0) {
        console.log(`   ✅ Control test passed - no false positives`);
    } else {
        console.log(`   ❌ Control test FAILED - false positives detected`);
        cleanConflicts.forEach(c => console.log(`      - ${c.title}: ${c.affectedMods.join(', ')}`));
    }
    
    // Summary
    console.log('\n📊 ACCURACY TEST SUMMARY');
    console.log('========================');
    const totalTests = 5;
    let passedTests = 0;
    
    if (mcccConflicts.length > 0) passedTests++;
    if (uiConflicts.length > 0) passedTests++;
    if (sliderConflicts.length > 0) passedTests++;
    // User test is informational, not pass/fail
    if (cleanConflicts.length === 0) passedTests++;
    
    console.log(`Tests passed: ${passedTests}/4 (excluding user mod test)`);
    
    if (passedTests >= 3) {
        console.log('✅ Conflict detection appears to be working correctly');
        console.log('💡 0 conflicts in user collection may be accurate');
    } else {
        console.log('❌ Conflict detection has issues - results may be inaccurate');
        console.log('🔧 Pattern matching or detection logic needs fixing');
    }
}

// Run the accuracy test
if (require.main === module) {
    testKnownConflictAccuracy().catch(console.error);
}

export { testKnownConflictAccuracy };
