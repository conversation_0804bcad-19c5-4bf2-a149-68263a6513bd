/**
 * CAS Age Gender Mapper
 * 
 * Specialized component for extracting age and gender information from CAS Part data.
 * Handles bitwise flag parsing for age groups and gender categories.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring.
 */

import { AgeGroup, Gender, AGE_GROUP_FLAGS, GENDER_FLAGS, type CASPartInfo } from './types';

/**
 * Handles mapping of age and gender flags to CAS categories
 */
export class AgeGenderMapper {
    
    /**
     * Extracts age and gender information from AgeGender field value
     * 
     * @param ageGenderValue - The numeric value from the AgeGender field
     * @param casPartInfo - CAS part info object to populate
     */
    public static extractAgeGenderFromValue(ageGenderValue: any, casPartInfo: CASPartInfo): void {
        const numericValue = this.parseNumericValue(ageGenderValue);
        
        if (numericValue === null) {
            console.warn('Invalid AgeGender value:', ageGenderValue);
            this.setDefaultAgeGender(casPartInfo);
            return;
        }
        
        // Extract age groups using bitwise operations
        this.extractAgeGroups(numericValue, casPartInfo);
        
        // Extract gender information using bitwise operations
        this.extractGenders(numericValue, casPartInfo);
    }
    
    /**
     * Parses various input formats to numeric value
     * 
     * @param value - Input value in various formats
     * @returns Numeric value or null if parsing fails
     */
    private static parseNumericValue(value: any): number | null {
        if (typeof value === 'number') {
            return value;
        }
        
        if (typeof value === 'string') {
            const parsed = parseInt(value, 10);
            return isNaN(parsed) ? null : parsed;
        }
        
        // Handle object with value property
        if (value && typeof value.value !== 'undefined') {
            return this.parseNumericValue(value.value);
        }
        
        return null;
    }
    
    /**
     * Extracts age groups from numeric value using bitwise flags
     * 
     * @param ageGenderValue - Numeric value containing age flags
     * @param casPartInfo - CAS part info to populate
     */
    private static extractAgeGroups(ageGenderValue: number, casPartInfo: CASPartInfo): void {
        casPartInfo.ageGroups = [];
        
        // Check each age group flag
        if (ageGenderValue & AGE_GROUP_FLAGS.INFANT) {
            casPartInfo.ageGroups.push(AgeGroup.INFANT);
        }
        if (ageGenderValue & AGE_GROUP_FLAGS.TODDLER) {
            casPartInfo.ageGroups.push(AgeGroup.TODDLER);
        }
        if (ageGenderValue & AGE_GROUP_FLAGS.CHILD) {
            casPartInfo.ageGroups.push(AgeGroup.CHILD);
        }
        if (ageGenderValue & AGE_GROUP_FLAGS.TEEN) {
            casPartInfo.ageGroups.push(AgeGroup.TEEN);
        }
        if (ageGenderValue & AGE_GROUP_FLAGS.YOUNG_ADULT) {
            casPartInfo.ageGroups.push(AgeGroup.YOUNG_ADULT);
        }
        if (ageGenderValue & AGE_GROUP_FLAGS.ADULT) {
            casPartInfo.ageGroups.push(AgeGroup.ADULT);
        }
        if (ageGenderValue & AGE_GROUP_FLAGS.ELDER) {
            casPartInfo.ageGroups.push(AgeGroup.ELDER);
        }
        
        // If no age groups found, set default
        if (casPartInfo.ageGroups.length === 0) {
            casPartInfo.ageGroups = [AgeGroup.TEEN, AgeGroup.YOUNG_ADULT, AgeGroup.ADULT, AgeGroup.ELDER];
        }
    }
    
    /**
     * Extracts gender information from numeric value using bitwise flags
     * 
     * @param ageGenderValue - Numeric value containing gender flags
     * @param casPartInfo - CAS part info to populate
     */
    private static extractGenders(ageGenderValue: number, casPartInfo: CASPartInfo): void {
        casPartInfo.genders = [];
        
        const hasMale = (ageGenderValue & GENDER_FLAGS.MALE) !== 0;
        const hasFemale = (ageGenderValue & GENDER_FLAGS.FEMALE) !== 0;
        
        if (hasMale && hasFemale) {
            casPartInfo.genders.push(Gender.UNISEX);
        } else if (hasMale) {
            casPartInfo.genders.push(Gender.MALE);
        } else if (hasFemale) {
            casPartInfo.genders.push(Gender.FEMALE);
        } else {
            // Default to unisex if no gender flags found
            casPartInfo.genders.push(Gender.UNISEX);
        }
    }
    
    /**
     * Sets default age and gender values when parsing fails
     * 
     * @param casPartInfo - CAS part info to populate with defaults
     */
    private static setDefaultAgeGender(casPartInfo: CASPartInfo): void {
        casPartInfo.ageGroups = [AgeGroup.TEEN, AgeGroup.YOUNG_ADULT, AgeGroup.ADULT, AgeGroup.ELDER];
        casPartInfo.genders = [Gender.UNISEX];
    }
    
    /**
     * Validates age group assignments
     * 
     * @param ageGroups - Array of age groups to validate
     * @returns true if age groups are valid
     */
    public static validateAgeGroups(ageGroups: AgeGroup[]): boolean {
        return ageGroups.length > 0 && ageGroups.every(age => 
            Object.values(AgeGroup).includes(age)
        );
    }
    
    /**
     * Validates gender assignments
     * 
     * @param genders - Array of genders to validate
     * @returns true if genders are valid
     */
    public static validateGenders(genders: Gender[]): boolean {
        return genders.length > 0 && genders.every(gender => 
            Object.values(Gender).includes(gender)
        );
    }
}
