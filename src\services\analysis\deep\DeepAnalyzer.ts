import type { ResourceEntry } from '@s4tk/models/types';
import { FileType } from '../../../types/analysis';
import { CategoryAnalyzer } from './CategoryAnalyzer';
import { DependencyAnalyzer } from './DependencyAnalyzer';
import { OverrideDetector } from './OverrideDetector';
import { MetadataExtractor } from './MetadataExtractor';
import type { DeepAnalysisResult } from './types';

/**
 * Main deep analyzer that orchestrates specialized analysis components
 * Refactored to use specialized analyzers for better maintainability
 */
export class DeepAnalyzer {
    
    /**
     * Performs comprehensive deep analysis using specialized analyzers
     */
    public static analyzeResources(
        resources: ResourceEntry[], 
        filePath: string, 
        fileType: FileType
    ): DeepAnalysisResult {
        // Perform category analysis
        const categoryAnalysis = CategoryAnalyzer.analyze(resources, filePath, fileType);
        
        // Perform dependency analysis
        const dependencyAnalysis = DependencyAnalyzer.analyze(resources, filePath);
        
        // Perform override analysis
        const overrideAnalysis = OverrideDetector.analyze(resources, filePath);
        
        // Perform metadata extraction
        const metadataAnalysis = MetadataExtractor.analyze(resources, filePath);
        
        // Calculate overall confidence
        const confidence = this.calculateOverallConfidence(
            categoryAnalysis,
            dependencyAnalysis,
            overrideAnalysis,
            metadataAnalysis
        );
        
        return {
            primaryCategory: categoryAnalysis.primaryCategory,
            subcategory: categoryAnalysis.subcategory,
            suggestedPath: categoryAnalysis.suggestedPath,
            confidence,
            categoryAnalysis,
            dependencyAnalysis,
            overrideAnalysis,
            metadataAnalysis
        };
    }
    
    /**
     * Calculates overall confidence score based on all analyses
     */
    private static calculateOverallConfidence(
        categoryAnalysis: any,
        dependencyAnalysis: any,
        overrideAnalysis: any,
        metadataAnalysis: any
    ): number {
        let totalConfidence = 0;
        let factors = 0;
        
        // Category analysis confidence
        if (categoryAnalysis.detectedCategories.length > 0) {
            totalConfidence += categoryAnalysis.detectedCategories[0].confidence;
            factors++;
        }
        
        // Override analysis confidence
        if (overrideAnalysis.confidence > 0) {
            totalConfidence += overrideAnalysis.confidence;
            factors++;
        }
        
        // Metadata complexity factor (inverse relationship)
        const complexityScore = metadataAnalysis.complexity.score;
        if (complexityScore > 0) {
            // Higher complexity can reduce confidence in simple categorization
            const complexityFactor = Math.max(0.3, 1 - (complexityScore / 20));
            totalConfidence += complexityFactor;
            factors++;
        }
        
        return factors > 0 ? totalConfidence / factors : 0;
    }
    
    /**
     * Generates analysis summary for reporting
     */
    public static generateAnalysisSummary(result: DeepAnalysisResult): {
        category: string;
        subcategory: string;
        confidence: string;
        keyFindings: string[];
        recommendations: string[];
        warnings: string[];
    } {
        const keyFindings: string[] = [];
        const recommendations: string[] = [];
        const warnings: string[] = [];
        
        // Category findings
        if (result.categoryAnalysis.detectedCategories.length > 0) {
            const primaryCategory = result.categoryAnalysis.detectedCategories[0];
            keyFindings.push(`Identified as ${primaryCategory.category} with ${(primaryCategory.confidence * 100).toFixed(1)}% confidence`);
            
            if (primaryCategory.evidence.length > 0) {
                keyFindings.push(`Evidence: ${primaryCategory.evidence.join(', ')}`);
            }
        }
        
        // Dependency findings
        if (result.dependencyAnalysis.dependencies.length > 0) {
            const requiredDeps = result.dependencyAnalysis.dependencies.filter(d => d.required);
            if (requiredDeps.length > 0) {
                keyFindings.push(`Requires: ${requiredDeps.map(d => d.name).join(', ')}`);
                recommendations.push('Ensure required packs are installed before using this mod');
            }
        }
        
        // Override findings
        if (result.overrideAnalysis.isOverride) {
            keyFindings.push(`Contains ${result.overrideAnalysis.overriddenResources.length} override(s)`);
            warnings.push('This mod overrides base game content and may conflict with other mods');
            
            if (result.overrideAnalysis.overrideType !== 'unknown') {
                keyFindings.push(`Override type: ${result.overrideAnalysis.overrideType}`);
            }
        }
        
        // Conflict warnings
        if (result.dependencyAnalysis.potentialConflicts.length > 0) {
            const highSeverityConflicts = result.dependencyAnalysis.potentialConflicts.filter(c => c.severity === 'high');
            if (highSeverityConflicts.length > 0) {
                warnings.push(`High risk conflicts: ${highSeverityConflicts.map(c => c.description).join(', ')}`);
            }
        }
        
        // Complexity recommendations
        const complexity = result.metadataAnalysis.complexity;
        if (complexity.score > 7) {
            recommendations.push('This is a complex mod - monitor for conflicts and performance impact');
        } else if (complexity.score > 4) {
            recommendations.push('This mod has moderate complexity - test thoroughly before use');
        }
        
        // Path recommendations
        if (result.suggestedPath) {
            recommendations.push(`Suggested location: ${result.suggestedPath}`);
        }
        
        return {
            category: result.primaryCategory,
            subcategory: result.subcategory,
            confidence: `${(result.confidence * 100).toFixed(1)}%`,
            keyFindings,
            recommendations,
            warnings
        };
    }
    
    /**
     * Validates analysis results for consistency
     */
    public static validateAnalysis(result: DeepAnalysisResult): {
        isValid: boolean;
        issues: string[];
        warnings: string[];
    } {
        const issues: string[] = [];
        const warnings: string[] = [];
        
        // Check category consistency
        if (result.categoryAnalysis.detectedCategories.length === 0) {
            issues.push('No categories detected - analysis may be incomplete');
        }
        
        // Check confidence levels
        if (result.confidence < 0.3) {
            warnings.push('Low confidence in analysis results');
        }
        
        // Check override consistency
        if (result.overrideAnalysis.isOverride && result.overrideAnalysis.overriddenResources.length === 0) {
            issues.push('Marked as override but no overridden resources found');
        }
        
        // Check dependency consistency
        const requiredDeps = result.dependencyAnalysis.dependencies.filter(d => d.required);
        if (requiredDeps.length > 5) {
            warnings.push('Unusually high number of required dependencies');
        }
        
        return {
            isValid: issues.length === 0,
            issues,
            warnings
        };
    }
}