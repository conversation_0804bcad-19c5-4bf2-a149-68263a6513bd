<template>
  <div class="mod-card" :class="{ 'mod-card--expanded': isExpanded }">
    <!-- Card Header -->
    <div class="mod-card__header" @click="toggleExpanded">
      <div class="mod-card__header-main">
        <div class="mod-card__title-section">
          <h3 class="mod-card__title">
            {{ getActualModName() }}
          </h3>
          <div class="mod-card__metadata">
            <span class="mod-card__author" v-if="modData?.author">
              by {{ modData.author }}
            </span>
            <span class="mod-card__version" v-if="modData?.version">
              v{{ modData.version }}
            </span>
            <span class="mod-card__confidence" v-if="modData?.metadataConfidence">
              {{ modData.metadataConfidence }}% confidence
            </span>
            <span v-if="modData?.hasStringTable" class="mod-card__stringtable-indicator" title="Mod name extracted from StringTable">
              📋 STBL
            </span>
          </div>
        </div>
        
        <div class="mod-card__quick-stats">
          <div class="mod-card__file-info">
            <span class="mod-card__file-size">{{ formatFileSize(modData?.fileSize || 0) }}</span>
            <span
              class="mod-card__file-type"
              :class="`file-type--${(modData?.fileExtension || '.package').slice(1)}`"
              :title="getFileTypeDescription(modData?.fileExtension || '.package')"
            >
              {{ getFileTypeLabel(modData?.fileExtension || '.package') }}
            </span>
          </div>
          
          <div class="mod-card__intelligence-indicators">
            <IntelligenceIndicator
              v-if="hasResourceIntelligence"
              :type="intelligenceType"
              :quality-score="modData?.qualityScore || 0"
              :risk-level="modData?.riskLevel || 'unknown'"
            />
            <div v-if="modData?.qualityScore" class="quality-indicator">
              <QualityBadge
                :score="modData.qualityScore"
                size="sm"
              />
              <span class="quality-description">{{ getQualityDescription(modData.qualityScore) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <button class="mod-card__expand-button" :class="{ 'expanded': isExpanded }">
        <ChevronDownIcon class="mod-card__expand-icon" />
      </button>
    </div>
    
    <!-- Expanded Content -->
    <Transition name="expand" appear>
      <div v-if="isExpanded" class="mod-card__content">
        <!-- Mod Summary Section -->
        <div class="mod-card__section mod-card__summary">
          <h4 class="mod-card__section-title">
            <StarIcon class="mod-card__section-icon" />
            What This Mod Adds
          </h4>
          <div class="mod-summary">
            <p class="mod-summary__description">{{ getModDescription() }}</p>
            <div class="mod-summary__tags">
              <span class="mod-tag mod-tag--category" :class="`mod-tag--${getModCategory()}`">
                <component :is="getCategoryIcon()" class="mod-tag__icon" />
                {{ getModCategoryLabel() }}
              </span>
              <span class="mod-tag mod-tag--content">
                {{ getContentType() }}
              </span>
              <span v-if="modData?.author" class="mod-tag mod-tag--author">
                by {{ modData.author }}
              </span>
              <span v-if="(modData?.fileExtension || '.package') === '.ts4script'" class="mod-tag mod-tag--script">
                Script Mod
              </span>
            </div>
          </div>
        </div>

        <!-- Resource Intelligence Section -->
        <div v-if="hasResourceIntelligence" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <CpuChipIcon class="mod-card__section-icon" />
            {{ intelligenceType }} Analysis
          </h4>
          
          <!-- Script Intelligence -->
          <ScriptIntelligenceDisplay
            v-if="isScript && scriptIntelligence"
            :intelligence="scriptIntelligence"
          />
          
          <!-- Package Intelligence -->
          <ResourceIntelligenceDisplay
            v-else-if="isPackage && resourceIntelligence"
            :intelligence="resourceIntelligence"
            :resource-count="modData?.resourceCount || 0"
          />
        </div>

        <!-- Enhanced CAS Content Section -->
        <div v-if="hasCASContent" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <UserIcon class="mod-card__section-icon" />
            Create-a-Sim Content
          </h4>

          <!-- Universal Subcategory Display -->
          <UniversalSubcategoryDisplay
            v-if="universalClassificationData"
            :classification-result="universalClassificationData"
          />

          <!-- Fallback: Hair Classification Display (backward compatibility) -->
          <HairClassificationDisplay
            v-else-if="hairClassificationData"
            :hair-details="hairClassificationData"
          />

          <!-- General CAS Content Info -->
          <div v-else class="cas-content-info">
            <div class="cas-content-summary">
              <span class="cas-content-type">{{ getCASContentType() }}</span>
              <span class="cas-content-description">{{ getCASContentDescription() }}</span>
            </div>
          </div>
        </div>

        <!-- Enhanced Object Content Section -->
        <div v-if="hasObjectContent" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <CubeIcon class="mod-card__section-icon" />
            Object Content
          </h4>

          <!-- Universal Subcategory Display for Objects -->
          <UniversalSubcategoryDisplay
            v-if="objectClassificationData"
            :classification-result="objectClassificationData"
          />

          <!-- General Object Content Info -->
          <div v-else class="object-content-info">
            <div class="object-content-summary">
              <span class="object-content-type">{{ getObjectContentType() }}</span>
              <span class="object-content-description">{{ getObjectContentDescription() }}</span>
            </div>
          </div>
        </div>

        <!-- Quality Assessment Section -->
        <div v-if="qualityAssessment" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <StarIcon class="mod-card__section-icon" />
            Quality Assessment
          </h4>
          <QualityAssessmentDisplay :assessment="qualityAssessment" />
        </div>
        
        <!-- Dependencies Section -->
        <div v-if="dependencies" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <LinkIcon class="mod-card__section-icon" />
            Dependencies & Conflicts
          </h4>
          <DependencyDisplay :dependencies="dependencies" />
        </div>
        
        <!-- Performance Impact -->
        <div v-if="performanceData" class="mod-card__section">
          <h4 class="mod-card__section-title">
            <BoltIcon class="mod-card__section-icon" />
            Performance Impact
          </h4>
          <PerformanceDisplay :performance="performanceData" />
        </div>

        <!-- Installation & Compatibility -->
        <div class="mod-card__section">
          <h4 class="mod-card__section-title">
            <CogIcon class="mod-card__section-icon" />
            Installation & Compatibility
          </h4>
          <div class="mod-card__installation-info">
            <div class="installation-note">
              <strong>Installation:</strong> {{ getInstallationNotes() }}
            </div>
            <div v-if="(modData?.fileExtension || '.package') === '.ts4script'" class="compatibility-warning">
              <strong>⚠️ Script Mod:</strong> Enable script mods in Game Options > Other > Script Mods Allowed
            </div>
            <div v-if="getExpansionRequirements().length > 0" class="expansion-requirements">
              <strong>Requires:</strong> {{ getExpansionRequirements().join(', ') }}
            </div>
            <div class="compatibility-info">
              <strong>Compatibility:</strong> {{ getGameCompatibility() }}
            </div>
            <div class="file-info">
              <strong>File Type:</strong> {{ getFileTypeDescription(modData?.fileExtension || '.package') }}
            </div>
          </div>
        </div>
        
        <!-- Raw Data (Debug Mode) -->
        <div v-if="showDebugInfo" class="mod-card__section mod-card__section--debug">
          <h4 class="mod-card__section-title">
            <CodeBracketIcon class="mod-card__section-icon" />
            Raw Analysis Data
          </h4>
          <pre class="mod-card__debug-data">{{ JSON.stringify(modData, null, 2) }}</pre>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  ChevronDownIcon,
  CpuChipIcon,
  StarIcon,
  LinkIcon,
  BoltIcon,
  CodeBracketIcon,
  CogIcon,
  UserIcon,
  HomeIcon,
  PuzzlePieceIcon,
  GlobeAltIcon,
  CommandLineIcon,
  CubeIcon
} from '@heroicons/vue/24/outline';

import IntelligenceIndicator from './IntelligenceIndicator.vue';
import QualityBadge from './QualityBadge.vue';
import ScriptIntelligenceDisplay from './ScriptIntelligenceDisplay.vue';
import ResourceIntelligenceDisplay from './ResourceIntelligenceDisplay.vue';
import QualityAssessmentDisplay from './QualityAssessmentDisplay.vue';
import DependencyDisplay from './DependencyDisplay.vue';
import PerformanceDisplay from './PerformanceDisplay.vue';
import HairClassificationDisplay from './HairClassificationDisplay.vue';
import UniversalSubcategoryDisplay from './UniversalSubcategoryDisplay.vue';

interface ModData {
  fileName: string;
  fileExtension: string;
  fileSize: number;
  author?: string;
  version?: string;
  metadataConfidence?: number;
  qualityScore?: number;
  riskLevel?: string;
  resourceCount?: number;
  hasIntelligence: boolean;
  hasResourceIntelligence: boolean;
  intelligenceType: string;
  resourceIntelligenceData?: any;
  scriptIntelligenceData?: any;
  qualityAssessmentData?: any;
  dependencyData?: any;
  processingTime: number;

  // Phase 1: StringTable Analysis Fields
  stringTableData?: {
    modName?: string;
    description?: string;
    itemNames: string[];
    customStringCount: number;
    locale: string;
    confidence: number;
    processingTime: number;
  };
  actualModName?: string;
  actualDescription?: string;
  extractedItemNames?: string[];
  hasStringTable?: boolean;

  // Enhanced CAS Content Analysis Fields
  casContent?: {
    totalItems: number;
    items: Array<{
      category: string;
      subcategory: string;
      description: string;
      isHair: boolean;
      isClothing: boolean;
      isMakeup: boolean;
      isAccessory: boolean;
      hairDetails?: {
        length: string;
        style: string[];
        texture: string;
        hasAccessories: boolean;
        confidence: number;
        detectionMethod: string;
        keywords: string[];
      };
      tags: string[];
    }>;
  };

  // Enhanced Object Content Analysis Fields
  objectContent?: {
    totalItems: number;
    items: Array<{
      category: string;
      subcategory: string;
      roomAssignment: string;
      function: string;
      style: string;
      price: number;
      isDecor: boolean;
      isFunctional: boolean;
      tags: string[];
      description: string;
    }>;
  };

  // Enhanced Classification Data for UI Display
  objectClassification?: {
    category: string;
    subcategories: string[];
    specificType: string;
    confidence: number;
    detectionMethod: string;
    roomTypes: string[];
    functionality: string[];
    tags: string[];
    metadata: {
      priceRange?: string;
      description?: string;
    };
  };

  universalClassification?: {
    category: string;
    subcategories: string[];
    confidence: number;
    detectionMethod: string;
    keywords: string[];
    metadata: any;
    tags: string[];
  };
}

const props = defineProps<{
  modData: ModData;
  showDebugInfo?: boolean;
}>();

const isExpanded = ref(false);

// Computed properties
const isScript = computed(() => (props.modData?.fileExtension || '.package') === '.ts4script');
const isPackage = computed(() => (props.modData?.fileExtension || '.package') === '.package');

const hasResourceIntelligence = computed(() => props.modData?.hasResourceIntelligence || false);

const intelligenceType = computed(() => {
  if (isScript.value && hasResourceIntelligence.value) return 'Script Intelligence';
  if (isPackage.value && hasResourceIntelligence.value) return 'Resource Intelligence';
  if (props.modData?.hasIntelligence) return 'Basic Intelligence';
  return 'No Intelligence';
});

const scriptIntelligence = computed(() => {
  return isScript.value ? props.modData?.resourceIntelligenceData : null;
});

const resourceIntelligence = computed(() => {
  return isPackage.value ? props.modData?.resourceIntelligenceData : null;
});

const qualityAssessment = computed(() => props.modData?.qualityAssessmentData);
const dependencies = computed(() => props.modData?.dependencyData);

const performanceData = computed(() => {
  if (scriptIntelligence.value?.performance) {
    return scriptIntelligence.value.performance;
  }
  if (resourceIntelligence.value?.performance) {
    return resourceIntelligence.value.performance;
  }
  return null;
});

// Enhanced CAS Content computed properties
const hasCASContent = computed(() => {
  const fileName = (props.modData?.fileName || '').toLowerCase();
  return fileName.includes('hair') ||
         fileName.includes('clothing') ||
         fileName.includes('makeup') ||
         fileName.includes('skin') ||
         fileName.includes('cas') ||
         (resourceIntelligence.value?.resourceBreakdown?.cas > 0);
});

const hairClassificationData = computed(() => {
  // Check if this is a hair mod and if we have enhanced CAS data
  const fileName = (props.modData?.fileName || '').toLowerCase();
  if (!fileName.includes('hair')) return null;

  // Look for enhanced hair classification data in the mod data
  // This would come from the enhanced CAS analysis
  return props.modData?.casContent?.items?.[0]?.hairDetails || null;
});

const universalClassificationData = computed(() => {
  // Get universal classification data for CAS content
  const fileName = (props.modData?.fileName || '').toLowerCase();

  // Check if we have enhanced universal classification data
  if (props.modData?.universalClassification) {
    return props.modData.universalClassification;
  }

  // Generate classification data from available information
  if (hasCASContent.value) {
    const casContent = props.modData?.casContent?.items?.[0];
    if (casContent) {
      return {
        category: casContent.category || 'unknown',
        subcategories: [casContent.subcategory || 'unknown'],
        confidence: 0.8,
        detectionMethod: 'filename',
        keywords: fileName.split(/[\s_-]+/).filter(k => k.length > 2),
        metadata: {},
        tags: casContent.tags || []
      };
    }
  }

  return null;
});

const hasObjectContent = computed(() => {
  const fileName = (props.modData?.fileName || '').toLowerCase();
  return fileName.includes('furniture') ||
         fileName.includes('decoration') ||
         fileName.includes('appliance') ||
         fileName.includes('door') ||
         fileName.includes('window') ||
         fileName.includes('wall') ||
         fileName.includes('floor') ||
         (resourceIntelligence.value?.resourceBreakdown?.objects > 0);
});

const objectClassificationData = computed(() => {
  // Get universal classification data for object content
  const fileName = (props.modData?.fileName || '').toLowerCase();

  // Check if we have enhanced universal classification data for objects
  if (props.modData?.objectClassification) {
    return props.modData.objectClassification;
  }

  // Generate classification data from available information
  if (hasObjectContent.value) {
    const objectContent = props.modData?.objectContent?.items?.[0];
    if (objectContent) {
      return {
        category: objectContent.category || 'furniture',
        subcategories: [objectContent.subcategory || 'unknown'],
        confidence: 0.7,
        detectionMethod: 'filename',
        keywords: fileName.split(/[\s_-]+/).filter(k => k.length > 2),
        metadata: {},
        tags: objectContent.tags || []
      };
    }
  }

  return null;
});

// Methods
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

const getQualityDescription = (score: number): string => {
  if (score >= 90) return 'Excellent - High quality, no issues detected';
  if (score >= 80) return 'Very Good - Minor optimization opportunities';
  if (score >= 70) return 'Good - Some areas for improvement';
  if (score >= 60) return 'Fair - Several issues to address';
  if (score >= 50) return 'Poor - Multiple problems detected';
  return 'Very Poor - Significant issues found';
};

const getDisplayName = (fileName: string): string => {
  // Remove file extension and clean up the name for better readability
  let name = fileName.replace(/\.(package|ts4script)$/i, '');

  // Replace underscores with spaces and capitalize words
  name = name.replace(/_/g, ' ');

  // Capitalize each word
  name = name.replace(/\b\w/g, l => l.toUpperCase());

  return name;
};

const getActualModName = (): string => {
  // Use actual mod name from StringTable if available, otherwise fall back to filename
  if (props.modData?.actualModName) {
    return props.modData.actualModName;
  }

  return getDisplayName(props.modData?.fileName || 'Unknown');
};

const getFileTypeLabel = (extension: string): string => {
  switch (extension.toLowerCase()) {
    case '.package':
      return 'Package';
    case '.ts4script':
      return 'Script';
    default:
      return extension;
  }
};

const getFileTypeDescription = (extension: string): string => {
  switch (extension.toLowerCase()) {
    case '.package':
      return 'Sims 4 Package File - Contains game assets like CAS items, objects, lots, or gameplay modifications';
    case '.ts4script':
      return 'Sims 4 Script File - Contains Python code that modifies game behavior and adds new functionality';
    default:
      return `${extension} file`;
  }
};

const getModDescription = (): string => {
  // Use actual description from StringTable if available
  if (props.modData?.actualDescription) {
    return props.modData.actualDescription;
  }

  const fileName = (props.modData?.fileName || '').toLowerCase();
  const isScript = (props.modData?.fileExtension || '.package') === '.ts4script';

  // More detailed analysis for specific content types
  if (fileName.includes('hair')) {
    if (fileName.includes('male')) return 'Adds new hairstyles for male Sims in Create-a-Sim.';
    if (fileName.includes('female')) return 'Adds new hairstyles for female Sims in Create-a-Sim.';
    if (fileName.includes('child')) return 'Adds new hairstyles for child Sims in Create-a-Sim.';
    return 'Adds new hairstyles for your Sims in Create-a-Sim.';
  }

  if (fileName.includes('clothing') || fileName.includes('outfit')) {
    if (fileName.includes('formal')) return 'Adds new formal clothing options for your Sims.';
    if (fileName.includes('casual')) return 'Adds new casual clothing options for your Sims.';
    if (fileName.includes('athletic')) return 'Adds new athletic wear for your Sims.';
    return 'Adds new clothing and outfit options for your Sims.';
  }

  if (fileName.includes('skin') || fileName.includes('makeup')) {
    return 'Adds new skin tones, makeup, or facial details for Create-a-Sim.';
  }

  if (fileName.includes('trait')) {
    return 'Adds new personality traits that affect Sim behavior and interactions.';
  }

  if (fileName.includes('career') || fileName.includes('job')) {
    return 'Adds new career paths and job opportunities for your Sims.';
  }

  if (fileName.includes('skill')) {
    return 'Adds new skills for your Sims to learn and master.';
  }

  if (fileName.includes('furniture') || fileName.includes('object')) {
    return 'Adds new furniture and decorative objects for Build/Buy mode.';
  }

  if (fileName.includes('lot') || fileName.includes('house')) {
    return 'Adds new residential or community lots for your Sims to live in or visit.';
  }

  if (fileName.includes('world') || fileName.includes('neighborhood')) {
    return 'Adds new worlds or neighborhoods for your Sims to explore.';
  }

  if (isScript) {
    if (fileName.includes('mccc') || fileName.includes('command')) return 'Advanced mod that adds extensive gameplay control and customization options. Requires script mods enabled.';
    if (fileName.includes('ui') || fileName.includes('interface')) return 'Modifies the game\'s user interface and menus. Requires script mods enabled.';
    if (fileName.includes('autonomy')) return 'Modifies how Sims behave autonomously and make decisions. Requires script mods enabled.';
    return 'Script mod that modifies game behavior and adds new functionality. Requires script mods to be enabled.';
  }

  // Fallback based on file analysis
  if (fileName.includes('cas')) return 'Adds new Create-a-Sim content for customizing your Sims\' appearance.';
  if (fileName.includes('build')) return 'Adds new Build/Buy mode content for decorating and furnishing.';
  if (fileName.includes('gameplay')) return 'Modifies or enhances core gameplay mechanics and features.';

  return 'Enhances your Sims 4 experience with additional content or features.';
};

const getModCategory = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();
  const isScript = (props.modData?.fileExtension || '.package') === '.ts4script';

  // More specific categorization
  if (fileName.includes('hair') || fileName.includes('clothing') || fileName.includes('skin') || fileName.includes('makeup') || fileName.includes('cas')) return 'cas';
  if (fileName.includes('build') || fileName.includes('furniture') || fileName.includes('object') || fileName.includes('decor')) return 'buildbuy';
  if (fileName.includes('trait') || fileName.includes('career') || fileName.includes('skill') || fileName.includes('gameplay')) return 'gameplay';
  if (fileName.includes('lot') || fileName.includes('world') || fileName.includes('venue') || fileName.includes('neighborhood')) return 'world';
  if (isScript) return 'script';

  return 'general';
};

const getModCategoryLabel = (): string => {
  const category = getModCategory();

  switch (category) {
    case 'cas': return 'Create-a-Sim';
    case 'buildbuy': return 'Build/Buy';
    case 'gameplay': return 'Gameplay';
    case 'world': return 'World/Lots';
    case 'script': return 'Script Mod';
    default: return 'General';
  }
};

const getContentType = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  // Specific content type detection
  if (fileName.includes('hair')) return 'Hair';
  if (fileName.includes('clothing') || fileName.includes('outfit')) return 'Clothing';
  if (fileName.includes('skin')) return 'Skin';
  if (fileName.includes('makeup')) return 'Makeup';
  if (fileName.includes('trait')) return 'Traits';
  if (fileName.includes('career')) return 'Careers';
  if (fileName.includes('skill')) return 'Skills';
  if (fileName.includes('furniture')) return 'Furniture';
  if (fileName.includes('object')) return 'Objects';
  if (fileName.includes('lot')) return 'Lots';
  if (fileName.includes('world')) return 'Worlds';

  return getModCategoryLabel();
};

const getInstallationNotes = (): string => {
  const isScript = (props.modData?.fileExtension || '.package') === '.ts4script';

  if (isScript) {
    return 'Requires script mods to be enabled in game options';
  }

  return 'Place in Mods folder';
};

const getCategoryIcon = () => {
  const category = getModCategory();

  switch (category) {
    case 'cas': return UserIcon;
    case 'buildbuy': return HomeIcon;
    case 'gameplay': return PuzzlePieceIcon;
    case 'world': return GlobeAltIcon;
    case 'script': return CommandLineIcon;
    default: return CogIcon;
  }
};

const getExpansionRequirements = (): string[] => {
  const fileName = (props.modData?.fileName || '').toLowerCase();
  const requirements: string[] = [];

  // Detect expansion pack requirements from filename
  if (fileName.includes('gtw') || fileName.includes('gettowork')) requirements.push('Get to Work');
  if (fileName.includes('gts') || fileName.includes('gettogether')) requirements.push('Get Together');
  if (fileName.includes('cl') || fileName.includes('cityliving')) requirements.push('City Living');
  if (fileName.includes('cats') || fileName.includes('catsdogs')) requirements.push('Cats & Dogs');
  if (fileName.includes('seasons')) requirements.push('Seasons');
  if (fileName.includes('fame') || fileName.includes('getfamous')) requirements.push('Get Famous');
  if (fileName.includes('island') || fileName.includes('islandliving')) requirements.push('Island Living');
  if (fileName.includes('magic') || fileName.includes('realmofmagic')) requirements.push('Realm of Magic');
  if (fileName.includes('discover') || fileName.includes('discoveruniversity')) requirements.push('Discover University');
  if (fileName.includes('eco') || fileName.includes('ecolifestyle')) requirements.push('Eco Lifestyle');
  if (fileName.includes('snowy') || fileName.includes('snowyescape')) requirements.push('Snowy Escape');
  if (fileName.includes('cottage') || fileName.includes('cottageliving')) requirements.push('Cottage Living');
  if (fileName.includes('werewolves')) requirements.push('Werewolves');
  if (fileName.includes('highschool')) requirements.push('High School Years');
  if (fileName.includes('growing') || fileName.includes('growingtogether')) requirements.push('Growing Together');

  return requirements;
};

const getGameCompatibility = (): string => {
  const isScript = (props.modData?.fileExtension || '.package') === '.ts4script';

  if (isScript) {
    return 'Requires latest game version for script compatibility';
  }

  return 'Compatible with all game versions';
};

// Enhanced CAS Content Methods
const getCASContentType = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  if (fileName.includes('hair')) return 'Hair';
  if (fileName.includes('clothing') || fileName.includes('outfit')) return 'Clothing';
  if (fileName.includes('makeup')) return 'Makeup';
  if (fileName.includes('skin')) return 'Skin Details';
  if (fileName.includes('accessory')) return 'Accessories';

  return 'Create-a-Sim Content';
};

const getCASContentDescription = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  if (fileName.includes('hair')) {
    return 'Adds new hairstyles for your Sims in Create-a-Sim';
  }
  if (fileName.includes('clothing')) {
    return 'Adds new clothing options for your Sims';
  }
  if (fileName.includes('makeup')) {
    return 'Adds new makeup options for your Sims';
  }
  if (fileName.includes('skin')) {
    return 'Adds new skin details and overlays';
  }

  return 'Enhances Create-a-Sim customization options';
};

// Enhanced Object Content Methods
const getObjectContentType = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  if (fileName.includes('furniture') || fileName.includes('chair') || fileName.includes('sofa') || fileName.includes('table')) return 'Furniture';
  if (fileName.includes('decoration') || fileName.includes('art') || fileName.includes('plant') || fileName.includes('lamp')) return 'Decorations';
  if (fileName.includes('appliance') || fileName.includes('stove') || fileName.includes('fridge') || fileName.includes('tv')) return 'Appliances';
  if (fileName.includes('door') || fileName.includes('window') || fileName.includes('wall') || fileName.includes('floor')) return 'Build Items';

  return 'Object Content';
};

const getObjectContentDescription = (): string => {
  const fileName = (props.modData?.fileName || '').toLowerCase();

  if (fileName.includes('furniture')) {
    return 'Adds new furniture pieces for your Sims\' homes';
  }
  if (fileName.includes('decoration')) {
    return 'Adds new decorative items to enhance your builds';
  }
  if (fileName.includes('appliance')) {
    return 'Adds new functional appliances for your Sims';
  }
  if (fileName.includes('door') || fileName.includes('window')) {
    return 'Adds new architectural elements for building';
  }

  return 'Enhances Build/Buy mode with new objects';
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
</script>

<style scoped>
.mod-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  transition: all var(--duration-200) var(--ease-out);
  overflow: hidden;
}

.mod-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--border-medium);
}

.mod-card--expanded {
  box-shadow: var(--shadow-xl);
}

/* Header - Enhanced Apple-inspired design */
.mod-card__header {
  padding: var(--space-5) var(--space-6);
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  transition: all var(--duration-200) var(--ease-out);
  border-bottom: 1px solid transparent;
}

.mod-card__header:hover {
  background-color: var(--bg-secondary);
  border-bottom-color: var(--border-light);
}

.mod-card__header-main {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  width: 100%;
  margin-right: var(--space-4);
}

.mod-card__title-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  min-width: 0;
}

.mod-card__title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--leading-tight);
  word-break: break-word;
}

.mod-card__metadata {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  align-items: center;
}

.mod-card__author {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  border: 1px solid var(--border-light);
}

.mod-card__version {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--sims-blue);
  background: var(--sims-blue-bg);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--sims-blue-light);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.mod-card__confidence {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--plumbob-green-dark);
  background: var(--plumbob-green-bg);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--plumbob-green-light);
}

.mod-card__stringtable-indicator {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--sims-purple);
  background: var(--sims-purple-bg);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--sims-purple-light);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.mod-card__quick-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border-light);
}

.mod-card__file-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.mod-card__file-size {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.mod-card__file-type {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.file-type--package {
  color: var(--sims-blue-dark);
  background: var(--sims-blue-bg);
  border: 1px solid var(--sims-blue-light);
}

.file-type--ts4script {
  color: var(--sims-purple);
  background: var(--sims-purple-bg);
  border: 1px solid var(--sims-purple-light);
}

.mod-card__intelligence-indicators {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.quality-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.quality-description {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
}

/* Expand Button - Enhanced Apple-style */
.mod-card__expand-button {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-full);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

.mod-card__expand-button:hover {
  background: var(--plumbob-green-bg);
  border-color: var(--plumbob-green-light);
  transform: scale(1.05);
}

.mod-card__expand-button.expanded {
  background: var(--plumbob-green);
  border-color: var(--plumbob-green-dark);
  color: white;
}

.mod-card__expand-icon {
  width: 16px;
  height: 16px;
  transition: transform var(--duration-200) var(--ease-out);
}

.mod-card__expand-button.expanded .mod-card__expand-icon {
  transform: rotate(180deg);
}

/* Content Section - Enhanced spacing and hierarchy */
.mod-card__content {
  border-top: 1px solid var(--border-light);
  background: var(--bg-secondary);
}

.mod-card__section {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.mod-card__section:last-child {
  border-bottom: none;
}

.mod-card__section-title {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  line-height: var(--leading-tight);
}

.mod-card__section-icon {
  width: 20px;
  height: 20px;
  color: var(--sims-blue);
  flex-shrink: 0;
}

/* Summary Section */
.mod-summary {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.mod-summary__description {
  font-size: var(--text-base);
  line-height: var(--leading-relaxed);
  color: var(--text-secondary);
  margin: 0;
}

.mod-summary__tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.mod-tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  border: 1px solid;
  transition: all var(--duration-150) var(--ease-out);
}

.mod-tag:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.mod-tag__icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.mod-tag--category {
  background: var(--bg-elevated);
  border-color: var(--border-medium);
  color: var(--text-primary);
}

.mod-tag--content {
  background: var(--sims-blue-bg);
  border-color: var(--sims-blue-light);
  color: var(--sims-blue-dark);
}

.mod-tag--author {
  background: var(--sims-purple-bg);
  border-color: var(--sims-purple-light);
  color: var(--sims-purple);
}

.mod-tag--script {
  background: var(--sims-orange-bg);
  border-color: var(--sims-orange-light);
  color: var(--sims-orange);
}

/* CAS and Object Content Sections */
.cas-content-info,
.object-content-info {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--bg-secondary) 100%);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-top: var(--space-3);
}

.cas-content-summary,
.object-content-summary {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.cas-content-type,
.object-content-type {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--sims-blue);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.cas-content-description,
.object-content-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

/* Expand/Collapse Animation */
.expand-enter-active,
.expand-leave-active {
  transition: all var(--duration-300) var(--ease-out);
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 2000px;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .mod-card__header {
    padding: var(--space-4);
  }

  .mod-card__header-main {
    gap: var(--space-3);
  }

  .mod-card__title {
    font-size: var(--text-lg);
  }

  .mod-card__metadata {
    gap: var(--space-2);
  }

  .mod-card__quick-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .mod-card__section {
    padding: var(--space-4);
  }

  .mod-summary__tags {
    gap: var(--space-1);
  }

  .mod-tag {
    font-size: 10px;
    padding: var(--space-1) var(--space-2);
  }
}

@media (max-width: 480px) {
  .mod-card__header {
    padding: var(--space-3);
  }

  .mod-card__title {
    font-size: var(--text-base);
  }

  .mod-card__metadata {
    flex-direction: column;
    align-items: flex-start;
  }

  .mod-card__section {
    padding: var(--space-3);
  }
}

.mod-card__author {
  font-weight: var(--font-medium);
  color: var(--text-accent);
}

.mod-card__version {
  padding: var(--space-1) var(--space-2);
  background: var(--info-bg);
  color: var(--info);
  border-radius: var(--radius-sm);
  font-weight: var(--font-medium);
}

.mod-card__confidence {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.mod-card__quick-stats {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.mod-card__file-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.mod-card__file-size {
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}

.mod-card__file-type {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
}

.file-type--package {
  background: var(--sims-blue-bg);
  color: var(--sims-blue);
}

.file-type--ts4script {
  background: var(--sims-purple-bg);
  color: var(--sims-purple);
}

.mod-card__intelligence-indicators {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.mod-card__expand-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: var(--radius-md);
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.mod-card__expand-button:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.mod-card__expand-icon {
  width: 20px;
  height: 20px;
  transition: transform var(--duration-200) var(--ease-out);
}

.mod-card__expand-button.expanded .mod-card__expand-icon {
  transform: rotate(180deg);
}

/* Content */
.mod-card__content {
  border-top: 1px solid var(--border-light);
  padding: var(--card-padding);
  background: var(--bg-secondary);
}

.mod-card__section {
  margin-bottom: var(--space-6);
}

.mod-card__section:last-child {
  margin-bottom: 0;
}

.mod-card__section-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

.mod-card__section-icon {
  width: 20px;
  height: 20px;
  color: var(--text-accent);
}

.mod-card__section--debug {
  border-top: 1px solid var(--border-light);
  padding-top: var(--space-6);
  margin-top: var(--space-6);
}

.mod-card__debug-data {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

/* Transitions */
.expand-enter-active,
.expand-leave-active {
  transition: all var(--duration-300) var(--ease-out);
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 1000px;
}

/* Quality Indicator Styles */
.quality-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.quality-description {
  font-size: 0.75rem;
  color: var(--text-secondary);
  text-align: center;
  font-weight: 500;
  max-width: 120px;
  line-height: 1.2;
}

/* Mod Summary Styles */
.mod-card__summary {
  background: var(--bg-subtle);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.mod-summary__description {
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
}

.mod-summary__tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.mod-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mod-tag__icon {
  width: 0.875rem;
  height: 0.875rem;
}

.mod-tag--cas {
  background: #e3f2fd;
  color: #1565c0;
}

.mod-tag--buildbuy {
  background: #f3e5f5;
  color: #7b1fa2;
}

.mod-tag--gameplay {
  background: #e8f5e8;
  color: #2e7d32;
}

.mod-tag--world {
  background: #fff3e0;
  color: #ef6c00;
}

.mod-tag--script {
  background: #fce4ec;
  color: #c2185b;
}

.mod-tag--general {
  background: var(--bg-muted);
  color: var(--text-secondary);
}

.mod-tag--author {
  background: var(--accent-subtle);
  color: var(--accent-primary);
}

.mod-tag--content {
  background: #f0f4f8;
  color: #334155;
  font-weight: 600;
}

.mod-card__installation-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.installation-note,
.compatibility-warning,
.expansion-requirements,
.compatibility-info,
.file-info {
  padding: 0.5rem;
  border-radius: 0.375rem;
  background: var(--bg-subtle);
}

.compatibility-warning {
  background: #fef3cd;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.expansion-requirements {
  background: #e0f2fe;
  color: #0277bd;
  border: 1px solid #29b6f6;
}

.compatibility-info {
  background: #e8f5e8;
  color: #2e7d32;
}

/* Enhanced CAS Content Styles */
.cas-content-info {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid var(--sims-blue-light);
  border-radius: var(--radius-lg);
  padding: 1rem;
}

.cas-content-summary {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cas-content-type {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--sims-blue-dark);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.cas-content-description {
  font-size: 0.875rem;
  color: var(--gray-600);
  line-height: 1.5;
}
</style>
