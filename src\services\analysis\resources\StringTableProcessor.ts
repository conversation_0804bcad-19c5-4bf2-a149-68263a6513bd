import type { ResourceEntry } from '@s4tk/models/types';
import { StringTableResource } from '@s4tk/models';
import type { IResourceProcessor, ProcessedResource, ResourceProcessingOptions } from './types';
import type { StringTableData } from '../../../types/analysis';
import { URT } from '../../../constants/unifiedResourceTypes';
import { GenericResourceProcessor } from './GenericResourceProcessor';
import { errorHandler } from '../../s4tk/ErrorHandler';

/**
 * Specialized processor for StringTable resources
 * Implements Phase 1 StringTable analysis with S4TK integration
 */
export class StringTableProcessor implements IResourceProcessor {
    private genericProcessor = new GenericResourceProcessor();

    // Security limits
    private static readonly MAX_STRING_LENGTH = 1000;
    private static readonly MAX_ITEMS = 500;

    canProcess(resourceType: number): boolean {
        return resourceType === URT.StringTable;
    }

    async process(entry: any, options?: ResourceProcessingOptions): Promise<ProcessedResource> {
        const result = await this.genericProcessor.process(entry, options);

        result.type = 'StringTable';
        result.metadata.specialized = true;
        result.metadata.processorUsed = this.getProcessorName();

        // Add StringTable-specific analysis
        try {
            const stringTableData = await this.processStringTable(entry);
            result.metadata.stringTableData = stringTableData;
            result.metadata.modName = stringTableData.modName;
            result.metadata.description = stringTableData.description;
            result.metadata.itemNames = stringTableData.itemNames;
            result.metadata.confidence = stringTableData.confidence;
        } catch (error) {
            console.warn('StringTable processing failed:', error);
            result.metadata.stringTableError = error.message;
        }

        return result;
    }

    /**
     * Process StringTable resource and extract mod metadata
     * @param entry ResourceEntry containing STBL data
     * @returns Promise<StringTableData> Extracted metadata with confidence scoring
     */
    async processStringTable(entry: ResourceEntry): Promise<StringTableData> {
        const startTime = performance.now();

        try {
            // Validate input
            if (!entry) {
                throw new Error('No resource entry provided');
            }

            if (!entry.value) {
                throw new Error('Resource entry has no value');
            }

            // Parse StringTable using S4TK (following ManifestAnalyzer pattern)
            let stringTable: StringTableResource;

            try {
                stringTable = StringTableResource.from(entry.value);
            } catch (s4tkError) {
                // S4TK rejected this as not a valid StringTable
                // This is common - resources may have StringTable type but different format
                throw new Error(`S4TK StringTable parsing failed: ${s4tkError.message}`);
            }

            // Validate that we got a valid StringTable
            if (!stringTable || stringTable.size === 0) {
                throw new Error('StringTable is empty or invalid');
            }

            // Extract metadata with safety checks
            const modName = this.extractModName(stringTable);
            const description = this.extractDescription(stringTable);
            const itemNames = this.extractItemNames(stringTable);

            // Calculate confidence score
            const confidence = this.calculateConfidence(modName, description, itemNames);

            const result: StringTableData = {
                modName,
                description,
                itemNames,
                customStringCount: stringTable.size,
                locale: stringTable.locale || 'unknown',
                confidence,
                processingTime: performance.now() - startTime
            };

            return result;

        } catch (error) {
            errorHandler.handleS4TKError(error, 'processStringTable', entry.id?.toString());

            // Return safe fallback
            return {
                itemNames: [],
                customStringCount: 0,
                locale: 'unknown',
                confidence: 0,
                processingTime: performance.now() - startTime
            };
        }
    }

    /**
     * Extract mod name using pattern matching
     */
    private extractModName(stringTable: StringTableResource): string | undefined {
        const namePatterns = [
            /^MOD_NAME$/i,
            /^TITLE$/i,
            /^DISPLAY_NAME$/i,
            /^PACKAGE_NAME$/i,
            /^NAME$/i,
            // Additional patterns for common mod naming conventions
            /.*_TITLE$/i,
            /.*_NAME$/i,
            /^MODNAME$/i,
            /^MOD$/i,
            // Look for strings that might be mod titles (longer, descriptive strings)
            /^[A-Z_]+_TITLE$/i,
            /^[A-Z_]+_NAME$/i
        ];

        // First pass: exact pattern matching
        for (const [key, value] of stringTable.entries) {
            // Security: Validate string length
            if (value.length > StringTableProcessor.MAX_STRING_LENGTH) continue;

            for (const pattern of namePatterns) {
                if (pattern.test(key)) {
                    const sanitized = this.sanitizeString(value);
                    if (sanitized && sanitized.length > 3) { // Ensure meaningful names
                        return sanitized;
                    }
                }
            }
        }

        // Second pass: look for the first meaningful string that could be a title
        for (const [key, value] of stringTable.entries) {
            if (value.length > StringTableProcessor.MAX_STRING_LENGTH) continue;

            const sanitized = this.sanitizeString(value);
            // Look for strings that seem like titles (proper length, not just numbers/codes)
            if (sanitized &&
                sanitized.length >= 5 &&
                sanitized.length <= 100 &&
                !/^\d+$/.test(sanitized) && // Not just numbers
                !/^[A-F0-9]{8,}$/i.test(sanitized) && // Not hex codes
                /[a-zA-Z]/.test(sanitized) // Contains letters
            ) {
                return sanitized;
            }
        }

        return undefined;
    }

    /**
     * Extract description using pattern matching
     */
    private extractDescription(stringTable: StringTableResource): string | undefined {
        const descPatterns = [
            /^DESCRIPTION$/i,
            /^MOD_DESCRIPTION$/i,
            /^PACKAGE_DESCRIPTION$/i,
            /^INFO$/i,
            /^ABOUT$/i,
            // Additional patterns
            /.*_DESCRIPTION$/i,
            /.*_DESC$/i,
            /.*_INFO$/i,
            /^DESC$/i,
            /^TOOLTIP$/i,
            /^HELP$/i
        ];

        // First pass: exact pattern matching
        for (const [key, value] of stringTable.entries) {
            // Security: Validate string length
            if (value.length > StringTableProcessor.MAX_STRING_LENGTH) continue;

            for (const pattern of descPatterns) {
                if (pattern.test(key)) {
                    const sanitized = this.sanitizeString(value);
                    if (sanitized && sanitized.length > 10) { // Ensure meaningful descriptions
                        return sanitized;
                    }
                }
            }
        }

        // Second pass: look for longer strings that could be descriptions
        for (const [key, value] of stringTable.entries) {
            if (value.length > StringTableProcessor.MAX_STRING_LENGTH) continue;

            const sanitized = this.sanitizeString(value);
            // Look for strings that seem like descriptions (longer, descriptive text)
            if (sanitized &&
                sanitized.length >= 20 &&
                sanitized.length <= 500 &&
                !/^\d+$/.test(sanitized) && // Not just numbers
                !/^[A-F0-9]{8,}$/i.test(sanitized) && // Not hex codes
                /[a-zA-Z]/.test(sanitized) && // Contains letters
                (sanitized.includes(' ') || sanitized.includes('.')) // Contains spaces or periods (sentence-like)
            ) {
                return sanitized;
            }
        }

        return undefined;
    }

    /**
     * Extract item names from string table
     */
    private extractItemNames(stringTable: StringTableResource): string[] {
        const itemPatterns = [
            /^ITEM_\d+_NAME$/i,
            /^CAS_PART_\d+$/i,
            /^OBJECT_\d+_NAME$/i,
            /.*_NAME$/i
        ];

        const itemNames: string[] = [];

        for (const [key, value] of stringTable.entries) {
            // Security: Limit number of items and string length
            if (itemNames.length >= StringTableProcessor.MAX_ITEMS) break;
            if (value.length > StringTableProcessor.MAX_STRING_LENGTH) continue;

            for (const pattern of itemPatterns) {
                if (pattern.test(key)) {
                    const sanitized = this.sanitizeString(value);
                    if (sanitized && !itemNames.includes(sanitized)) {
                        itemNames.push(sanitized);
                    }
                    break;
                }
            }
        }

        return itemNames;
    }

    /**
     * Calculate confidence score based on extracted data
     */
    private calculateConfidence(
        modName?: string,
        description?: string,
        itemNames: string[] = []
    ): number {
        let confidence = 0;

        // Mod name contributes 40% to confidence
        if (modName) {
            confidence += 40;
            // Bonus for meaningful names (not just "Mod" or "Package")
            if (modName.length > 5 && !/^(mod|package)$/i.test(modName)) {
                confidence += 10;
            }
        }

        // Description contributes 30% to confidence
        if (description) {
            confidence += 30;
            // Bonus for detailed descriptions
            if (description.length > 50) {
                confidence += 10;
            }
        }

        // Item names contribute 20% to confidence
        if (itemNames.length > 0) {
            confidence += Math.min(20, itemNames.length * 5);
        }

        return Math.min(100, confidence);
    }

    /**
     * Sanitize string for safe display
     */
    private sanitizeString(input: string): string {
        return input
            .trim()
            .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
            .replace(/[<>]/g, '') // Remove potential HTML
            .substring(0, StringTableProcessor.MAX_STRING_LENGTH);
    }

    getProcessorName(): string {
        return 'StringTableProcessor';
    }
}