/**
 * Enhanced Object Classifier
 * 
 * Advanced categorization system for Sims 4 objects using:
 * 1. SimData parsing for accurate category detection
 * 2. Filename pattern matching with extensive keyword database
 * 3. Resource type analysis
 * 4. Context-aware classification
 * 
 * Inspired by Sims 4 Toolkit and community modding knowledge.
 */

import { SimDataResource } from '@s4tk/models';
import type { ObjectPartInfo, ObjectCategory, ObjectFunction, RoomType } from './types';

/**
 * Enhanced object classification result
 */
export interface EnhancedObjectClassification {
    category: string;
    subcategory: string;
    specificType: string;
    confidence: number;
    detectionMethod: 'simdata' | 'filename' | 'hybrid' | 'fallback';
    roomTypes: string[];
    functionality: string[];
    tags: string[];
    metadata: {
        price?: number;
        brand?: string;
        style?: string;
        size?: string;
    };
}

/**
 * Comprehensive object classification patterns
 */
const OBJECT_PATTERNS = {
    // Furniture - Seating
    seating: {
        keywords: ['chair', 'sofa', 'couch', 'armchair', 'loveseat', 'sectional', 'recliner', 'stool', 'bench', 'ottoman'],
        category: 'furniture',
        subcategory: 'seating',
        functionality: ['seating', 'comfort'],
        roomTypes: ['living', 'dining', 'bedroom', 'office']
    },
    
    // Furniture - Beds
    beds: {
        keywords: ['bed', 'bedframe', 'mattress', 'bunk', 'crib', 'bassinet', 'daybed'],
        category: 'furniture',
        subcategory: 'bed',
        functionality: ['sleeping', 'comfort'],
        roomTypes: ['bedroom', 'kids']
    },
    
    // Furniture - Tables
    tables: {
        keywords: ['table', 'desk', 'nightstand', 'endtable', 'sidetable', 'coffeetable', 'diningtable', 'worktable'],
        category: 'furniture',
        subcategory: 'table',
        functionality: ['surface', 'storage'],
        roomTypes: ['living', 'dining', 'bedroom', 'office']
    },
    
    // Furniture - Storage
    storage: {
        keywords: ['dresser', 'wardrobe', 'closet', 'bookshelf', 'shelf', 'cabinet', 'drawer', 'chest', 'armoire'],
        category: 'furniture',
        subcategory: 'storage',
        functionality: ['storage'],
        roomTypes: ['bedroom', 'living', 'office']
    },
    
    // Electronics
    electronics: {
        keywords: ['tv', 'television', 'computer', 'laptop', 'stereo', 'radio', 'speaker', 'gaming', 'console'],
        category: 'electronics',
        subcategory: 'entertainment',
        functionality: ['entertainment', 'skill'],
        roomTypes: ['living', 'bedroom', 'office']
    },
    
    // Appliances - Kitchen
    kitchen_appliances: {
        keywords: ['fridge', 'refrigerator', 'stove', 'oven', 'microwave', 'dishwasher', 'counter', 'island'],
        category: 'appliances',
        subcategory: 'kitchen',
        functionality: ['cooking', 'food'],
        roomTypes: ['kitchen']
    },
    
    // Appliances - Laundry
    laundry: {
        keywords: ['washer', 'dryer', 'laundry'],
        category: 'appliances',
        subcategory: 'laundry',
        functionality: ['hygiene'],
        roomTypes: ['bathroom', 'basement']
    },
    
    // Plumbing
    plumbing: {
        keywords: ['toilet', 'sink', 'bathtub', 'shower', 'tub', 'basin', 'faucet'],
        category: 'plumbing',
        subcategory: 'bathroom',
        functionality: ['hygiene'],
        roomTypes: ['bathroom']
    },
    
    // Lighting
    lighting: {
        keywords: ['lamp', 'light', 'chandelier', 'sconce', 'ceiling', 'floor', 'table', 'pendant'],
        category: 'lighting',
        subcategory: 'light',
        functionality: ['lighting'],
        roomTypes: ['any']
    },
    
    // Decorations
    decorations: {
        keywords: ['painting', 'art', 'sculpture', 'vase', 'plant', 'flower', 'decoration', 'decor', 'mirror'],
        category: 'decorations',
        subcategory: 'decoration',
        functionality: ['decoration'],
        roomTypes: ['any']
    },
    
    // Outdoor
    outdoor: {
        keywords: ['grill', 'pool', 'hot', 'tub', 'garden', 'outdoor', 'patio', 'deck', 'fence', 'gate'],
        category: 'outdoor',
        subcategory: 'outdoor',
        functionality: ['outdoor'],
        roomTypes: ['outdoor']
    }
};

/**
 * SimData field mappings for object categories
 */
const SIMDATA_CATEGORY_MAPPINGS = {
    // Common SimData fields that indicate object types
    'function': {
        1: 'seating',      // Chair function
        2: 'surface',      // Table/surface function
        3: 'bed',          // Bed function
        4: 'storage',      // Storage function
        5: 'appliance',    // Appliance function
        6: 'plumbing',     // Plumbing function
        7: 'lighting',     // Lighting function
        8: 'decoration',   // Decorative function
        9: 'electronics'   // Electronic function
    },
    'catalog_category': {
        1: 'seating',
        2: 'surfaces',
        3: 'storage',
        4: 'appliances',
        5: 'plumbing',
        6: 'lighting',
        7: 'decorations',
        8: 'electronics'
    }
};

/**
 * Enhanced Object Classifier
 */
export class EnhancedObjectClassifier {
    
    /**
     * Main classification method
     */
    public static classifyObject(
        filename: string,
        simData?: SimDataResource,
        resourceType?: number
    ): EnhancedObjectClassification {
        
        // Try SimData classification first (most accurate)
        if (simData) {
            const simDataResult = this.classifyFromSimData(simData, filename);
            if (simDataResult.confidence > 0.8) {
                return simDataResult;
            }
        }
        
        // Fallback to filename classification
        const filenameResult = this.classifyFromFilename(filename);
        
        // Combine results if both available
        if (simData && filenameResult.confidence > 0.6) {
            return this.combineClassifications(simDataResult!, filenameResult);
        }
        
        return filenameResult;
    }
    
    /**
     * Classify from SimData (most accurate method)
     */
    private static classifyFromSimData(simData: SimDataResource, filename: string): EnhancedObjectClassification {
        const instance = simData.instance;
        let category = 'furniture';
        let subcategory = 'unknown';
        let confidence = 0.9;
        let functionality: string[] = [];
        let roomTypes: string[] = [];
        let metadata: any = {};
        
        try {
            // Check for function field
            if (instance.has('Function')) {
                const functionValue = Number(instance.get('Function'));
                const mappedFunction = SIMDATA_CATEGORY_MAPPINGS.function[functionValue];
                if (mappedFunction) {
                    subcategory = mappedFunction;
                    confidence = 0.95;
                }
            }
            
            // Check for catalog category
            if (instance.has('CatalogCategory')) {
                const catalogValue = Number(instance.get('CatalogCategory'));
                const mappedCategory = SIMDATA_CATEGORY_MAPPINGS.catalog_category[catalogValue];
                if (mappedCategory) {
                    subcategory = mappedCategory;
                    confidence = 0.9;
                }
            }
            
            // Extract price
            if (instance.has('Price')) {
                metadata.price = Number(instance.get('Price'));
            }
            
            // Extract room flags for room type detection
            if (instance.has('RoomFlags')) {
                roomTypes = this.extractRoomTypesFromFlags(instance.get('RoomFlags'));
            }
            
            // Map subcategory to main category
            category = this.mapSubcategoryToMainCategory(subcategory);
            functionality = this.getFunctionalityFromSubcategory(subcategory);
            
        } catch (error) {
            console.warn('Error parsing SimData for object classification:', error);
            confidence = 0.5;
        }
        
        return {
            category,
            subcategory,
            specificType: this.getSpecificTypeFromFilename(filename, subcategory),
            confidence,
            detectionMethod: 'simdata',
            roomTypes: roomTypes.length > 0 ? roomTypes : this.getDefaultRoomTypes(subcategory),
            functionality,
            tags: this.generateTags(category, subcategory, filename),
            metadata
        };
    }
    
    /**
     * Classify from filename patterns
     */
    private static classifyFromFilename(filename: string): EnhancedObjectClassification {
        const lowerFilename = filename.toLowerCase();
        let bestMatch: any = null;
        let highestScore = 0;
        
        // Score each pattern against the filename
        for (const [patternName, pattern] of Object.entries(OBJECT_PATTERNS)) {
            let score = 0;
            let matchedKeywords: string[] = [];
            
            for (const keyword of pattern.keywords) {
                if (lowerFilename.includes(keyword)) {
                    score += keyword.length; // Longer keywords get higher scores
                    matchedKeywords.push(keyword);
                }
            }
            
            // Bonus for multiple keyword matches
            if (matchedKeywords.length > 1) {
                score *= 1.5;
            }
            
            if (score > highestScore) {
                highestScore = score;
                bestMatch = { ...pattern, matchedKeywords, patternName };
            }
        }
        
        if (bestMatch) {
            const confidence = Math.min(0.9, highestScore / 10); // Normalize confidence
            
            return {
                category: bestMatch.category,
                subcategory: bestMatch.subcategory,
                specificType: this.getSpecificTypeFromFilename(filename, bestMatch.subcategory),
                confidence,
                detectionMethod: 'filename',
                roomTypes: bestMatch.roomTypes,
                functionality: bestMatch.functionality,
                tags: this.generateTags(bestMatch.category, bestMatch.subcategory, filename),
                metadata: {}
            };
        }
        
        // Fallback classification
        return {
            category: 'furniture',
            subcategory: 'unknown',
            specificType: 'object',
            confidence: 0.3,
            detectionMethod: 'fallback',
            roomTypes: ['any'],
            functionality: ['decoration'],
            tags: ['custom-content'],
            metadata: {}
        };
    }
    
    /**
     * Helper methods
     */
    private static mapSubcategoryToMainCategory(subcategory: string): string {
        const mapping: Record<string, string> = {
            'seating': 'furniture',
            'bed': 'furniture',
            'table': 'furniture',
            'surface': 'furniture',
            'storage': 'furniture',
            'appliance': 'appliances',
            'kitchen': 'appliances',
            'laundry': 'appliances',
            'plumbing': 'plumbing',
            'bathroom': 'plumbing',
            'lighting': 'lighting',
            'light': 'lighting',
            'decoration': 'decorations',
            'electronics': 'electronics',
            'entertainment': 'electronics',
            'outdoor': 'outdoor'
        };
        
        return mapping[subcategory] || 'furniture';
    }
    
    private static getFunctionalityFromSubcategory(subcategory: string): string[] {
        const mapping: Record<string, string[]> = {
            'seating': ['seating', 'comfort'],
            'bed': ['sleeping', 'comfort'],
            'table': ['surface', 'storage'],
            'storage': ['storage'],
            'appliance': ['cooking', 'food'],
            'plumbing': ['hygiene'],
            'lighting': ['lighting'],
            'decoration': ['decoration'],
            'electronics': ['entertainment']
        };
        
        return mapping[subcategory] || ['decoration'];
    }
    
    private static getDefaultRoomTypes(subcategory: string): string[] {
        const mapping: Record<string, string[]> = {
            'bed': ['bedroom'],
            'seating': ['living', 'dining'],
            'table': ['living', 'dining', 'office'],
            'storage': ['bedroom', 'living'],
            'kitchen': ['kitchen'],
            'bathroom': ['bathroom'],
            'plumbing': ['bathroom'],
            'outdoor': ['outdoor']
        };
        
        return mapping[subcategory] || ['any'];
    }
    
    private static getSpecificTypeFromFilename(filename: string, subcategory: string): string {
        const lowerFilename = filename.toLowerCase();
        
        // Extract specific type based on subcategory and filename
        if (subcategory === 'bed') {
            if (lowerFilename.includes('double') || lowerFilename.includes('queen') || lowerFilename.includes('king')) return 'double bed';
            if (lowerFilename.includes('single') || lowerFilename.includes('twin')) return 'single bed';
            if (lowerFilename.includes('bunk')) return 'bunk bed';
            return 'bed';
        }
        
        if (subcategory === 'seating') {
            if (lowerFilename.includes('sofa') || lowerFilename.includes('couch')) return 'sofa';
            if (lowerFilename.includes('armchair')) return 'armchair';
            if (lowerFilename.includes('dining')) return 'dining chair';
            if (lowerFilename.includes('office')) return 'office chair';
            return 'chair';
        }
        
        if (subcategory === 'table') {
            if (lowerFilename.includes('desk')) return 'desk';
            if (lowerFilename.includes('dining')) return 'dining table';
            if (lowerFilename.includes('coffee')) return 'coffee table';
            if (lowerFilename.includes('night') || lowerFilename.includes('bedside')) return 'nightstand';
            return 'table';
        }
        
        return subcategory;
    }
    
    private static generateTags(category: string, subcategory: string, filename: string): string[] {
        const tags = [category, subcategory, 'custom-content'];
        
        const lowerFilename = filename.toLowerCase();
        
        // Add style tags
        if (lowerFilename.includes('modern')) tags.push('modern');
        if (lowerFilename.includes('vintage') || lowerFilename.includes('retro')) tags.push('vintage');
        if (lowerFilename.includes('rustic')) tags.push('rustic');
        if (lowerFilename.includes('luxury') || lowerFilename.includes('expensive')) tags.push('luxury');
        
        // Add brand tags
        if (lowerFilename.includes('ikea')) tags.push('ikea-style');
        if (lowerFilename.includes('cc')) tags.push('cc');
        
        return tags;
    }
    
    private static extractRoomTypesFromFlags(roomFlags: any): string[] {
        // This would need to be implemented based on actual SimData room flag values
        // For now, return empty array to use defaults
        return [];
    }
    
    private static combineClassifications(
        simDataResult: EnhancedObjectClassification,
        filenameResult: EnhancedObjectClassification
    ): EnhancedObjectClassification {
        // Prefer SimData for category/subcategory, filename for specific details
        return {
            ...simDataResult,
            specificType: filenameResult.specificType,
            tags: [...new Set([...simDataResult.tags, ...filenameResult.tags])],
            detectionMethod: 'hybrid',
            confidence: Math.max(simDataResult.confidence, filenameResult.confidence)
        };
    }
}
