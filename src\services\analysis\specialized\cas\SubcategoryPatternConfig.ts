/**
 * Subcategory Pattern Configuration
 * 
 * Configuration-driven pattern system for unified subcategory detection.
 * This replaces individual classifiers with a single, scalable pattern-based approach.
 * 
 * Part of Unified Subcategory Detection System (v2.2.0)
 */

import { HairLength, HairStyle, HairTexture, CASCategory } from './types';

/**
 * Universal subcategory pattern configuration
 */
export interface SubcategoryPattern {
    category: string;
    subcategory: string;
    patterns: string[];
    confidence: number;
    priority: number;
    tags?: string[];
}

/**
 * Pattern detection result
 */
export interface PatternDetectionResult {
    category: string;
    subcategory: string;
    confidence: number;
    detectedPatterns: string[];
    tags: string[];
    metadata?: Record<string, any>;
}

/**
 * Comprehensive pattern configuration for all content types
 */
export class SubcategoryPatternConfig {
    
    /**
     * Hair length patterns (migrated from HairStyleClassifier)
     */
    public static readonly HAIR_LENGTH_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'hair',
            subcategory: HairLength.VERY_SHORT,
            patterns: ['pixie', 'buzz', 'crew', 'short', 'crop', 'bob'],
            confidence: 0.8,
            priority: 1,
            tags: ['very_short', 'hair']
        },
        {
            category: 'hair',
            subcategory: HairLength.SHORT,
            patterns: ['bob', 'lob', 'chin', 'shoulder', 'short'],
            confidence: 0.8,
            priority: 1,
            tags: ['short', 'hair']
        },
        {
            category: 'hair',
            subcategory: HairLength.MEDIUM,
            patterns: ['medium', 'mid', 'shoulder', 'collarbone', 'lob'],
            confidence: 0.8,
            priority: 1,
            tags: ['medium', 'hair']
        },
        {
            category: 'hair',
            subcategory: HairLength.LONG,
            patterns: ['long', 'waist', 'hip', 'flowing'],
            confidence: 0.8,
            priority: 1,
            tags: ['long', 'hair']
        },
        {
            category: 'hair',
            subcategory: HairLength.VERY_LONG,
            patterns: ['floor', 'ankle', 'very_long', 'extra_long', 'rapunzel'],
            confidence: 0.8,
            priority: 1,
            tags: ['very_long', 'hair']
        }
    ];

    /**
     * Hair style patterns (migrated from HairStyleClassifier)
     */
    public static readonly HAIR_STYLE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'hair',
            subcategory: HairStyle.PONYTAIL,
            patterns: ['ponytail', 'pony', 'tail', 'high_pony', 'low_pony'],
            confidence: 0.9,
            priority: 2,
            tags: ['ponytail', 'hair', 'style']
        },
        {
            category: 'hair',
            subcategory: HairStyle.BUN,
            patterns: ['bun', 'chignon', 'topknot', 'messy_bun', 'high_bun', 'low_bun'],
            confidence: 0.9,
            priority: 2,
            tags: ['bun', 'hair', 'style']
        },
        {
            category: 'hair',
            subcategory: HairStyle.BRAIDS,
            patterns: ['braid', 'braided', 'plait', 'french_braid', 'dutch_braid', 'fishtail'],
            confidence: 0.9,
            priority: 2,
            tags: ['braids', 'hair', 'style']
        },
        {
            category: 'hair',
            subcategory: HairStyle.PIGTAILS,
            patterns: ['pigtails', 'twin_tails', 'twintails', 'pig_tails'],
            confidence: 0.9,
            priority: 2,
            tags: ['pigtails', 'hair', 'style']
        },
        {
            category: 'hair',
            subcategory: HairStyle.UPDO,
            patterns: ['updo', 'up_do', 'formal', 'elegant', 'wedding'],
            confidence: 0.8,
            priority: 2,
            tags: ['updo', 'hair', 'style', 'formal']
        },
        {
            category: 'hair',
            subcategory: HairStyle.HALF_UP,
            patterns: ['half_up', 'halfup', 'half_down', 'halfdown'],
            confidence: 0.9,
            priority: 2,
            tags: ['half_up', 'hair', 'style']
        },
        {
            category: 'hair',
            subcategory: HairStyle.BANGS,
            patterns: ['bangs', 'fringe', 'bang', 'side_bang'],
            confidence: 0.9,
            priority: 2,
            tags: ['bangs', 'hair', 'style']
        },
        {
            category: 'hair',
            subcategory: HairStyle.SIDE_SWEPT,
            patterns: ['side_swept', 'swept', 'side_part', 'asymmetric'],
            confidence: 0.8,
            priority: 2,
            tags: ['side_swept', 'hair', 'style']
        },
        {
            category: 'hair',
            subcategory: HairStyle.LOOSE,
            patterns: ['loose', 'down', 'flowing', 'free'],
            confidence: 0.7,
            priority: 1,
            tags: ['loose', 'hair', 'style']
        }
    ];

    /**
     * Hair texture patterns (migrated from HairStyleClassifier)
     */
    public static readonly HAIR_TEXTURE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'hair',
            subcategory: HairTexture.STRAIGHT,
            patterns: ['straight', 'sleek', 'smooth', 'pin_straight'],
            confidence: 0.8,
            priority: 3,
            tags: ['straight', 'hair', 'texture']
        },
        {
            category: 'hair',
            subcategory: HairTexture.WAVY,
            patterns: ['wavy', 'wave', 'beach', 'tousled', 'loose_wave'],
            confidence: 0.8,
            priority: 3,
            tags: ['wavy', 'hair', 'texture']
        },
        {
            category: 'hair',
            subcategory: HairTexture.CURLY,
            patterns: ['curly', 'curl', 'ringlet', 'spiral', 'bouncy'],
            confidence: 0.8,
            priority: 3,
            tags: ['curly', 'hair', 'texture']
        },
        {
            category: 'hair',
            subcategory: HairTexture.COILY,
            patterns: ['coily', 'kinky', 'afro', 'natural', 'textured'],
            confidence: 0.8,
            priority: 3,
            tags: ['coily', 'hair', 'texture']
        }
    ];

    /**
     * Hair accessory patterns
     */
    public static readonly HAIR_ACCESSORY_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'hair',
            subcategory: 'hair_with_accessories',
            patterns: ['bow', 'ribbon', 'headband', 'clip', 'pin', 'flower', 'accessory', 'decorated', 'ornament', 'jewel'],
            confidence: 0.9,
            priority: 4,
            tags: ['accessories', 'hair', 'decorated']
        }
    ];

    /**
     * Clothing style patterns
     */
    public static readonly CLOTHING_STYLE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'clothing',
            subcategory: 'formal',
            patterns: ['formal', 'suit', 'tuxedo', 'gown', 'dress_shirt', 'blazer', 'elegant', 'wedding', 'business'],
            confidence: 0.9,
            priority: 1,
            tags: ['formal', 'clothing', 'style']
        },
        {
            category: 'clothing',
            subcategory: 'casual',
            patterns: ['casual', 't_shirt', 'jeans', 'hoodie', 'sweater', 'everyday', 'relaxed', 'comfortable'],
            confidence: 0.8,
            priority: 1,
            tags: ['casual', 'clothing', 'style']
        },
        {
            category: 'clothing',
            subcategory: 'athletic',
            patterns: ['athletic', 'sport', 'gym', 'workout', 'fitness', 'running', 'yoga', 'activewear'],
            confidence: 0.9,
            priority: 1,
            tags: ['athletic', 'clothing', 'style']
        },
        {
            category: 'clothing',
            subcategory: 'party',
            patterns: ['party', 'club', 'night_out', 'cocktail', 'festive', 'celebration'],
            confidence: 0.9,
            priority: 1,
            tags: ['party', 'clothing', 'style']
        },
        {
            category: 'clothing',
            subcategory: 'swimwear',
            patterns: ['swimwear', 'bikini', 'swimsuit', 'bathing_suit', 'beach', 'pool'],
            confidence: 0.95,
            priority: 1,
            tags: ['swimwear', 'clothing', 'style']
        }
    ];

    /**
     * Clothing garment type patterns
     */
    public static readonly CLOTHING_GARMENT_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'clothing',
            subcategory: 'dress',
            patterns: ['dress', 'gown', 'frock', 'sundress', 'maxi_dress', 'mini_dress'],
            confidence: 0.95,
            priority: 2,
            tags: ['dress', 'clothing', 'garment']
        },
        {
            category: 'clothing',
            subcategory: 'top',
            patterns: ['top', 'shirt', 'blouse', 't_shirt', 'tank_top', 'camisole', 'sweater', 'hoodie'],
            confidence: 0.9,
            priority: 2,
            tags: ['top', 'clothing', 'garment']
        },
        {
            category: 'clothing',
            subcategory: 'bottom',
            patterns: ['bottom', 'pants', 'jeans', 'skirt', 'shorts', 'leggings', 'trousers'],
            confidence: 0.9,
            priority: 2,
            tags: ['bottom', 'clothing', 'garment']
        },
        {
            category: 'clothing',
            subcategory: 'outerwear',
            patterns: ['jacket', 'coat', 'blazer', 'cardigan', 'vest', 'outerwear'],
            confidence: 0.9,
            priority: 2,
            tags: ['outerwear', 'clothing', 'garment']
        },
        {
            category: 'clothing',
            subcategory: 'shoes',
            patterns: ['shoes', 'boots', 'sneakers', 'heels', 'sandals', 'flats', 'footwear'],
            confidence: 0.95,
            priority: 2,
            tags: ['shoes', 'clothing', 'garment']
        }
    ];

    /**
     * Makeup type patterns
     */
    public static readonly MAKEUP_TYPE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'makeup',
            subcategory: 'lipstick',
            patterns: ['lipstick', 'lip_color', 'lip_gloss', 'lip_balm', 'lips'],
            confidence: 0.95,
            priority: 1,
            tags: ['lipstick', 'makeup', 'lips']
        },
        {
            category: 'makeup',
            subcategory: 'eyeshadow',
            patterns: ['eyeshadow', 'eye_shadow', 'eye_makeup', 'eyes'],
            confidence: 0.95,
            priority: 1,
            tags: ['eyeshadow', 'makeup', 'eyes']
        },
        {
            category: 'makeup',
            subcategory: 'eyeliner',
            patterns: ['eyeliner', 'eye_liner', 'liner'],
            confidence: 0.95,
            priority: 1,
            tags: ['eyeliner', 'makeup', 'eyes']
        },
        {
            category: 'makeup',
            subcategory: 'mascara',
            patterns: ['mascara', 'lashes', 'eyelashes'],
            confidence: 0.95,
            priority: 1,
            tags: ['mascara', 'makeup', 'eyes']
        },
        {
            category: 'makeup',
            subcategory: 'blush',
            patterns: ['blush', 'rouge', 'cheek_color'],
            confidence: 0.95,
            priority: 1,
            tags: ['blush', 'makeup', 'cheeks']
        },
        {
            category: 'makeup',
            subcategory: 'foundation',
            patterns: ['foundation', 'base', 'concealer', 'coverage'],
            confidence: 0.9,
            priority: 1,
            tags: ['foundation', 'makeup', 'base']
        }
    ];

    /**
     * Accessory type patterns
     */
    public static readonly ACCESSORY_TYPE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'accessories',
            subcategory: 'jewelry',
            patterns: ['jewelry', 'necklace', 'earrings', 'bracelet', 'ring', 'pendant'],
            confidence: 0.95,
            priority: 1,
            tags: ['jewelry', 'accessories']
        },
        {
            category: 'accessories',
            subcategory: 'glasses',
            patterns: ['glasses', 'sunglasses', 'eyewear', 'spectacles'],
            confidence: 0.95,
            priority: 1,
            tags: ['glasses', 'accessories', 'eyewear']
        },
        {
            category: 'accessories',
            subcategory: 'hat',
            patterns: ['hat', 'cap', 'beanie', 'headwear', 'headband'],
            confidence: 0.95,
            priority: 1,
            tags: ['hat', 'accessories', 'headwear']
        },
        {
            category: 'accessories',
            subcategory: 'bag',
            patterns: ['bag', 'purse', 'handbag', 'backpack', 'clutch'],
            confidence: 0.95,
            priority: 1,
            tags: ['bag', 'accessories']
        },
        {
            category: 'accessories',
            subcategory: 'watch',
            patterns: ['watch', 'timepiece', 'wristwatch'],
            confidence: 0.95,
            priority: 1,
            tags: ['watch', 'accessories']
        }
    ];

    /**
     * Skin detail patterns
     */
    public static readonly SKIN_DETAIL_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'skin_details',
            subcategory: 'tattoo',
            patterns: ['tattoo', 'ink', 'body_art', 'tribal'],
            confidence: 0.95,
            priority: 1,
            tags: ['tattoo', 'skin_details', 'body_art']
        },
        {
            category: 'skin_details',
            subcategory: 'scar',
            patterns: ['scar', 'wound', 'mark', 'blemish'],
            confidence: 0.9,
            priority: 1,
            tags: ['scar', 'skin_details']
        },
        {
            category: 'skin_details',
            subcategory: 'freckles',
            patterns: ['freckles', 'spots', 'dots'],
            confidence: 0.9,
            priority: 1,
            tags: ['freckles', 'skin_details']
        },
        {
            category: 'skin_details',
            subcategory: 'overlay',
            patterns: ['overlay', 'skin_overlay', 'detail'],
            confidence: 0.8,
            priority: 1,
            tags: ['overlay', 'skin_details']
        }
    ];

    /**
     * Gets all hair patterns (for backward compatibility)
     */
    public static getAllHairPatterns(): SubcategoryPattern[] {
        return [
            ...this.HAIR_LENGTH_PATTERNS,
            ...this.HAIR_STYLE_PATTERNS,
            ...this.HAIR_TEXTURE_PATTERNS,
            ...this.HAIR_ACCESSORY_PATTERNS
        ];
    }

    /**
     * Furniture type patterns
     */
    public static readonly FURNITURE_TYPE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'furniture',
            subcategory: 'seating',
            patterns: ['chair', 'sofa', 'couch', 'armchair', 'stool', 'bench', 'loveseat'],
            confidence: 0.95,
            priority: 1,
            tags: ['seating', 'furniture']
        },
        {
            category: 'furniture',
            subcategory: 'table',
            patterns: ['table', 'desk', 'dining_table', 'coffee_table', 'end_table', 'nightstand'],
            confidence: 0.95,
            priority: 1,
            tags: ['table', 'furniture']
        },
        {
            category: 'furniture',
            subcategory: 'bed',
            patterns: ['bed', 'mattress', 'bedframe', 'bunk_bed', 'crib'],
            confidence: 0.95,
            priority: 1,
            tags: ['bed', 'furniture', 'bedroom']
        },
        {
            category: 'furniture',
            subcategory: 'storage',
            patterns: ['dresser', 'wardrobe', 'bookshelf', 'cabinet', 'chest', 'storage'],
            confidence: 0.9,
            priority: 1,
            tags: ['storage', 'furniture']
        },
        {
            category: 'furniture',
            subcategory: 'entertainment',
            patterns: ['tv_stand', 'entertainment_center', 'stereo', 'gaming_chair'],
            confidence: 0.9,
            priority: 1,
            tags: ['entertainment', 'furniture']
        }
    ];

    /**
     * Furniture style patterns
     */
    public static readonly FURNITURE_STYLE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'furniture',
            subcategory: 'modern',
            patterns: ['modern', 'contemporary', 'minimalist', 'sleek'],
            confidence: 0.8,
            priority: 2,
            tags: ['modern', 'furniture', 'style']
        },
        {
            category: 'furniture',
            subcategory: 'vintage',
            patterns: ['vintage', 'antique', 'retro', 'classic', 'traditional'],
            confidence: 0.8,
            priority: 2,
            tags: ['vintage', 'furniture', 'style']
        },
        {
            category: 'furniture',
            subcategory: 'rustic',
            patterns: ['rustic', 'farmhouse', 'country', 'wood', 'wooden'],
            confidence: 0.8,
            priority: 2,
            tags: ['rustic', 'furniture', 'style']
        }
    ];

    /**
     * Decoration type patterns
     */
    public static readonly DECORATION_TYPE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'decorations',
            subcategory: 'art',
            patterns: ['painting', 'artwork', 'picture', 'poster', 'canvas', 'sculpture'],
            confidence: 0.95,
            priority: 1,
            tags: ['art', 'decorations', 'wall']
        },
        {
            category: 'decorations',
            subcategory: 'plants',
            patterns: ['plant', 'flower', 'tree', 'garden', 'planter', 'vase'],
            confidence: 0.9,
            priority: 1,
            tags: ['plants', 'decorations', 'nature']
        },
        {
            category: 'decorations',
            subcategory: 'lighting',
            patterns: ['lamp', 'light', 'chandelier', 'sconce', 'candle', 'lantern'],
            confidence: 0.95,
            priority: 1,
            tags: ['lighting', 'decorations']
        },
        {
            category: 'decorations',
            subcategory: 'ornaments',
            patterns: ['ornament', 'decoration', 'figurine', 'statue', 'collectible'],
            confidence: 0.8,
            priority: 1,
            tags: ['ornaments', 'decorations']
        }
    ];

    /**
     * Appliance type patterns
     */
    public static readonly APPLIANCE_TYPE_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'appliances',
            subcategory: 'kitchen',
            patterns: ['stove', 'oven', 'refrigerator', 'microwave', 'dishwasher', 'blender', 'toaster'],
            confidence: 0.95,
            priority: 1,
            tags: ['kitchen', 'appliances']
        },
        {
            category: 'appliances',
            subcategory: 'bathroom',
            patterns: ['toilet', 'sink', 'shower', 'bathtub', 'mirror'],
            confidence: 0.95,
            priority: 1,
            tags: ['bathroom', 'appliances']
        },
        {
            category: 'appliances',
            subcategory: 'electronics',
            patterns: ['tv', 'television', 'computer', 'stereo', 'phone', 'tablet'],
            confidence: 0.9,
            priority: 1,
            tags: ['electronics', 'appliances']
        },
        {
            category: 'appliances',
            subcategory: 'laundry',
            patterns: ['washer', 'dryer', 'washing_machine', 'laundry'],
            confidence: 0.95,
            priority: 1,
            tags: ['laundry', 'appliances']
        }
    ];

    /**
     * Build item patterns
     */
    public static readonly BUILD_ITEM_PATTERNS: SubcategoryPattern[] = [
        {
            category: 'build_items',
            subcategory: 'doors',
            patterns: ['door', 'entrance', 'gate', 'doorway'],
            confidence: 0.95,
            priority: 1,
            tags: ['doors', 'build_items', 'architectural']
        },
        {
            category: 'build_items',
            subcategory: 'windows',
            patterns: ['window', 'glass', 'pane'],
            confidence: 0.9,
            priority: 1,
            tags: ['windows', 'build_items', 'architectural']
        },
        {
            category: 'build_items',
            subcategory: 'stairs',
            patterns: ['stairs', 'staircase', 'steps', 'ladder'],
            confidence: 0.95,
            priority: 1,
            tags: ['stairs', 'build_items', 'architectural']
        },
        {
            category: 'build_items',
            subcategory: 'walls',
            patterns: ['wall', 'wallpaper', 'panel', 'siding'],
            confidence: 0.8,
            priority: 1,
            tags: ['walls', 'build_items', 'architectural']
        },
        {
            category: 'build_items',
            subcategory: 'flooring',
            patterns: ['floor', 'flooring', 'tile', 'carpet', 'wood_floor'],
            confidence: 0.9,
            priority: 1,
            tags: ['flooring', 'build_items', 'architectural']
        }
    ];

    /**
     * Gets all object patterns
     */
    public static getAllObjectPatterns(): SubcategoryPattern[] {
        return [
            ...this.FURNITURE_TYPE_PATTERNS,
            ...this.FURNITURE_STYLE_PATTERNS,
            ...this.DECORATION_TYPE_PATTERNS,
            ...this.APPLIANCE_TYPE_PATTERNS,
            ...this.BUILD_ITEM_PATTERNS
        ];
    }

    /**
     * Gets all CAS patterns (comprehensive)
     */
    public static getAllCASPatterns(): SubcategoryPattern[] {
        return [
            ...this.HAIR_LENGTH_PATTERNS,
            ...this.HAIR_STYLE_PATTERNS,
            ...this.HAIR_TEXTURE_PATTERNS,
            ...this.HAIR_ACCESSORY_PATTERNS,
            ...this.CLOTHING_STYLE_PATTERNS,
            ...this.CLOTHING_GARMENT_PATTERNS,
            ...this.MAKEUP_TYPE_PATTERNS,
            ...this.ACCESSORY_TYPE_PATTERNS,
            ...this.SKIN_DETAIL_PATTERNS
        ];
    }

    /**
     * Gets all patterns (CAS + Objects) - Universal coverage
     */
    public static getAllPatterns(): SubcategoryPattern[] {
        return [
            ...this.getAllCASPatterns(),
            ...this.getAllObjectPatterns()
        ];
    }

    /**
     * Gets patterns by category
     */
    public static getPatternsByCategory(category: string): SubcategoryPattern[] {
        const allPatterns = this.getAllPatterns();
        return allPatterns.filter(pattern => pattern.category === category);
    }

    /**
     * Gets patterns by priority
     */
    public static getPatternsByPriority(priority: number): SubcategoryPattern[] {
        const allPatterns = this.getAllPatterns();
        return allPatterns.filter(pattern => pattern.priority === priority);
    }

    /**
     * Searches for patterns matching a specific subcategory
     */
    public static findPatternsBySubcategory(subcategory: string): SubcategoryPattern[] {
        const allPatterns = this.getAllPatterns();
        return allPatterns.filter(pattern => pattern.subcategory === subcategory);
    }

    /**
     * Gets all available categories
     */
    public static getAllCategories(): string[] {
        const allPatterns = this.getAllPatterns();
        const categories = new Set(allPatterns.map(p => p.category));
        return Array.from(categories);
    }

    /**
     * Gets subcategories for a specific category
     */
    public static getSubcategoriesForCategory(category: string): string[] {
        const patterns = this.getPatternsByCategory(category);
        const subcategories = new Set(patterns.map(p => p.subcategory));
        return Array.from(subcategories);
    }

    /**
     * Checks if a category is a CAS category
     */
    public static isCASCategory(category: string): boolean {
        const casCategories = ['hair', 'clothing', 'makeup', 'accessories', 'skin_details'];
        return casCategories.includes(category);
    }

    /**
     * Checks if a category is an object category
     */
    public static isObjectCategory(category: string): boolean {
        const objectCategories = ['furniture', 'decorations', 'appliances', 'build_items'];
        return objectCategories.includes(category);
    }
}
