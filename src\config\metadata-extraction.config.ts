/**
 * Configuration for metadata extraction system
 * Centralized configuration to avoid hardcoded values
 */

export interface FilenamePattern {
    readonly name: string;
    readonly pattern: RegExp;
    readonly groups: {
        readonly author?: number;
        readonly modName?: number;
        readonly version?: number;
        readonly description?: number;
    };
    readonly confidence: number;
    readonly priority: number;
}

export interface MetadataExtractionConfig {
    readonly filename: {
        readonly patterns: readonly FilenamePattern[];
        readonly maxLength: number;
        readonly minAuthorLength: number;
        readonly minModNameLength: number;
        readonly versionFormats: readonly RegExp[];
    };
    
    readonly stringTable: {
        readonly supportedVersions: readonly number[];
        readonly maxStringLength: number;
        readonly encoding: string;
        readonly keyPatterns: {
            readonly modName: readonly RegExp[];
            readonly description: readonly RegExp[];
            readonly author: readonly RegExp[];
        };
    };
    
    readonly tuning: {
        readonly commentPatterns: readonly RegExp[];
        readonly creatorPatterns: readonly RegExp[];
        readonly maxCommentLength: number;
    };
    
    readonly simData: {
        readonly metadataKeys: readonly string[];
        readonly maxValueLength: number;
    };
    
    readonly confidence: {
        readonly weights: {
            readonly manifest: number;
            readonly simdata: number;
            readonly tuningComment: number;
            readonly filename: number;
            readonly stringTable: number;
        };
        readonly minimumConfidence: number;
        readonly conflictThreshold: number;
    };
    
    readonly security: {
        readonly maxProcessingTime: number;
        readonly maxMemoryUsage: number;
        readonly allowedFileExtensions: readonly string[];
        readonly sanitizationRules: {
            readonly removeHtml: boolean;
            readonly removeScripts: boolean;
            readonly maxLength: number;
        };
    };
}

/**
 * Default configuration for metadata extraction
 */
export const DEFAULT_METADATA_CONFIG: MetadataExtractionConfig = {
    filename: {
        patterns: [
            {
                name: 'author_modname_version_desc',
                pattern: /^([^_\-\s]+)[-_]([^_\-\s]+?)[-_]v?(\d+(?:\.\d+)*(?:\.\d+)?)[-_](.+?)\.package$/i,
                groups: { author: 1, modName: 2, version: 3, description: 4 },
                confidence: 90,
                priority: 1
            },
            {
                name: 'author_modname_version',
                pattern: /^([^_\-\s]+)[-_]([^_\-\s]+?)[-_]v?(\d+(?:\.\d+)*(?:\.\d+)?)\.package$/i,
                groups: { author: 1, modName: 2, version: 3 },
                confidence: 85,
                priority: 2
            },
            {
                name: 'author_modname_desc_noversion',
                pattern: /^([^_\-\s]+)[-_]([^_\-\s]+?)[-_]([^_\-\s]+?)\.package$/i,
                groups: { author: 1, modName: 2, description: 3 },
                confidence: 75,
                priority: 3
            },
            {
                name: 'bracketed_author_modname_version',
                pattern: /^\[([^\]]+)\][-_\s]*([^_\-\s]+?)[-_\s]*v?(\d+(?:\.\d+)*(?:\.\d+)?)(?:[-_\s](.+?))?\.package$/i,
                groups: { author: 1, modName: 2, version: 3, description: 4 },
                confidence: 90,
                priority: 4
            },
            {
                name: 'spaced_author_modname_version',
                pattern: /^([^-]+?)\s*-\s*([^-]+?)\s*-?\s*v?(\d+(?:\.\d+)*(?:\.\d+)?)(?:\s*-\s*(.+?))?\.package$/i,
                groups: { author: 1, modName: 2, version: 3, description: 4 },
                confidence: 80,
                priority: 5
            },
            {
                name: 'modname_by_author_version',
                pattern: /^([^_\-\s]+?)[-_\s]*(?:by|from)[-_\s]*([^_\-\s]+?)[-_\s]*v?(\d+(?:\.\d+)*(?:\.\d+)?)(?:[-_\s](.+?))?\.package$/i,
                groups: { modName: 1, author: 2, version: 3, description: 4 },
                confidence: 75,
                priority: 6
            },
            {
                name: 'simple_modname_version',
                pattern: /^([^_\-\s]+?)[-_\s]*v(\d+(?:\.\d+)*(?:\.\d+)?)(?:[-_\s](.+?))?\.package$/i,
                groups: { modName: 1, version: 2, description: 3 },
                confidence: 75,
                priority: 7
            },
            {
                name: 'simple_author_modname',
                pattern: /^([^_\-\s]+)[-_]([^_\-\s]+?)\.package$/i,
                groups: { author: 1, modName: 2 },
                confidence: 70,
                priority: 8
            }
        ] as const,
        maxLength: 255,
        minAuthorLength: 2,
        minModNameLength: 3,
        versionFormats: [
            /^\d+\.\d+\.\d+$/,  // 1.2.3
            /^\d+\.\d+$/,       // 1.2
            /^\d+$/             // 1
        ] as const
    },
    
    stringTable: {
        supportedVersions: [5, 4, 3] as const,
        maxStringLength: 1000,
        encoding: 'utf-8',
        keyPatterns: {
            modName: [
                /^MOD_NAME$/i,
                /^TITLE$/i,
                /^DISPLAY_NAME$/i,
                /^PACKAGE_NAME$/i,
                /^NAME$/i,
                /.*_TITLE$/i,
                /.*_NAME$/i,
                /^MODNAME$/i,
                /^MOD$/i
            ] as const,
            description: [
                /^DESCRIPTION$/i,
                /^MOD_DESCRIPTION$/i,
                /^PACKAGE_DESCRIPTION$/i,
                /^INFO$/i,
                /^ABOUT$/i,
                /.*_DESCRIPTION$/i,
                /.*_DESC$/i,
                /.*_INFO$/i,
                /^DESC$/i,
                /^TOOLTIP$/i,
                /^HELP$/i
            ] as const,
            author: [
                /^AUTHOR$/i,
                /^CREATOR$/i,
                /^MADE_BY$/i,
                /^CREATED_BY$/i,
                /.*_AUTHOR$/i,
                /.*_CREATOR$/i
            ] as const
        }
    },
    
    tuning: {
        commentPatterns: [
            /<!--\s*(.+?)\s*-->/g,
            /\/\*\s*(.+?)\s*\*\//g,
            /\/\/\s*(.+?)$/gm
        ] as const,
        creatorPatterns: [
            /(?:created?|made|author|by)[\s:]+([^,\n\r]+)/gi,
            /creator[\s:]+([^,\n\r]+)/gi,
            /mod[\s:]+([^,\n\r]+)/gi
        ] as const,
        maxCommentLength: 500
    },
    
    simData: {
        metadataKeys: [
            'mod_name',
            'creator',
            'author',
            'version',
            'description',
            'title',
            'name'
        ] as const,
        maxValueLength: 200
    },
    
    confidence: {
        weights: {
            manifest: 100,
            simdata: 80,
            tuningComment: 70,
            filename: 60,
            stringTable: 40
        },
        minimumConfidence: 30,
        conflictThreshold: 20
    },
    
    security: {
        maxProcessingTime: 5000, // 5 seconds
        maxMemoryUsage: 50 * 1024 * 1024, // 50MB
        allowedFileExtensions: ['.package', '.ts4script'] as const,
        sanitizationRules: {
            removeHtml: true,
            removeScripts: true,
            maxLength: 1000
        }
    }
} as const;

/**
 * Type guard to validate configuration
 */
export function isValidMetadataConfig(config: any): config is MetadataExtractionConfig {
    return (
        config &&
        typeof config === 'object' &&
        config.filename &&
        Array.isArray(config.filename.patterns) &&
        config.stringTable &&
        Array.isArray(config.stringTable.supportedVersions) &&
        config.confidence &&
        typeof config.confidence.weights === 'object' &&
        config.security &&
        typeof config.security.maxProcessingTime === 'number'
    );
}
