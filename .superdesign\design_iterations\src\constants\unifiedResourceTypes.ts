import { BinaryResourceType } from '@s4tk/models/enums';

/**
 * A custom set of resource types that are not included in the S4TK library.
 * These are often for more specific, community-defined, or newly discovered types.
 */
const CustomResourceTypes = {
    SimData: 0x545AC67A,
    SimDataXml: 0x545AC67B, // Example of a custom variant
    CombinedTuning: 0xE6BBD73D,
    HotspotControl: 0x73E23234,
    ClientHotspot: 0x2BC2A58D,
    Tuning: 0x03B33D45,
    Generic: 0xDEADBEEF, // Placeholder for generic/unknown but parsable data
    Trait: 0xCB5FDDC7,
    CASPart: 0x034AEECB,
    Animation: 0x02D5DF13,
    Snapshot: 0x7DB48362,
    LotTrait: 0x00000000, // Example, might need a real value
    ClubSeed: 0x245223B4,
    // Add other custom types as needed
};

/**
 * A unified and immutable mapping of all resource types, combining the official S4TK
 * `BinaryResourceType` with the `CustomResourceTypes`.
 *
 * This object serves as the single source of truth for resource type identification
 * throughout the application, preventing the ID conflicts and inconsistencies that
 * plagued the previous version.
 *
 * The `BinaryResourceType` from S4TK is treated as the base, and custom types are
 * merged in. If a key collision occurs, the S4TK value is preserved to maintain
 * compatibility with the core library.
 */
const buildUnifiedResourceTypes = () => {
    const unified: { [key: string]: number } = {};

    // Add all S4TK types first
    for (const key in BinaryResourceType) {
        if (isNaN(parseInt(key, 10))) {
            const value = BinaryResourceType[key as keyof typeof BinaryResourceType];
            if (typeof value === 'number') {
                unified[key] = value;
            }
        }
    }

    // Merge custom types, overwriting only if the key doesn't already exist
    for (const key in CustomResourceTypes) {
        if (!unified.hasOwnProperty(key)) {
            unified[key] = CustomResourceTypes[key as keyof typeof CustomResourceTypes];
        }
    }

    return unified;
};

export const URT = Object.freeze(buildUnifiedResourceTypes());

/**
 * Resource type groups for efficient filtering and analysis
 */
export const RESOURCE_TYPE_GROUPS = {
    // Essential types for quick categorization
    ESSENTIAL_FOR_QUICK_ANALYSIS: [
        URT.SimData,
        URT.CasPart,
        URT.ObjectDefinition,
        URT.CombinedTuning,
        URT.ObjectCatalog,
        URT.StringTable,
    ],

    // CAS-related resources
    CAS_RESOURCES: [
        URT.CasPart,
        URT.CasPreset,
        URT.CasPartThumbnail,
    ],

    // Build/Buy related resources
    BUILD_BUY_RESOURCES: [
        URT.ObjectDefinition,
        URT.ObjectCatalog,
        URT.ObjectCatalogSet,
        URT.Footprint,
        URT.Slot,
    ],

    // Visual/texture resources
    TEXTURE_RESOURCES: [
        URT.DdsImage,
        URT.DstImage,
        URT.PngImage,
        URT.Rle2Image,
        URT.RlesImage,
    ],

    // 3D model resources
    MODEL_RESOURCES: [
        URT.Model,
        URT.ModelLod,
        URT.Rig,
    ],

    // Tuning and gameplay resources
    TUNING_RESOURCES: [
        URT.SimData,
        URT.CombinedTuning,
        URT.Tuning,
    ],

    // Resources that commonly indicate overrides
    OVERRIDE_PRONE_RESOURCES: [
        URT.SimData,        // Most common conflicts
        URT.CombinedTuning, // Gameplay conflicts
        URT.Tuning,         // Custom tuning conflicts
    ],

    // Resources needed for conflict detection
    CONFLICT_DETECTION_RESOURCES: [
        URT.SimData,
        URT.CombinedTuning,
        URT.ObjectDefinition,
        URT.CasPart,
        URT.Tuning,
    ],
} as const;

/**
 * Common resource group patterns for override detection
 */
export const OVERRIDE_GROUPS = {
    COMMON_OVERRIDE_GROUP: 0x80000000,
    BASEGAME_GROUP: 0x00000000,
} as const;

/**
 * Helper functions for resource type checking
 */
export const ResourceTypeHelpers = {
    /**
     * Check if a resource type is a CAS resource
     */
    isCASResource: (type: number): boolean => {
        return RESOURCE_TYPE_GROUPS.CAS_RESOURCES.includes(type);
    },

    /**
     * Check if a resource type is a Build/Buy resource
     */
    isBuildBuyResource: (type: number): boolean => {
        return RESOURCE_TYPE_GROUPS.BUILD_BUY_RESOURCES.includes(type);
    },

    /**
     * Check if a resource type is a texture resource
     */
    isTextureResource: (type: number): boolean => {
        return RESOURCE_TYPE_GROUPS.TEXTURE_RESOURCES.includes(type);
    },

    /**
     * Check if a resource type is commonly involved in overrides
     */
    isOverrideProne: (type: number): boolean => {
        return RESOURCE_TYPE_GROUPS.OVERRIDE_PRONE_RESOURCES.includes(type);
    },

    /**
     * Check if a resource type is essential for quick analysis
     */
    isEssentialForQuickAnalysis: (type: number): boolean => {
        return RESOURCE_TYPE_GROUPS.ESSENTIAL_FOR_QUICK_ANALYSIS.includes(type);
    },

    /**
     * Check if a resource type is a SimData resource
     */
    isSimDataResource: (type: number): boolean => {
        return type === URT.SimData;
    },

    /**
     * Check if a resource type is a tuning resource
     */
    isTuningResource: (type: number): boolean => {
        return RESOURCE_TYPE_GROUPS.TUNING_RESOURCES.includes(type);
    },

    /**
     * Check if a resource type is a script resource
     */
    isScriptResource: (type: number): boolean => {
        // Script resources are typically handled at file level, not resource level
        // This is a placeholder for completeness
        return false;
    },

    /**
     * Check if a resource type is a string table resource
     */
    isStringTableResource: (type: number): boolean => {
        return type === URT.StringTable;
    },

    /**
     * Check if a resource type is an object resource
     */
    isObjectResource: (type: number): boolean => {
        return type === URT.ObjectDefinition ||
               type === URT.ObjectCatalog ||
               type === URT.ObjectCatalogSet;
    },

    /**
     * Get human-readable name for resource type
     */
    getTypeName: (type: number): string => {
        // Find the key in URT that matches this value
        const entry = Object.entries(URT).find(([, value]) => value === type);
        if (entry) {
            return entry[0].replace(/([A-Z])/g, ' $1').trim()
                .replace(/\b\w/g, l => l.toUpperCase());
        }
        return `Unknown (0x${type.toString(16).toUpperCase().padStart(8, '0')})`;
    },
} as const;