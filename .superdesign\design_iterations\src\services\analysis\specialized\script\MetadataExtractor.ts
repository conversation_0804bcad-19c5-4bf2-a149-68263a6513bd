/**
 * Script Metadata Extractor
 * 
 * Specialized component for extracting metadata (author, version, etc.) 
 * from script file content and ZIP archives.
 * 
 * Extracted from ScriptAnalyzer.ts as part of Phase 2 refactoring.
 */

import { METADATA_PATTERNS, type ScriptModInfo } from './types';

/**
 * Handles extraction of metadata from script content
 */
export class MetadataExtractor {
    
    /**
     * Extracts metadata from ZIP file content
     * 
     * @param content - ZIP content as string
     * @param scriptInfo - Script info object to populate
     */
    public static extractFromZipContent(content: string, scriptInfo: ScriptModInfo): void {
        try {
            // Extract author information
            this.extractAuthor(content, scriptInfo);
            
            // Extract version information
            this.extractVersion(content, scriptInfo);
            
        } catch (error) {
            console.warn('Error extracting metadata from ZIP content:', error);
        }
    }
    
    /**
     * Extracts metadata from Python file content
     * 
     * @param content - Python file content as string
     * @param scriptInfo - Script info object to populate
     */
    public static extractFromPythonContent(content: string, scriptInfo: ScriptModInfo): void {
        try {
            // Extract author information (only if not already set)
            if (!scriptInfo.author) {
                this.extractAuthor(content, scriptInfo);
            }
            
            // Extract version information (only if not already set)
            if (!scriptInfo.version) {
                this.extractVersion(content, scriptInfo);
            }
            
        } catch (error) {
            console.warn('Error extracting metadata from Python content:', error);
        }
    }
    
    /**
     * Extracts author information from content
     * 
     * @param content - Content to search
     * @param scriptInfo - Script info object to populate
     */
    private static extractAuthor(content: string, scriptInfo: ScriptModInfo): void {
        for (const pattern of METADATA_PATTERNS.AUTHOR) {
            const match = content.match(pattern);
            if (match && match[1]) {
                scriptInfo.author = this.cleanMetadataValue(match[1]);
                return;
            }
        }
    }
    
    /**
     * Extracts version information from content
     * 
     * @param content - Content to search
     * @param scriptInfo - Script info object to populate
     */
    private static extractVersion(content: string, scriptInfo: ScriptModInfo): void {
        for (const pattern of METADATA_PATTERNS.VERSION) {
            const match = content.match(pattern);
            if (match && match[1]) {
                scriptInfo.version = this.cleanMetadataValue(match[1]);
                return;
            }
        }
    }
    
    /**
     * Cleans and validates metadata values
     * 
     * @param value - Raw metadata value
     * @returns Cleaned metadata value
     */
    private static cleanMetadataValue(value: string): string {
        return value
            .trim()
            .replace(/['"]/g, '') // Remove quotes
            .replace(/\s+/g, ' ') // Normalize whitespace
            .substring(0, 100); // Limit length
    }
    
    /**
     * Extracts description or docstring from Python content
     * 
     * @param content - Python file content
     * @returns Extracted description or null
     */
    public static extractDescription(content: string): string | null {
        try {
            // Look for module docstring (triple quotes at the beginning)
            const docstringPatterns = [
                /^"""([\s\S]*?)"""/m,
                /^'''([\s\S]*?)'''/m,
                /^#\s*Description:\s*(.+?)$/m,
                /^#\s*(.+?)$/m
            ];
            
            for (const pattern of docstringPatterns) {
                const match = content.match(pattern);
                if (match && match[1]) {
                    const description = match[1].trim();
                    if (description.length > 10 && description.length < 500) {
                        return this.cleanDescription(description);
                    }
                }
            }
            
            return null;
            
        } catch (error) {
            console.warn('Error extracting description:', error);
            return null;
        }
    }
    
    /**
     * Cleans description text
     * 
     * @param description - Raw description
     * @returns Cleaned description
     */
    private static cleanDescription(description: string): string {
        return description
            .replace(/\n\s*/g, ' ') // Replace newlines with spaces
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim()
            .substring(0, 200); // Limit length
    }
    
    /**
     * Extracts license information from content
     * 
     * @param content - Content to search
     * @returns License information or null
     */
    public static extractLicense(content: string): string | null {
        const licensePatterns = [
            /__license__\s*=\s*['"](.+?)['"]/,
            /license\s*=\s*['"](.+?)['"]/,
            /# License:\s*(.+?)$/m,
            /MIT License/i,
            /GPL/i,
            /Apache/i,
            /BSD/i
        ];
        
        for (const pattern of licensePatterns) {
            const match = content.match(pattern);
            if (match) {
                return match[1] ? this.cleanMetadataValue(match[1]) : match[0];
            }
        }
        
        return null;
    }
    
    /**
     * Extracts contact information from content
     * 
     * @param content - Content to search
     * @returns Contact information or null
     */
    public static extractContact(content: string): string | null {
        const contactPatterns = [
            /__email__\s*=\s*['"](.+?)['"]/,
            /email\s*=\s*['"](.+?)['"]/,
            /# Contact:\s*(.+?)$/m,
            /# Email:\s*(.+?)$/m,
            /[\w\.-]+@[\w\.-]+\.\w+/
        ];
        
        for (const pattern of contactPatterns) {
            const match = content.match(pattern);
            if (match && match[1]) {
                return this.cleanMetadataValue(match[1]);
            }
        }
        
        return null;
    }
    
    /**
     * Validates metadata completeness
     * 
     * @param scriptInfo - Script info to validate
     * @returns Validation result with missing fields
     */
    public static validateMetadata(scriptInfo: ScriptModInfo): {
        isComplete: boolean;
        missingFields: string[];
        score: number;
    } {
        const requiredFields = ['author', 'version', 'description'];
        const missingFields: string[] = [];
        
        requiredFields.forEach(field => {
            if (!scriptInfo[field as keyof ScriptModInfo]) {
                missingFields.push(field);
            }
        });
        
        const score = ((requiredFields.length - missingFields.length) / requiredFields.length) * 100;
        
        return {
            isComplete: missingFields.length === 0,
            missingFields,
            score: Math.round(score)
        };
    }
}
