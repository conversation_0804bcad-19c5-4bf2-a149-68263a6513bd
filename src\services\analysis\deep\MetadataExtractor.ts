import type { ResourceEntry } from '@s4tk/models/types';
import { ResourceTypeHelpers } from '../../../constants/unifiedResourceTypes';
import type { MetadataAnalysisResult } from './types';

/**
 * Specialized extractor for metadata and complexity analysis
 * Extracts detailed information about mod content and structure
 */
export class MetadataExtractor {
    
    /**
     * Extracts comprehensive metadata from resources
     */
    public static analyze(resources: ResourceEntry[], filePath: string): MetadataAnalysisResult {
        const extractedMetadata = this.extractResourceMetadata(resources);
        const customContent = this.analyzeCustomContent(resources);
        const complexity = this.calculateComplexity(resources, filePath);
        
        return {
            extractedMetadata,
            customContent,
            complexity
        };
    }
    
    /**
     * Extracts detailed metadata from all resources
     */
    private static extractResourceMetadata(resources: ResourceEntry[]): Record<string, any> {
        const metadata: Record<string, any> = {
            resourceCount: resources.length,
            resourceTypes: this.getResourceTypeBreakdown(resources),
            compressionStats: this.getCompressionStats(resources),
            sizeStats: this.getSizeStats(resources),
            groupAnalysis: this.getGroupAnalysis(resources),
            instanceAnalysis: this.getInstanceAnalysis(resources)
        };
        
        return metadata;
    }
    
    /**
     * Gets breakdown of resource types
     */
    private static getResourceTypeBreakdown(resources: ResourceEntry[]): Record<string, number> {
        const breakdown: Record<string, number> = {};
        
        resources.forEach(resource => {
            const typeName = ResourceTypeHelpers.getTypeName(resource.key.type);
            breakdown[typeName] = (breakdown[typeName] || 0) + 1;
        });
        
        return breakdown;
    }
    
    /**
     * Gets compression statistics
     */
    private static getCompressionStats(resources: ResourceEntry[]): {
        compressed: number;
        uncompressed: number;
        compressionTypes: Record<string, number>;
    } {
        let compressed = 0;
        let uncompressed = 0;
        const compressionTypes: Record<string, number> = {};
        
        resources.forEach(resource => {
            if (resource.value?.compressionType !== undefined) {
                const compressionName = this.getCompressionTypeName(resource.value.compressionType);
                compressionTypes[compressionName] = (compressionTypes[compressionName] || 0) + 1;
                
                if (resource.value.compressionType === 0x0000) {
                    uncompressed++;
                } else {
                    compressed++;
                }
            }
        });
        
        return {
            compressed,
            uncompressed,
            compressionTypes
        };
    }
    
    /**
     * Gets size statistics
     */
    private static getSizeStats(resources: ResourceEntry[]): {
        totalSize: number;
        averageSize: number;
        largestResource: { size: number; type: string };
        smallestResource: { size: number; type: string };
    } {
        let totalSize = 0;
        let largestSize = 0;
        let smallestSize = Infinity;
        let largestType = '';
        let smallestType = '';
        
        resources.forEach(resource => {
            const size = resource.value?.buffer?.length || 0;
            totalSize += size;
            
            if (size > largestSize) {
                largestSize = size;
                largestType = ResourceTypeHelpers.getTypeName(resource.key.type);
            }
            
            if (size < smallestSize && size > 0) {
                smallestSize = size;
                smallestType = ResourceTypeHelpers.getTypeName(resource.key.type);
            }
        });
        
        return {
            totalSize,
            averageSize: resources.length > 0 ? totalSize / resources.length : 0,
            largestResource: { size: largestSize, type: largestType },
            smallestResource: { size: smallestSize === Infinity ? 0 : smallestSize, type: smallestType }
        };
    }
    
    /**
     * Analyzes group patterns
     */
    private static getGroupAnalysis(resources: ResourceEntry[]): {
        uniqueGroups: number;
        groupBreakdown: Record<string, number>;
        hasBaseGameGroups: boolean;
        hasCustomGroups: boolean;
    } {
        const groups = new Set<number>();
        const groupBreakdown: Record<string, number> = {};
        let hasBaseGameGroups = false;
        let hasCustomGroups = false;
        
        resources.forEach(resource => {
            const group = resource.key.group;
            groups.add(group);
            
            const groupHex = `0x${group.toString(16).toUpperCase().padStart(8, '0')}`;
            groupBreakdown[groupHex] = (groupBreakdown[groupHex] || 0) + 1;
            
            // Check for base game groups
            if (group === 0x80000000 || group === 0x00000000) {
                hasBaseGameGroups = true;
            } else {
                hasCustomGroups = true;
            }
        });
        
        return {
            uniqueGroups: groups.size,
            groupBreakdown,
            hasBaseGameGroups,
            hasCustomGroups
        };
    }
    
    /**
     * Analyzes instance patterns
     */
    private static getInstanceAnalysis(resources: ResourceEntry[]): {
        uniqueInstances: number;
        hasSequentialInstances: boolean;
        hasRandomInstances: boolean;
        instancePatterns: string[];
    } {
        const instances = new Set<string>();
        const instancePatterns: string[] = [];
        let hasSequentialInstances = false;
        let hasRandomInstances = false;
        
        const sortedInstances = resources
            .map(r => r.key.instance)
            .sort((a, b) => Number(a - b));
        
        sortedInstances.forEach((instance, index) => {
            const instanceHex = instance.toString(16).toUpperCase();
            instances.add(instanceHex);
            
            // Check for sequential patterns
            if (index > 0) {
                const prev = sortedInstances[index - 1];
                if (Number(instance - prev) === 1) {
                    hasSequentialInstances = true;
                }
            }
            
            // Check for random patterns (high entropy in hex)
            if (this.hasHighEntropy(instanceHex)) {
                hasRandomInstances = true;
            }
            
            // Identify common patterns
            if (instanceHex.startsWith('00000000')) {
                instancePatterns.push('Base Game Pattern');
            } else if (instanceHex.length === 16) {
                instancePatterns.push('Full Instance ID');
            }
        });
        
        return {
            uniqueInstances: instances.size,
            hasSequentialInstances,
            hasRandomInstances,
            instancePatterns: [...new Set(instancePatterns)]
        };
    }
    
    /**
     * Analyzes custom content characteristics
     */
    private static analyzeCustomContent(resources: ResourceEntry[]): {
        hasCustomStrings: boolean;
        hasCustomTuning: boolean;
        hasCustomObjects: boolean;
        hasCustomTextures: boolean;
    } {
        let hasCustomStrings = false;
        let hasCustomTuning = false;
        let hasCustomObjects = false;
        let hasCustomTextures = false;
        
        resources.forEach(resource => {
            const type = resource.key.type;
            
            if (ResourceTypeHelpers.isStringTableResource(type)) {
                hasCustomStrings = true;
            }
            
            if (ResourceTypeHelpers.isTuningResource(type)) {
                hasCustomTuning = true;
            }
            
            if (ResourceTypeHelpers.isObjectResource(type)) {
                hasCustomObjects = true;
            }
            
            if (ResourceTypeHelpers.isTextureResource(type)) {
                hasCustomTextures = true;
            }
        });
        
        return {
            hasCustomStrings,
            hasCustomTuning,
            hasCustomObjects,
            hasCustomTextures
        };
    }
    
    /**
     * Calculates complexity score and factors
     */
    private static calculateComplexity(resources: ResourceEntry[], filePath: string): {
        score: number;
        factors: string[];
    } {
        let score = 0;
        const factors: string[] = [];
        
        // Resource count factor
        if (resources.length > 100) {
            score += 3;
            factors.push(`High resource count (${resources.length})`);
        } else if (resources.length > 50) {
            score += 2;
            factors.push(`Medium resource count (${resources.length})`);
        } else if (resources.length > 10) {
            score += 1;
            factors.push(`Low resource count (${resources.length})`);
        }
        
        // Resource type diversity factor
        const uniqueTypes = new Set(resources.map(r => r.key.type)).size;
        if (uniqueTypes > 10) {
            score += 2;
            factors.push(`High type diversity (${uniqueTypes} types)`);
        } else if (uniqueTypes > 5) {
            score += 1;
            factors.push(`Medium type diversity (${uniqueTypes} types)`);
        }
        
        // Custom content complexity
        const customContent = this.analyzeCustomContent(resources);
        let customContentTypes = 0;
        if (customContent.hasCustomStrings) customContentTypes++;
        if (customContent.hasCustomTuning) customContentTypes++;
        if (customContent.hasCustomObjects) customContentTypes++;
        if (customContent.hasCustomTextures) customContentTypes++;
        
        if (customContentTypes >= 3) {
            score += 2;
            factors.push('Multiple custom content types');
        } else if (customContentTypes >= 2) {
            score += 1;
            factors.push('Mixed custom content');
        }
        
        // File size factor
        const totalSize = resources.reduce((sum, r) => sum + (r.value?.buffer?.length || 0), 0);
        if (totalSize > 50 * 1024 * 1024) { // > 50MB
            score += 2;
            factors.push('Large file size');
        } else if (totalSize > 10 * 1024 * 1024) { // > 10MB
            score += 1;
            factors.push('Medium file size');
        }
        
        return {
            score: Math.min(score, 10), // Cap at 10
            factors
        };
    }
    
    /**
     * Gets compression type name
     */
    private static getCompressionTypeName(compressionType: number): string {
        switch (compressionType) {
            case 0x0000: return 'Uncompressed';
            case 0xFFFE: return 'Streamable';
            case 0xFFFF: return 'Internal';
            case 0xFFE0: return 'Deleted';
            case 0x5A42: return 'ZLIB';
            default: return `Unknown (0x${compressionType.toString(16)})`;
        }
    }
    
    /**
     * Checks if a hex string has high entropy (randomness)
     */
    private static hasHighEntropy(hexString: string): boolean {
        const charCounts: Record<string, number> = {};
        
        for (const char of hexString) {
            charCounts[char] = (charCounts[char] || 0) + 1;
        }
        
        // Calculate entropy
        let entropy = 0;
        const length = hexString.length;
        
        Object.values(charCounts).forEach(count => {
            const probability = count / length;
            entropy -= probability * Math.log2(probability);
        });
        
        // High entropy threshold for hex strings (close to 4 bits per character)
        return entropy > 3.5;
    }
}