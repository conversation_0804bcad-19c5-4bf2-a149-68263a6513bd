import { ModCategory } from '../../../types/analysis';

/**
 * Types for deep analysis components
 */

export interface DeepAnalysisResult {
    primaryCategory: ModCategory;
    subcategory: string;
    suggestedPath?: string;
    confidence: number;
    categoryAnalysis: CategoryAnalysisResult;
    dependencyAnalysis: DependencyAnalysisResult;
    overrideAnalysis: OverrideAnalysisResult;
    metadataAnalysis: MetadataAnalysisResult;
}

export interface CategoryAnalysisResult {
    detectedCategories: Array<{
        category: ModCategory;
        confidence: number;
        evidence: string[];
    }>;
    primaryCategory: ModCategory;
    subcategory: string;
    suggestedPath?: string;
}

export interface DependencyAnalysisResult {
    dependencies: Array<{
        type: 'pack' | 'expansion' | 'mod';
        name: string;
        required: boolean;
        confidence: number;
    }>;
    potentialConflicts: Array<{
        type: string;
        description: string;
        severity: 'low' | 'medium' | 'high';
    }>;
}

export interface OverrideAnalysisResult {
    isOverride: boolean;
    overriddenResources: Array<{
        type: number;
        group: number;
        instance: string;
        description: string;
    }>;
    overrideType: 'replacement' | 'modification' | 'addition' | 'unknown';
    confidence: number;
}

export interface MetadataAnalysisResult {
    extractedMetadata: Record<string, any>;
    customContent: {
        hasCustomStrings: boolean;
        hasCustomTuning: boolean;
        hasCustomObjects: boolean;
        hasCustomTextures: boolean;
    };
    complexity: {
        score: number;
        factors: string[];
    };
}