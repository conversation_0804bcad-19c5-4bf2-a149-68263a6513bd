/**
 * Object Analysis Types and Enums
 * 
 * This file contains all type definitions, interfaces, and enums
 * related to Object analysis and categorization for furniture, decorations, and build items.
 * 
 * Following the same patterns as CAS analysis types.
 */

/**
 * Detailed Object information extracted from actual resource data
 * Following CAS patterns with Reddit-requested features for conflict detection and visual management
 */
export interface ObjectPartInfo {
    category: ObjectCategory;
    subcategory: string;
    roomTypes: RoomType[];
    priceRange: PriceRange;
    functionality: ObjectFunction[];
    tags: string[];
    description: string;

    // Following CAS patterns for future enhancements
    conflicts: ObjectConflictInfo;
    validation: ObjectValidationInfo;
    visual: ObjectVisualInfo;
    dependencies: ObjectDependencyInfo;
}

/**
 * Object main categories
 */
export enum ObjectCategory {
    FURNITURE = 'furniture',
    DECORATIONS = 'decorations',
    LIGHTING = 'lighting',
    PLUMBING = 'plumbing',
    ELECTRONICS = 'electronics',
    APPLIANCES = 'appliances',
    OUTDOOR = 'outdoor',
    STORAGE = 'storage',
    UNKNOWN = 'unknown'
}

/**
 * Room types for object placement
 */
export enum RoomType {
    LIVING = 'living',
    BEDROOM = 'bedroom',
    KITCHEN = 'kitchen',
    BATHROOM = 'bathroom',
    DINING = 'dining',
    OUTDOOR = 'outdoor',
    OFFICE = 'office',
    KIDS = 'kids',
    GARAGE = 'garage',
    BASEMENT = 'basement',
    ANY = 'any'
}

/**
 * Price ranges for objects
 */
export enum PriceRange {
    BUDGET = 'budget',      // < 500
    MID = 'mid',           // 500-2000
    EXPENSIVE = 'expensive', // > 2000
    UNKNOWN = 'unknown'
}

/**
 * Object functionality types
 */
export enum ObjectFunction {
    SEATING = 'seating',
    SLEEPING = 'sleeping',
    STORAGE = 'storage',
    ENTERTAINMENT = 'entertainment',
    COOKING = 'cooking',
    HYGIENE = 'hygiene',
    DECORATION = 'decoration',
    LIGHTING = 'lighting',
    EXERCISE = 'exercise',
    STUDY = 'study',
    HOBBY = 'hobby',
    UNKNOWN = 'unknown'
}

/**
 * Object summary information for categorization
 */
export interface ObjectPartSummary {
    primaryCategory: string;
    subcategory: string;
    description: string;
}

/**
 * Display names mapping for object categories
 */
export const OBJECT_CATEGORY_DISPLAY_NAMES: Record<ObjectCategory, string> = {
    [ObjectCategory.FURNITURE]: 'Furniture',
    [ObjectCategory.DECORATIONS]: 'Decorations',
    [ObjectCategory.LIGHTING]: 'Lighting',
    [ObjectCategory.PLUMBING]: 'Plumbing',
    [ObjectCategory.ELECTRONICS]: 'Electronics',
    [ObjectCategory.APPLIANCES]: 'Appliances',
    [ObjectCategory.OUTDOOR]: 'Outdoor',
    [ObjectCategory.STORAGE]: 'Storage',
    [ObjectCategory.UNKNOWN]: 'Unknown Object'
};

/**
 * Object resource type identifiers
 */
export const OBJECT_DEFINITION_RESOURCE_TYPE = 0x319E4F1D;
export const OBJECT_CATALOG_RESOURCE_TYPE = 0x9151E6BC;

/**
 * Default fallback object part info for when analysis fails
 */
export const DEFAULT_OBJECT_PART_INFO: ObjectPartInfo = {
    category: ObjectCategory.UNKNOWN,
    subcategory: 'unknown',
    roomTypes: [RoomType.ANY],
    priceRange: PriceRange.UNKNOWN,
    functionality: [ObjectFunction.UNKNOWN],
    tags: [],
    description: 'Unknown object content',
    conflicts: {
        hasConflicts: false,
        conflictType: [],
        conflictingFiles: [],
        conflictSeverity: 'low' as any,
        conflictDetails: []
    },
    validation: {
        isValid: true,
        isBroken: false,
        validationIssues: [],
        autoDeleteRecommended: false,
        repairSuggestions: []
    },
    visual: {
        thumbnailGenerated: false,
        previewAvailable: false,
        visualTags: [],
        colorSwatches: [],
        displayCategory: 'unknown',
        sortOrder: 0,
        customTags: []
    },
    dependencies: {
        requiredMods: [],
        optionalMods: [],
        hasUnmetDependencies: false,
        dependencyWarnings: [],
        coreModsRequired: []
    }
};

/**
 * Object Conflict Information (following CAS patterns)
 */
export interface ObjectConflictInfo {
    hasConflicts: boolean;
    conflictType: ObjectConflictType[];
    conflictingFiles: string[];
    conflictSeverity: ConflictSeverity;
    conflictDetails: string[];
}

export enum ObjectConflictType {
    DUPLICATE_RESOURCE = 'duplicate_resource',
    PLACEMENT_CONFLICT = 'placement_conflict',
    FUNCTIONALITY_OVERLAP = 'functionality_overlap',
    CATEGORY_CONFLICT = 'category_conflict'
}

export enum ConflictSeverity {
    LOW = 'low',
    MEDIUM = 'medium',
    HIGH = 'high',
    CRITICAL = 'critical'
}

/**
 * Object Validation Information
 */
export interface ObjectValidationInfo {
    isValid: boolean;
    isBroken: boolean;
    validationIssues: ObjectValidationIssue[];
    autoDeleteRecommended: boolean;
    repairSuggestions: string[];
}

export interface ObjectValidationIssue {
    type: ObjectValidationIssueType;
    severity: ConflictSeverity;
    description: string;
    affectedResources: string[];
    autoFixAvailable: boolean;
}

export enum ObjectValidationIssueType {
    MISSING_MESH = 'missing_mesh',
    CORRUPTED_TEXTURE = 'corrupted_texture',
    INVALID_PLACEMENT = 'invalid_placement',
    BROKEN_FUNCTIONALITY = 'broken_functionality',
    MISSING_DEPENDENCY = 'missing_dependency'
}

/**
 * Object Visual Information
 */
export interface ObjectVisualInfo {
    thumbnailPath?: string;
    thumbnailGenerated: boolean;
    previewAvailable: boolean;
    visualTags: string[];
    colorSwatches: ObjectColorSwatch[];
    displayCategory: string;
    sortOrder: number;
    customTags: string[];
}

export interface ObjectColorSwatch {
    color: string;
    name?: string;
    isPrimary: boolean;
}

/**
 * Object Dependency Information
 */
export interface ObjectDependencyInfo {
    requiredMods: ObjectRequiredMod[];
    optionalMods: ObjectRequiredMod[];
    hasUnmetDependencies: boolean;
    dependencyWarnings: string[];
    coreModsRequired: ObjectCoreModType[];
}

export interface ObjectRequiredMod {
    modName: string;
    author?: string;
    version?: string;
    isInstalled: boolean;
    installPath?: string;
    downloadUrl?: string;
    isCore: boolean;
}

export enum ObjectCoreModType {
    TOOL_MOD = 'tool_mod',
    FUNCTIONAL_MOD = 'functional_mod',
    MESH_PACK = 'mesh_pack'
}
