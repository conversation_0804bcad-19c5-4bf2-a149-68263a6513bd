#!/usr/bin/env node

/**
 * Debug Metadata Extraction Tool
 * 
 * Deep debugging tool to understand why internal metadata extraction
 * isn't working and we're only getting filename-based results.
 */

import * as fs from 'fs';
import * as path from 'path';
import { ZipFileAnalyzer } from '../services/analysis/specialized/script/ZipFileAnalyzer';
import { MetadataExtractor } from '../services/analysis/specialized/script/MetadataExtractor';
import { FilenameMetadataExtractor } from '../services/analysis/specialized/common/FilenameMetadataExtractor';
import { ManifestAnalyzer } from '../services/analysis/specialized/common/ManifestAnalyzer';
import { DEFAULT_SCRIPT_MOD_INFO } from '../services/analysis/specialized/script/types';
import { Package } from '@s4tk/models';

class MetadataExtractionDebugger {
    
    /**
     * Debug metadata extraction for a specific file
     */
    public async debugFile(filePath: string): Promise<void> {
        const fileName = path.basename(filePath);
        console.log(`\n🔍 DEBUGGING: ${fileName}`);
        console.log('='.repeat(60));
        
        try {
            const buffer = fs.readFileSync(filePath);
            
            // Test 1: Filename extraction
            console.log('\n📁 FILENAME EXTRACTION:');
            const filenameResult = FilenameMetadataExtractor.extractFromFilename(filePath);
            console.log(`   Author: ${filenameResult.author || 'None'}`);
            console.log(`   Version: ${filenameResult.version || 'None'}`);
            console.log(`   Confidence: ${filenameResult.confidence}%`);
            console.log(`   Pattern: ${filenameResult.pattern || 'None'}`);
            
            // Test 2: Content analysis
            if (fileName.endsWith('.ts4script')) {
                console.log('\n📦 ZIP CONTENT ANALYSIS:');
                await this.debugZipContent(buffer, fileName);
            } else if (fileName.endsWith('.package')) {
                console.log('\n📦 PACKAGE CONTENT ANALYSIS:');
                await this.debugPackageContent(buffer, fileName);
            }
            
            // Test 3: Full script analysis pipeline
            console.log('\n🔧 FULL PIPELINE TEST:');
            const scriptInfo = { ...DEFAULT_SCRIPT_MOD_INFO };
            
            // Simulate the full pipeline
            const filenameMetadata = FilenameMetadataExtractor.extractFromFilename(filePath);
            if (filenameMetadata.author) scriptInfo.author = filenameMetadata.author;
            if (filenameMetadata.version) scriptInfo.version = filenameMetadata.version;
            
            if (fileName.endsWith('.ts4script')) {
                const bufferStr = buffer.toString('binary');
                const manifestMetadata = ZipFileAnalyzer.extractManifestMetadata(bufferStr);
                console.log(`   Manifest metadata found: ${JSON.stringify(manifestMetadata)}`);
                
                if (manifestMetadata.author && !scriptInfo.author) scriptInfo.author = manifestMetadata.author;
                if (manifestMetadata.version && !scriptInfo.version) scriptInfo.version = manifestMetadata.version;
                
                MetadataExtractor.extractFromZipContent(bufferStr, scriptInfo);
            }
            
            console.log(`   Final Author: ${scriptInfo.author || 'None'}`);
            console.log(`   Final Version: ${scriptInfo.version || 'None'}`);
            
        } catch (error) {
            console.error(`   ❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    
    /**
     * Debug ZIP content in detail
     */
    private async debugZipContent(buffer: Buffer, fileName: string): Promise<void> {
        try {
            // Check if it's a valid ZIP
            const isZip = ZipFileAnalyzer.isZipFile(buffer);
            console.log(`   Is valid ZIP: ${isZip}`);
            
            if (!isZip) {
                console.log('   ⚠️ Not a valid ZIP file, skipping ZIP analysis');
                return;
            }
            
            const bufferStr = buffer.toString('binary');
            
            // Show ZIP structure info
            console.log(`   ZIP buffer size: ${buffer.length} bytes`);
            console.log(`   Binary string length: ${bufferStr.length}`);
            
            // Look for Python files
            const pythonFiles = this.findPythonFiles(bufferStr);
            console.log(`   Python files found: ${pythonFiles.length}`);
            pythonFiles.forEach(file => console.log(`     - ${file}`));
            
            // Look for manifest files
            console.log('\n   🔍 MANIFEST FILE SEARCH:');
            const manifestPatterns = [
                { name: 'Llama-Logic YAML', pattern: /llamalogic\.modfilemanifest\.yml/i },
                { name: 'Manifest JSON', pattern: /manifest\.json/i },
                { name: 'Mod Info JSON', pattern: /mod_info\.json/i },
                { name: 'Package JSON', pattern: /package\.json/i },
                { name: 'Info TXT', pattern: /info\.txt/i },
                { name: 'README TXT', pattern: /readme\.txt/i },
                { name: 'README MD', pattern: /readme\.md/i }
            ];
            
            let foundManifests = 0;
            for (const { name, pattern } of manifestPatterns) {
                const match = bufferStr.match(pattern);
                if (match) {
                    foundManifests++;
                    console.log(`     ✅ Found ${name} at position ${match.index}`);
                    
                    // Try to extract content
                    const content = this.extractFileContentByPattern(bufferStr, pattern);
                    if (content) {
                        console.log(`     📄 Content preview (first 200 chars):`);
                        console.log(`     "${content.substring(0, 200)}..."`);
                    }
                } else {
                    console.log(`     ❌ No ${name} found`);
                }
            }
            
            if (foundManifests === 0) {
                console.log('     ⚠️ No manifest files found');
            }
            
            // Look for Python metadata patterns
            console.log('\n   🐍 PYTHON METADATA SEARCH:');
            const pythonPatterns = [
                { name: '__author__', pattern: /__author__\s*=\s*['"](.+?)['"]/g },
                { name: '__version__', pattern: /__version__\s*=\s*['"](.+?)['"]/g },
                { name: 'Author comment', pattern: /# Author:\s*(.+?)$/gm },
                { name: 'Version comment', pattern: /# Version:\s*(.+?)$/gm }
            ];
            
            let foundPythonMetadata = 0;
            for (const { name, pattern } of pythonPatterns) {
                const matches = [...bufferStr.matchAll(pattern)];
                if (matches.length > 0) {
                    foundPythonMetadata++;
                    console.log(`     ✅ Found ${name}: ${matches.length} matches`);
                    matches.forEach((match, i) => {
                        console.log(`       ${i + 1}. "${match[1]}"`);
                    });
                } else {
                    console.log(`     ❌ No ${name} found`);
                }
            }
            
            if (foundPythonMetadata === 0) {
                console.log('     ⚠️ No Python metadata patterns found');
            }
            
            // Show raw content sample
            console.log('\n   📄 RAW CONTENT SAMPLE (first 500 chars):');
            const printableContent = bufferStr.replace(/[\x00-\x1F\x7F-\xFF]/g, '.');
            console.log(`   "${printableContent.substring(0, 500)}..."`);
            
        } catch (error) {
            console.error(`   ❌ ZIP analysis error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Debug package content in detail
     */
    private async debugPackageContent(buffer: Buffer, fileName: string): Promise<void> {
        try {
            console.log(`   Package buffer size: ${buffer.length} bytes`);

            // Try to load as S4TK package
            let s4tkPackage: Package;
            try {
                s4tkPackage = Package.from(buffer);
                console.log(`   ✅ Successfully loaded as S4TK package`);
                console.log(`   📊 Resource count: ${s4tkPackage.size}`);
            } catch (error) {
                console.log(`   ❌ Failed to load as S4TK package: ${error instanceof Error ? error.message : 'Unknown error'}`);
                return;
            }

            // Test manifest extraction
            console.log('\n   🔍 MANIFEST EXTRACTION TEST:');
            const manifestMetadata = ManifestAnalyzer.extractFromPackage(s4tkPackage);
            console.log(`   Author: ${manifestMetadata.author || 'None'}`);
            console.log(`   Version: ${manifestMetadata.version || 'None'}`);
            console.log(`   Mod Name: ${manifestMetadata.modName || 'None'}`);
            console.log(`   Description: ${manifestMetadata.description || 'None'}`);
            console.log(`   Confidence: ${manifestMetadata.confidence}%`);
            console.log(`   Source: ${manifestMetadata.source || 'None'}`);

            // Show resource types
            console.log('\n   📋 RESOURCE TYPES:');
            const resourceTypes = new Map<number, number>();
            for (const entry of s4tkPackage.entries.values()) {
                const type = entry.key.type;
                resourceTypes.set(type, (resourceTypes.get(type) || 0) + 1);
            }

            const sortedTypes = Array.from(resourceTypes.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10); // Show top 10 resource types

            sortedTypes.forEach(([type, count]) => {
                console.log(`     Type ${type}: ${count} resources`);
            });

            // Look for specific resource types that might contain metadata
            console.log('\n   🔍 METADATA-RELEVANT RESOURCES:');
            let foundMetadataResources = 0;

            for (const entry of s4tkPackage.entries.values()) {
                const type = entry.key.type;

                // Check for SnippetTuning (potential manifests)
                if (type === 0x03B33DDF) { // SnippetTuning
                    foundMetadataResources++;
                    console.log(`     ✅ SnippetTuning resource found (potential manifest)`);

                    try {
                        const content = entry.value.toString();
                        if (content.includes('ModFileManifest') || content.includes('llamalogic')) {
                            console.log(`       🎯 Llama-Logic manifest detected!`);
                        }
                        console.log(`       Content preview: "${content.substring(0, 100)}..."`);
                    } catch (error) {
                        console.log(`       ❌ Could not read content`);
                    }
                }

                // Check for StringTable
                if (type === 0x220557DA) { // StringTable
                    foundMetadataResources++;
                    console.log(`     ✅ StringTable resource found`);
                }

                // Check for CombinedTuning
                if (type === 0x62E94D38) { // CombinedTuning
                    foundMetadataResources++;
                    console.log(`     ✅ CombinedTuning resource found`);
                }
            }

            if (foundMetadataResources === 0) {
                console.log(`     ⚠️ No metadata-relevant resources found`);
            }

        } catch (error) {
            console.error(`   ❌ Package analysis error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    
    /**
     * Find Python files in ZIP content
     */
    private findPythonFiles(bufferStr: string): string[] {
        const pyFileMatches = bufferStr.match(/[\w\-_]+\.py/g);
        return pyFileMatches ? [...new Set(pyFileMatches)] : [];
    }
    
    /**
     * Extract file content by pattern (simplified version for debugging)
     */
    private extractFileContentByPattern(bufferStr: string, pattern: RegExp): string | null {
        try {
            const match = bufferStr.match(pattern);
            if (!match) return null;
            
            const matchIndex = match.index!;
            const contentStart = bufferStr.indexOf('\n', matchIndex);
            if (contentStart === -1) return null;
            
            const contentEnd = Math.min(contentStart + 1000, bufferStr.length);
            const content = bufferStr.substring(contentStart + 1, contentEnd);
            
            if (content.length < 10 || !/[a-zA-Z]/.test(content)) return null;
            
            return content;
        } catch (error) {
            return null;
        }
    }
    
    /**
     * Debug all files in assets directory
     */
    public async debugAllFiles(): Promise<void> {
        console.log('🔍 METADATA EXTRACTION DEBUGGING TOOL');
        console.log('=====================================\n');
        
        const assetsDir = path.join(process.cwd(), 'assets');
        
        if (!fs.existsSync(assetsDir)) {
            console.error('❌ Assets directory not found:', assetsDir);
            return;
        }
        
        const allFiles = fs.readdirSync(assetsDir)
            .filter(file => file.endsWith('.package') || file.endsWith('.ts4script'));

        // Get a mix of script and package files for testing
        const scriptFiles = allFiles.filter(f => f.endsWith('.ts4script')).slice(0, 2);
        const packageFiles = allFiles.filter(f => f.endsWith('.package')).slice(0, 3);
        const files = [...scriptFiles, ...packageFiles];
        
        console.log(`📁 Found ${files.length} test files (showing first 3 for detailed analysis)\n`);
        
        for (const file of files) {
            const filePath = path.join(assetsDir, file);
            await this.debugFile(filePath);
        }
        
        console.log('\n🎯 DEBUGGING SUMMARY:');
        console.log('====================');
        console.log('This tool helps identify why internal metadata extraction isn\'t working.');
        console.log('Look for:');
        console.log('- Whether ZIP files are valid');
        console.log('- Whether manifest files exist');
        console.log('- Whether Python metadata patterns are found');
        console.log('- Whether the extraction pipeline is working correctly');
    }
}

// Run the debugger
async function main() {
    const debugTool = new MetadataExtractionDebugger();
    await debugTool.debugAllFiles();
}

if (require.main === module) {
    main().catch(console.error);
}
