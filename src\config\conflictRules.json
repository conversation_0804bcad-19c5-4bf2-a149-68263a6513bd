{"metadata": {"lastUpdated": "2025-01-27", "sources": ["EA Forums - Broken and Updated Mods threads (luthienrising)", "Creator documentation and compatibility warnings", "Community-verified conflict reports with reproduction steps"], "note": "Only includes verified conflicts from official sources - no speculation"}, "verifiedConflicts": {"mcccMultipleVersions": {"source": "EA Forums - <PERSON><PERSON><PERSON> (MCCC Creator)", "evidence": "Creator explicitly states only one MCCC version should be installed", "patterns": ["mc_cmd_center", "mc_command_center", "mccc"], "type": "version_conflict", "description": "Multiple MC Command Center versions cause critical conflicts", "severity": "critical", "resolution": "Keep only the latest MCCC version and remove all others", "autoFix": false, "verifiedBy": "Creator documentation"}, "uiCheatsMultiple": {"source": "EA Forums - weerbesu (UI Cheats Creator)", "evidence": "Multiple UI Cheats versions cause menu conflicts", "patterns": ["ui_cheats_extension", "ui_cheat"], "type": "version_conflict", "description": "Multiple UI Cheats Extension versions conflict", "severity": "high", "resolution": "Keep only the latest UI Cheats Extension version", "autoFix": false, "verifiedBy": "Creator documentation"}, "childSlidersBroken": {"source": "EA Forums - Official Broken Mods List", "evidence": "Game update broke all child/toddler sliders - causes save corruption", "patterns": ["height_slider", "toddler_slider", "child_slider"], "type": "game_breaking", "description": "Child/toddler sliders broken by game update - prevent saving", "severity": "critical", "resolution": "Remove all child/toddler slider mods until updated", "autoFix": false, "verifiedBy": "EA Forums official list"}, "lot51Dependencies": {"source": "EA Forums - Lot51 Creator Documentation", "evidence": "Lot51 mods require core library to function", "patterns": ["lot51_"], "type": "dependency_missing", "description": "Lot51 mods require Lot51 Core Library", "severity": "high", "resolution": "Install Lot51 Core Library from official source", "autoFix": false, "verifiedBy": "Creator documentation", "requiredMods": ["lot51_core"]}}, "resourceConflicts": {"note": "Only script and tuning resource conflicts are flagged - these actually break gameplay", "criticalTypes": {"scriptResources": {"resourceType": "0x6017E896", "description": "Python script conflicts cause crashes", "severity": "critical", "evidence": "EA Forums - multiple crash reports"}, "tuningResources": {"resourceType": "0x62E94D38", "description": "Tuning conflicts cause gameplay issues", "severity": "high", "evidence": "EA Forums - gameplay malfunction reports"}}}, "excludedConflicts": {"note": "These are NOT flagged as conflicts - they are beneficial variety", "casContent": {"reason": "Multiple hair/clothing options are good for players", "evidence": "Community consensus - variety is desired"}, "objectContent": {"reason": "Multiple furniture options are good for players", "evidence": "Community consensus - decoration variety is desired"}, "wonderfulWickedWhims": {"reason": "No verified evidence they cannot coexist", "evidence": "Reddit reports show users running both successfully"}}, "severityLevels": {"low": {"color": "yellow", "action": "monitor", "description": "Minor conflicts that rarely cause issues"}, "medium": {"color": "orange", "action": "review", "description": "Moderate conflicts that may cause issues"}, "high": {"color": "red", "action": "resolve", "description": "Significant conflicts that likely cause issues"}, "critical": {"color": "darkred", "action": "immediate", "description": "Critical conflicts that will cause crashes or major issues"}}, "resolutionStrategies": {"keep_latest": "Keep the most recently modified version", "keep_all": "Keep all versions (usually safe for CAS content)", "user_choice": "User must manually choose which to keep", "choose_one": "User must choose only one from the conflicting set", "keep_one": "Keep only one, remove others", "install_dependency": "Install required dependency mods", "update_mods": "Update to newer versions", "check_compatibility": "Manually verify compatibility", "check_versions": "Ensure compatible versions are installed", "investigate": "Manual investigation required", "monitor": "Monitor for issues but no immediate action needed"}}