/**
 * Script Intelligence Analyzer
 * 
 * Provides intelligent analysis for .ts4script files including
 * framework detection, complexity analysis, and performance assessment.
 */

export interface ScriptIntelligence {
    scriptType: ScriptType;
    framework: ScriptFramework;
    complexity: ScriptComplexity;
    features: ScriptFeature[];
    dependencies: string[];
    performance: ScriptPerformanceImpact;
    qualityScore: number;
    riskLevel: 'low' | 'medium' | 'high';
}

export interface ScriptComplexity {
    level: 'simple' | 'moderate' | 'complex' | 'advanced';
    score: number;
    factors: string[];
}

export interface ScriptPerformanceImpact {
    estimatedImpact: 'minimal' | 'low' | 'medium' | 'high';
    memoryUsage: 'low' | 'medium' | 'high';
    cpuUsage: 'low' | 'medium' | 'high';
    loadTime: 'fast' | 'medium' | 'slow';
}

export type ScriptType = 
    | 'python_mod'
    | 'javascript_mod'
    | 'compiled_library'
    | 'framework_extension'
    | 'utility_script'
    | 'unknown';

export type ScriptFramework = 
    | 'mc_command_center'
    | 'basemental_drugs'
    | 'wicked_whims'
    | 'slice_of_life'
    | 'ui_cheats'
    | 'custom_framework'
    | 'standalone'
    | 'unknown';

export type ScriptFeature = 
    | 'ui_modification'
    | 'gameplay_override'
    | 'cheat_commands'
    | 'automation'
    | 'data_tracking'
    | 'event_handling'
    | 'menu_injection'
    | 'notification_system'
    | 'save_data_management'
    | 'network_communication';

/**
 * Analyzes .ts4script files for comprehensive intelligence
 */
export class ScriptIntelligenceAnalyzer {
    
    /**
     * Analyze script file for intelligence data
     */
    public static analyzeScriptIntelligence(buffer: Buffer, fileName: string): ScriptIntelligence {
        const content = this.extractScriptContent(buffer);
        
        const intelligence: ScriptIntelligence = {
            scriptType: this.determineScriptType(content, fileName),
            framework: this.detectFramework(content, fileName),
            complexity: this.analyzeComplexity(content),
            features: this.detectFeatures(content),
            dependencies: this.extractDependencies(content),
            performance: this.assessPerformance(content, buffer.length),
            qualityScore: 0,
            riskLevel: 'low'
        };
        
        // Calculate quality score and risk level
        intelligence.qualityScore = this.calculateQualityScore(intelligence, content);
        intelligence.riskLevel = this.assessRiskLevel(intelligence, content);
        
        return intelligence;
    }
    
    /**
     * Extract readable content from script buffer
     */
    private static extractScriptContent(buffer: Buffer): string {
        try {
            // Try to extract readable text from the script
            const content = buffer.toString('utf8');
            
            // If it's mostly binary, try to extract strings
            if (this.isMostlyBinary(content)) {
                return this.extractStringsFromBinary(buffer);
            }
            
            return content;
        } catch (error) {
            // Fallback to binary string extraction
            return this.extractStringsFromBinary(buffer);
        }
    }
    
    /**
     * Check if content is mostly binary
     */
    private static isMostlyBinary(content: string): boolean {
        const printableChars = content.replace(/[^\x20-\x7E\n\r\t]/g, '').length;
        const totalChars = content.length;
        return printableChars / totalChars < 0.7; // Less than 70% printable
    }
    
    /**
     * Extract readable strings from binary content
     */
    private static extractStringsFromBinary(buffer: Buffer): string {
        const strings: string[] = [];
        let currentString = '';
        
        for (let i = 0; i < buffer.length; i++) {
            const byte = buffer[i];
            
            // Check if byte is printable ASCII
            if (byte >= 32 && byte <= 126) {
                currentString += String.fromCharCode(byte);
            } else {
                if (currentString.length >= 4) { // Only keep strings of 4+ chars
                    strings.push(currentString);
                }
                currentString = '';
            }
        }
        
        // Add final string if it exists
        if (currentString.length >= 4) {
            strings.push(currentString);
        }
        
        return strings.join(' ');
    }
    
    /**
     * Determine script type based on content and filename
     */
    private static determineScriptType(content: string, fileName: string): ScriptType {
        const lowerContent = content.toLowerCase();
        const lowerFileName = fileName.toLowerCase();
        
        // Check for Python indicators
        if (lowerContent.includes('import ') || lowerContent.includes('def ') || 
            lowerContent.includes('class ') || lowerContent.includes('python')) {
            return 'python_mod';
        }
        
        // Check for JavaScript indicators
        if (lowerContent.includes('function') || lowerContent.includes('var ') || 
            lowerContent.includes('const ') || lowerContent.includes('javascript')) {
            return 'javascript_mod';
        }
        
        // Check for compiled library indicators
        if (lowerFileName.includes('lib') || lowerFileName.includes('dll') || 
            lowerContent.includes('library')) {
            return 'compiled_library';
        }
        
        // Check for framework extension
        if (lowerContent.includes('framework') || lowerContent.includes('extension')) {
            return 'framework_extension';
        }
        
        // Check for utility script
        if (lowerFileName.includes('util') || lowerFileName.includes('tool') || 
            lowerContent.includes('utility')) {
            return 'utility_script';
        }
        
        return 'unknown';
    }
    
    /**
     * Detect script framework
     */
    private static detectFramework(content: string, fileName: string): ScriptFramework {
        const lowerContent = content.toLowerCase();
        const lowerFileName = fileName.toLowerCase();
        
        // Check for known frameworks
        if (lowerContent.includes('mc_command_center') || lowerContent.includes('mccc') || 
            lowerFileName.includes('mccc')) {
            return 'mc_command_center';
        }
        
        if (lowerContent.includes('basemental') || lowerFileName.includes('basemental')) {
            return 'basemental_drugs';
        }
        
        if (lowerContent.includes('wicked') || lowerContent.includes('whims') || 
            lowerFileName.includes('wicked')) {
            return 'wicked_whims';
        }
        
        if (lowerContent.includes('slice_of_life') || lowerContent.includes('sol') || 
            lowerFileName.includes('sol')) {
            return 'slice_of_life';
        }
        
        if (lowerContent.includes('ui_cheats') || lowerContent.includes('uicheats') || 
            lowerFileName.includes('uicheats')) {
            return 'ui_cheats';
        }
        
        // Check for custom framework indicators
        if (lowerContent.includes('framework') || lowerContent.includes('core') || 
            lowerContent.includes('base')) {
            return 'custom_framework';
        }
        
        return 'standalone';
    }
    
    /**
     * Analyze script complexity
     */
    private static analyzeComplexity(content: string): ScriptComplexity {
        const factors: string[] = [];
        let score = 0;
        
        // Check for various complexity indicators
        const functionCount = (content.match(/def |function /gi) || []).length;
        const classCount = (content.match(/class /gi) || []).length;
        const importCount = (content.match(/import |from /gi) || []).length;
        const lineCount = content.split('\n').length;
        
        // Scoring based on code structure
        if (functionCount > 10) {
            score += 20;
            factors.push('Many functions');
        } else if (functionCount > 5) {
            score += 10;
            factors.push('Multiple functions');
        }
        
        if (classCount > 5) {
            score += 25;
            factors.push('Multiple classes');
        } else if (classCount > 0) {
            score += 10;
            factors.push('Object-oriented');
        }
        
        if (importCount > 10) {
            score += 15;
            factors.push('Many dependencies');
        } else if (importCount > 3) {
            score += 8;
            factors.push('External dependencies');
        }
        
        if (lineCount > 1000) {
            score += 20;
            factors.push('Large codebase');
        } else if (lineCount > 500) {
            score += 10;
            factors.push('Medium codebase');
        }
        
        // Determine complexity level
        let level: 'simple' | 'moderate' | 'complex' | 'advanced';
        if (score >= 60) level = 'advanced';
        else if (score >= 40) level = 'complex';
        else if (score >= 20) level = 'moderate';
        else level = 'simple';
        
        return { level, score, factors };
    }
    
    /**
     * Detect script features
     */
    private static detectFeatures(content: string): ScriptFeature[] {
        const features: ScriptFeature[] = [];
        const lowerContent = content.toLowerCase();
        
        if (lowerContent.includes('ui') || lowerContent.includes('interface') || 
            lowerContent.includes('menu')) {
            features.push('ui_modification');
        }
        
        if (lowerContent.includes('cheat') || lowerContent.includes('command')) {
            features.push('cheat_commands');
        }
        
        if (lowerContent.includes('auto') || lowerContent.includes('schedule')) {
            features.push('automation');
        }
        
        if (lowerContent.includes('data') || lowerContent.includes('track') || 
            lowerContent.includes('log')) {
            features.push('data_tracking');
        }
        
        if (lowerContent.includes('event') || lowerContent.includes('listener') || 
            lowerContent.includes('handler')) {
            features.push('event_handling');
        }
        
        if (lowerContent.includes('notification') || lowerContent.includes('alert') || 
            lowerContent.includes('message')) {
            features.push('notification_system');
        }
        
        if (lowerContent.includes('save') || lowerContent.includes('persist') || 
            lowerContent.includes('storage')) {
            features.push('save_data_management');
        }
        
        if (lowerContent.includes('network') || lowerContent.includes('http') || 
            lowerContent.includes('api')) {
            features.push('network_communication');
        }
        
        return features;
    }
    
    /**
     * Extract dependencies from script content
     */
    private static extractDependencies(content: string): string[] {
        const dependencies: string[] = [];
        
        // Extract import statements
        const importMatches = content.match(/(?:import|from)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g);
        if (importMatches) {
            importMatches.forEach(match => {
                const dep = match.replace(/(?:import|from)\s+/, '');
                if (dep && !dependencies.includes(dep)) {
                    dependencies.push(dep);
                }
            });
        }
        
        return dependencies.slice(0, 10); // Limit to first 10 dependencies
    }
    
    /**
     * Assess performance impact
     */
    private static assessPerformance(content: string, fileSize: number): ScriptPerformanceImpact {
        const lowerContent = content.toLowerCase();
        
        let estimatedImpact: 'minimal' | 'low' | 'medium' | 'high' = 'minimal';
        let memoryUsage: 'low' | 'medium' | 'high' = 'low';
        let cpuUsage: 'low' | 'medium' | 'high' = 'low';
        let loadTime: 'fast' | 'medium' | 'slow' = 'fast';
        
        // File size impact
        if (fileSize > 5 * 1024 * 1024) { // 5MB+
            estimatedImpact = 'high';
            loadTime = 'slow';
        } else if (fileSize > 1024 * 1024) { // 1MB+
            estimatedImpact = 'medium';
            loadTime = 'medium';
        }
        
        // Content-based assessment
        if (lowerContent.includes('loop') || lowerContent.includes('while') || 
            lowerContent.includes('for')) {
            cpuUsage = 'medium';
        }
        
        if (lowerContent.includes('data') || lowerContent.includes('cache') || 
            lowerContent.includes('store')) {
            memoryUsage = 'medium';
        }
        
        if (lowerContent.includes('network') || lowerContent.includes('file') || 
            lowerContent.includes('database')) {
            estimatedImpact = estimatedImpact === 'minimal' ? 'low' : estimatedImpact;
        }
        
        return { estimatedImpact, memoryUsage, cpuUsage, loadTime };
    }
    
    /**
     * Calculate quality score
     */
    private static calculateQualityScore(intelligence: ScriptIntelligence, content: string): number {
        let score = 50; // Base score
        
        // Positive factors
        if (intelligence.framework !== 'unknown') score += 15;
        if (intelligence.complexity.level === 'moderate' || intelligence.complexity.level === 'complex') score += 10;
        if (intelligence.features.length > 3) score += 10;
        if (intelligence.dependencies.length > 0 && intelligence.dependencies.length < 10) score += 5;
        if (intelligence.performance.estimatedImpact === 'minimal') score += 10;
        
        // Negative factors
        if (intelligence.performance.estimatedImpact === 'high') score -= 20;
        if (intelligence.complexity.level === 'simple') score -= 5;
        if (intelligence.scriptType === 'unknown') score -= 10;
        
        return Math.max(0, Math.min(100, score));
    }
    
    /**
     * Assess risk level
     */
    private static assessRiskLevel(intelligence: ScriptIntelligence, content: string): 'low' | 'medium' | 'high' {
        const lowerContent = content.toLowerCase();
        
        // High risk indicators
        if (lowerContent.includes('delete') || lowerContent.includes('remove') || 
            lowerContent.includes('modify') || intelligence.performance.estimatedImpact === 'high') {
            return 'high';
        }
        
        // Medium risk indicators
        if (intelligence.complexity.level === 'advanced' || 
            intelligence.features.includes('save_data_management') ||
            intelligence.features.includes('network_communication')) {
            return 'medium';
        }
        
        return 'low';
    }
}
