<template>
  <div class="resource-breakdown-chart">
    <div class="chart-container">
      <svg :width="size" :height="size" class="pie-chart">
        <g :transform="`translate(${size/2}, ${size/2})`">
          <path
            v-for="(segment, index) in segments"
            :key="segment.type"
            :d="segment.path"
            :fill="segment.color"
            :class="`segment segment-${segment.type}`"
            @mouseenter="hoveredSegment = segment"
            @mouseleave="hoveredSegment = null"
          />
        </g>
      </svg>
      
      <!-- Center label -->
      <div class="center-label">
        <div class="total-count">{{ totalResources }}</div>
        <div class="total-label">Resources</div>
      </div>
      
      <!-- Hover tooltip -->
      <div v-if="hoveredSegment" class="tooltip">
        <div class="tooltip-type">{{ formatResourceType(hoveredSegment.type) }}</div>
        <div class="tooltip-count">{{ hoveredSegment.count }} resources</div>
        <div class="tooltip-percentage">{{ hoveredSegment.percentage }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

interface ResourceBreakdown {
  cas: number;
  buildBuy: number;
  gameplay: number;
  audio: number;
  visual: number;
  tuning: number;
  scripts: number;
  other: number;
}

const props = defineProps<{
  breakdown: ResourceBreakdown;
  size?: number;
}>();

const size = props.size || 200;
const hoveredSegment = ref<any>(null);

const resourceColors: Record<string, string> = {
  cas: '#ff6699',
  buildBuy: '#0099cc',
  gameplay: '#00ff41',
  visual: '#9966cc',
  audio: '#ff9933',
  tuning: '#0099cc',
  scripts: '#f59e0b',
  other: '#a3a3a3'
};

const totalResources = computed(() => {
  return Object.values(props.breakdown).reduce((sum, count) => sum + count, 0);
});

const segments = computed(() => {
  if (totalResources.value === 0) return [];
  
  const nonZeroResources = Object.entries(props.breakdown)
    .filter(([_, count]) => count > 0)
    .map(([type, count]) => ({
      type,
      count,
      percentage: Math.round((count / totalResources.value) * 100),
      color: resourceColors[type] || resourceColors.other
    }));
  
  let currentAngle = 0;
  const radius = (size - 40) / 2;
  
  return nonZeroResources.map(resource => {
    const angle = (resource.count / totalResources.value) * 2 * Math.PI;
    const startAngle = currentAngle;
    const endAngle = currentAngle + angle;
    
    const x1 = Math.cos(startAngle) * radius;
    const y1 = Math.sin(startAngle) * radius;
    const x2 = Math.cos(endAngle) * radius;
    const y2 = Math.sin(endAngle) * radius;
    
    const largeArcFlag = angle > Math.PI ? 1 : 0;
    
    const path = [
      `M 0 0`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      `Z`
    ].join(' ');
    
    currentAngle += angle;
    
    return {
      ...resource,
      path
    };
  });
});

const formatResourceType = (type: string): string => {
  const types: Record<string, string> = {
    cas: 'CAS',
    buildBuy: 'Build/Buy',
    gameplay: 'Gameplay',
    audio: 'Audio',
    visual: 'Visual',
    tuning: 'Tuning',
    scripts: 'Scripts',
    other: 'Other'
  };
  return types[type] || type;
};
</script>

<style scoped>
.resource-breakdown-chart {
  position: relative;
  display: inline-block;
}

.chart-container {
  position: relative;
  display: inline-block;
}

.pie-chart {
  display: block;
}

.segment {
  cursor: pointer;
  transition: opacity var(--duration-150) var(--ease-out);
}

.segment:hover {
  opacity: 0.8;
}

.center-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.total-count {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
}

.total-label {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: var(--space-1);
}

.tooltip {
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  box-shadow: var(--shadow-lg);
  pointer-events: none;
  z-index: 10;
}

.tooltip-type {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.tooltip-count {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}

.tooltip-percentage {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
}
</style>
