import type { AnalyzedPackage } from '../../../types/analysis';
import * as path from 'path';

/**
 * Specialized component for extracting author information from mods
 * Handles various filename patterns and metadata sources
 */
export class AuthorExtractor {
    
    /**
     * Extracts author information from mod analysis
     */
    public static extractAuthor(analysis: AnalyzedPackage): string | null {
        const fileName = path.basename(analysis.filePath, path.extname(analysis.filePath));
        
        // Try filename patterns first
        const authorFromFilename = this.extractAuthorFromFilename(fileName);
        if (authorFromFilename) {
            return authorFromFilename;
        }

        // Check metadata for author information
        if (analysis.metadata.author) {
            return this.cleanAuthorName(analysis.metadata.author);
        }

        return null;
    }

    /**
     * Extracts author from filename using common patterns
     */
    private static extractAuthorFromFilename(fileName: string): string | null {
        // Common author patterns in filenames
        const authorPatterns = [
            /^([A-Za-z0-9_]+)[-_]/, // Author_ModName or Author-ModName
            /\[([A-Za-z0-9_\s]+)\]/, // [Author] ModName
            /^([A-Za-z0-9]+)\s+/, // Author ModName (space separated)
            /\(([A-Za-z0-9_\s]+)\)/, // (Author) ModName
            /by\s+([A-Za-z0-9_\s]+)/i, // ModName by Author
        ];

        for (const pattern of authorPatterns) {
            const match = fileName.match(pattern);
            if (match && match[1]) {
                const author = this.cleanAuthorName(match[1]);
                if (this.isValidAuthorName(author)) {
                    return author;
                }
            }
        }

        return null;
    }

    /**
     * Cleans and formats author names
     */
    private static cleanAuthorName(author: string): string {
        return author
            .replace(/[_-]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim()
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }

    /**
     * Validates if extracted text is likely a real author name
     */
    private static isValidAuthorName(author: string): boolean {
        // Filter out common false positives
        const invalidPatterns = [
            /^(mod|cc|custom|content|sims|the|and|or|for|with|by|v\d+|\d+)$/i,
            /^[0-9]+$/, // Pure numbers
            /^.{1,2}$/, // Too short
            /^.{30,}$/, // Too long
        ];

        return !invalidPatterns.some(pattern => pattern.test(author));
    }

    /**
     * Extracts multiple potential authors from complex filenames
     */
    public static extractMultipleAuthors(analysis: AnalyzedPackage): string[] {
        const fileName = path.basename(analysis.filePath, path.extname(analysis.filePath));
        const authors: string[] = [];

        // Look for collaboration patterns
        const collaborationPatterns = [
            /([A-Za-z0-9_]+)\s*[&+]\s*([A-Za-z0-9_]+)/g, // Author1 & Author2
            /([A-Za-z0-9_]+)\s*,\s*([A-Za-z0-9_]+)/g, // Author1, Author2
            /([A-Za-z0-9_]+)\s+and\s+([A-Za-z0-9_]+)/gi, // Author1 and Author2
        ];

        for (const pattern of collaborationPatterns) {
            let match;
            while ((match = pattern.exec(fileName)) !== null) {
                const author1 = this.cleanAuthorName(match[1]);
                const author2 = this.cleanAuthorName(match[2]);
                
                if (this.isValidAuthorName(author1)) {
                    authors.push(author1);
                }
                if (this.isValidAuthorName(author2)) {
                    authors.push(author2);
                }
            }
        }

        // If no collaboration found, try single author
        if (authors.length === 0) {
            const singleAuthor = this.extractAuthor(analysis);
            if (singleAuthor) {
                authors.push(singleAuthor);
            }
        }

        // Remove duplicates
        return [...new Set(authors)];
    }

    /**
     * Determines the primary author from multiple authors
     */
    public static getPrimaryAuthor(authors: string[]): string | null {
        if (authors.length === 0) return null;
        if (authors.length === 1) return authors[0];

        // For multiple authors, return the first one (usually the primary creator)
        return authors[0];
    }
}