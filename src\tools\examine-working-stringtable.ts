/**
 * Examine a working StringTable to understand the actual data structure
 */

import * as fs from 'fs';
import * as path from 'path';
import { Package, StringTableResource } from '@s4tk/models';
import { URT } from '../constants/unifiedResourceTypes';

async function examineWorkingStringTable(): Promise<void> {
    console.log('🔍 Examining Working StringTable Content');
    console.log('=' .repeat(60));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    const fileName = '!LittleMsSam_SimDaDatingApp_NoFWord.package'; // This one had StringTable
    const filePath = path.join(modsPath, fileName);
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ Test file not found');
        return;
    }
    
    try {
        console.log(`🔍 Examining: ${fileName}`);
        console.log('');
        
        const buffer = fs.readFileSync(filePath);
        const s4tkPackage = Package.from(buffer);
        
        // Find StringTable resources
        const stblResources = Array.from(s4tkPackage.entries.values())
            .filter(entry => entry.key.type === URT.StringTable);
        
        console.log(`📋 Found ${stblResources.length} StringTable resources`);
        console.log('');
        
        for (let i = 0; i < stblResources.length; i++) {
            const entry = stblResources[i];
            console.log(`📋 StringTable ${i + 1}:`);
            console.log(`   Resource ID: ${entry.id}`);
            console.log(`   Key Type: 0x${entry.key.type.toString(16).toUpperCase()}`);
            
            try {
                const stringTable = StringTableResource.from(entry.value);
                console.log(`   ✅ Successfully parsed with S4TK`);
                console.log(`   Locale: ${stringTable.locale || 'unknown'}`);
                console.log(`   Size: ${stringTable.size} entries`);
                console.log('');
                
                if (stringTable.size > 0) {
                    console.log(`   📝 All string entries:`);
                    
                    let entryCount = 0;
                    for (const [key, value] of stringTable.entries) {
                        entryCount++;
                        const truncatedValue = value.length > 150 ? 
                            value.substring(0, 150) + '...' : value;
                        console.log(`      [${entryCount}] "${key}" = "${truncatedValue}"`);
                        
                        // Stop after 50 entries to avoid spam
                        if (entryCount >= 50) {
                            console.log(`      ... and ${stringTable.size - 50} more entries`);
                            break;
                        }
                    }
                    
                    console.log('');
                    
                    // Analyze the patterns
                    console.log(`   🔍 Pattern Analysis:`);
                    const keys = Array.from(stringTable.entries.keys());
                    const values = Array.from(stringTable.entries.values());
                    
                    console.log(`      Key patterns:`);
                    const keyPatterns = new Set();
                    keys.forEach(key => {
                        // Extract patterns from keys
                        if (key.includes('_')) {
                            const parts = key.split('_');
                            keyPatterns.add(parts[0] + '_*');
                        }
                        if (key.match(/^\d+$/)) {
                            keyPatterns.add('NUMERIC_ID');
                        }
                        if (key.match(/^[A-F0-9]{8}$/i)) {
                            keyPatterns.add('HEX_ID');
                        }
                    });
                    
                    keyPatterns.forEach(pattern => {
                        console.log(`        - ${pattern}`);
                    });
                    
                    console.log(`      Value characteristics:`);
                    const shortValues = values.filter(v => v.length <= 50);
                    const longValues = values.filter(v => v.length > 50);
                    const englishValues = values.filter(v => /^[a-zA-Z0-9\s.,!?'"()-]+$/.test(v));
                    
                    console.log(`        - Short values (≤50 chars): ${shortValues.length}`);
                    console.log(`        - Long values (>50 chars): ${longValues.length}`);
                    console.log(`        - English-like values: ${englishValues.length}`);
                    
                    // Look for potential mod metadata
                    console.log(`      Potential metadata candidates:`);
                    let foundCandidates = false;
                    
                    for (const [key, value] of stringTable.entries) {
                        // Look for values that could be mod names or descriptions
                        if (value.length >= 5 && 
                            value.length <= 200 &&
                            !/^\d+$/.test(value) && // Not just numbers
                            !/^[A-F0-9]{8,}$/i.test(value) && // Not hex codes
                            /[a-zA-Z]/.test(value) && // Contains letters
                            !value.includes('\n') && // Single line
                            (value.includes(' ') || value.length > 20) // Either has spaces or is long enough to be meaningful
                        ) {
                            const truncatedValue = value.length > 100 ? 
                                value.substring(0, 100) + '...' : value;
                            console.log(`        "${key}" = "${truncatedValue}"`);
                            foundCandidates = true;
                        }
                    }
                    
                    if (!foundCandidates) {
                        console.log(`        (No obvious metadata candidates found)`);
                    }
                }
                
            } catch (error) {
                console.log(`   ❌ S4TK parsing failed: ${error.message}`);
            }
            
            console.log('');
        }
        
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
    }
}

// Run the examination
if (require.main === module) {
    examineWorkingStringTable().catch(console.error);
}

export { examineWorkingStringTable };
