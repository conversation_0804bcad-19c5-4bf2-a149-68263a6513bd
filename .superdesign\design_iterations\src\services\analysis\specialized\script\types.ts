/**
 * Script Analysis Types and Enums
 * 
 * This file contains all type definitions, interfaces, and enums
 * related to Script (.ts4script) analysis and categorization.
 * 
 * Extracted from ScriptAnalyzer.ts as part of Phase 2 refactoring
 * to follow the Single Responsibility Principle.
 */

/**
 * Detailed script mod information extracted from .ts4script files
 */
export interface ScriptModInfo {
    category: ScriptModCategory;
    subcategory: string;
    author?: string;
    version?: string;
    pythonFiles: string[];
    dependencies: string[];
    conflicts: string[];
    gameplayAreas: GameplayArea[];
    isFramework: boolean;
    isLibrary: boolean;
    description: string;
}

/**
 * Script mod categories
 */
export enum ScriptModCategory {
    GAMEPLAY = 'gameplay',
    FRAMEWORK = 'framework',
    LIBRARY = 'library',
    UTILITY = 'utility',
    CHEAT = 'cheat',
    UNKNOWN = 'unknown'
}

/**
 * Gameplay areas affected by script mods
 */
export enum GameplayArea {
    CAREERS = 'careers',
    RELATIONSHIPS = 'relationships',
    FAMILY = 'family',
    PREGNANCY = 'pregnancy',
    SKILLS = 'skills',
    TRAITS = 'traits',
    ASPIRATIONS = 'aspirations',
    EMOTIONS = 'emotions',
    INTERACTIONS = 'interactions',
    EVENTS = 'events',
    AUTONOMY = 'autonomy',
    UI = 'ui',
    OBJECTS = 'objects',
    UNKNOWN = 'unknown'
}

/**
 * Display names mapping for script mod categories
 */
export const SCRIPT_CATEGORY_DISPLAY_NAMES: Record<ScriptModCategory, string> = {
    [ScriptModCategory.GAMEPLAY]: 'Gameplay Mod',
    [ScriptModCategory.FRAMEWORK]: 'Framework Mod',
    [ScriptModCategory.LIBRARY]: 'Library Mod',
    [ScriptModCategory.UTILITY]: 'Utility Mod',
    [ScriptModCategory.CHEAT]: 'Cheat Mod',
    [ScriptModCategory.UNKNOWN]: 'Script Mod'
};

/**
 * Framework detection patterns
 */
export const FRAMEWORK_PATTERNS = [
    'mc_',
    'mccommand',
    'xmlinjector',
    'ui_cheats'
] as const;

/**
 * Library detection patterns
 */
export const LIBRARY_PATTERNS = [
    'library',
    'lib',
    'core',
    'base'
] as const;

/**
 * Cheat mod detection patterns
 */
export const CHEAT_PATTERNS = [
    'cheat',
    'hack',
    'money',
    'free'
] as const;

/**
 * Utility mod detection patterns
 */
export const UTILITY_PATTERNS = [
    'util',
    'tool',
    'helper',
    'fix'
] as const;

/**
 * Gameplay area detection patterns
 */
export const GAMEPLAY_AREA_PATTERNS: Record<GameplayArea, RegExp[]> = {
    [GameplayArea.CAREERS]: [/career/i, /job/i, /profession/i, /work/i],
    [GameplayArea.RELATIONSHIPS]: [/relationship/i, /romance/i, /love/i, /friend/i],
    [GameplayArea.FAMILY]: [/family/i, /household/i, /parent/i, /child/i],
    [GameplayArea.PREGNANCY]: [/pregnan/i, /birth/i, /baby/i, /trimester/i],
    [GameplayArea.SKILLS]: [/skill/i, /ability/i, /talent/i],
    [GameplayArea.TRAITS]: [/trait/i, /personality/i, /character/i],
    [GameplayArea.ASPIRATIONS]: [/aspiration/i, /goal/i, /wish/i],
    [GameplayArea.EMOTIONS]: [/emotion/i, /mood/i, /feeling/i, /buff/i],
    [GameplayArea.INTERACTIONS]: [/interaction/i, /social/i, /dialog/i],
    [GameplayArea.EVENTS]: [/event/i, /party/i, /holiday/i, /festival/i],
    [GameplayArea.AUTONOMY]: [/autonom/i, /behavior/i, /ai/i],
    [GameplayArea.UI]: [/ui/i, /interface/i, /menu/i, /dialog/i, /window/i],
    [GameplayArea.OBJECTS]: [/object/i, /furniture/i, /item/i],
    [GameplayArea.UNKNOWN]: []
};

/**
 * Framework type mapping
 */
export const FRAMEWORK_TYPE_MAPPING: Record<string, string> = {
    'mc_': 'mc_command_center',
    'mccommand': 'mc_command_center',
    'xmlinjector': 'xml_injector',
    'ui_cheats': 'ui_cheats'
};

/**
 * Default script mod info for when analysis fails
 */
export const DEFAULT_SCRIPT_MOD_INFO: ScriptModInfo = {
    category: ScriptModCategory.UNKNOWN,
    subcategory: 'unknown',
    pythonFiles: [],
    dependencies: [],
    conflicts: [],
    gameplayAreas: [GameplayArea.UNKNOWN],
    isFramework: false,
    isLibrary: false,
    description: 'Unknown script mod'
};

/**
 * ZIP file signature constants
 */
export const ZIP_SIGNATURE = {
    HEADER: [0x50, 0x4B] // 'PK'
} as const;

/**
 * Common Python import patterns to detect dependencies
 */
export const PYTHON_IMPORT_REGEX = /^(?:import|from)\s+(\w+)/gm;

/**
 * Enhanced metadata extraction patterns
 * Based on comprehensive research of Sims 4 modding community practices
 */
export const METADATA_PATTERNS = {
    AUTHOR: [
        // Python standard patterns
        /__author__\s*=\s*['"](.+?)['"]/,
        /author\s*=\s*['"](.+?)['"]/,

        // Comment patterns
        /# Author:\s*(.+?)$/m,
        /# Created by:\s*(.+?)$/m,
        /# Mod by:\s*(.+?)$/m,
        /# Made by:\s*(.+?)$/m,

        // Docstring patterns
        /Author:\s*(.+?)$/m,
        /Created by:\s*(.+?)$/m,
        /Mod Creator:\s*(.+?)$/m,
        /Creator:\s*(.+?)$/m,

        // Multi-line docstring patterns
        /"""[\s\S]*?Author:\s*(.+?)$/m,
        /'''[\s\S]*?Author:\s*(.+?)$/m,
        /"""[\s\S]*?Created by:\s*(.+?)$/m,
        /'''[\s\S]*?Created by:\s*(.+?)$/m
    ],
    VERSION: [
        // Python standard patterns
        /__version__\s*=\s*['"](.+?)['"]/,
        /version\s*=\s*['"](.+?)['"]/,

        // Comment patterns
        /# Version:\s*(.+?)$/m,
        /# v(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/m,
        /# Ver:\s*(.+?)$/m,

        // Docstring patterns
        /Version:\s*(.+?)$/m,
        /v(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/m,
        /Ver:\s*(.+?)$/m,

        // Multi-line docstring patterns
        /"""[\s\S]*?Version:\s*(.+?)$/m,
        /'''[\s\S]*?Version:\s*(.+?)$/m
    ],
    DESCRIPTION: [
        // Module docstrings
        /^"""([\s\S]*?)"""/m,
        /^'''([\s\S]*?)'''/m,

        // Comment blocks
        /^#\s*Description:\s*(.+?)(?:\n|$)/m,
        /^#\s*(.{20,200})(?:\n|$)/m
    ],
    MOD_NAME: [
        // Explicit mod name declarations
        /__mod_name__\s*=\s*['"](.+?)['"]/,
        /mod_name\s*=\s*['"](.+?)['"]/,
        /# Mod Name:\s*(.+?)$/m,
        /"""[\s\S]*?Mod Name:\s*(.+?)$/m
    ]
} as const;
