import type { AnalyzedPackage } from '../../../types/analysis';
import type { OrganizationPlan, OrganizationConflict } from './types';
import { PathGenerator } from './PathGenerator';
import * as path from 'path';

/**
 * Specialized component for detecting organization conflicts
 * Identifies potential issues when organizing mods into folders
 */
export class ConflictDetector {
    
    /**
     * Detects potential conflicts in organization plan
     */
    public static detectOrganizationConflicts(
        plan: OrganizationPlan, 
        analyses: AnalyzedPackage[],
        preferences: any
    ): void {
        // Clear existing conflicts
        plan.conflicts = [];

        // Check for dependency separation conflicts
        this.detectDependencySeparationConflicts(plan, analyses, preferences);

        // Check for folder conflicts
        this.detectFolderConflicts(plan, analyses);

        // Check for script mod conflicts
        this.detectScriptModConflicts(plan, analyses);

        // Check for override conflicts
        this.detectOverrideConflicts(plan, analyses);
    }

    /**
     * Detects files with dependencies that might be separated
     */
    private static detectDependencySeparationConflicts(
        plan: OrganizationPlan, 
        analyses: AnalyzedPackage[],
        preferences: any
    ): void {
        analyses.forEach(analysis => {
            if (analysis.dependencies.length > 0) {
                const dependencyFiles = analyses.filter(other => 
                    analysis.dependencies.some(dep => 
                        other.filePath.toLowerCase().includes(dep.toLowerCase())
                    )
                );

                if (dependencyFiles.length > 0) {
                    const analysisPath = PathGenerator.generateSuggestedPath(analysis, preferences);
                    const dependencyPaths = dependencyFiles.map(f => 
                        PathGenerator.generateSuggestedPath(f, preferences)
                    );
                    
                    if (dependencyPaths.some(depPath => depPath !== analysisPath)) {
                        plan.conflicts.push({
                            type: 'dependency_separation',
                            description: `${path.basename(analysis.filePath)} depends on files that will be in different folders`,
                            affectedFiles: [analysis.filePath, ...dependencyFiles.map(f => f.filePath)],
                            severity: 'warning'
                        });
                    }
                }
            }
        });
    }

    /**
     * Detects potential conflicts between mods in the same folder
     */
    private static detectFolderConflicts(plan: OrganizationPlan, analyses: AnalyzedPackage[]): void {
        plan.suggestions.forEach(suggestion => {
            if (suggestion.files.length > 1) {
                const filesInFolder = analyses.filter(a => suggestion.files.includes(a.filePath));
                const conflictingFiles = filesInFolder.filter(file => file.conflicts.length > 0);
                
                if (conflictingFiles.length > 1) {
                    plan.conflicts.push({
                        type: 'folder_conflicts',
                        description: `Multiple conflicting mods will be placed in ${suggestion.targetPath}`,
                        affectedFiles: conflictingFiles.map(f => f.filePath),
                        severity: 'error'
                    });
                }
            }
        });
    }

    /**
     * Detects script mod specific conflicts
     */
    private static detectScriptModConflicts(plan: OrganizationPlan, analyses: AnalyzedPackage[]): void {
        const scriptMods = analyses.filter(a => a.filePath.toLowerCase().endsWith('.ts4script'));
        
        // Check for multiple UI Cheat mods
        const uiCheatMods = scriptMods.filter(mod => 
            mod.filePath.toLowerCase().includes('ui_cheat') || 
            mod.filePath.toLowerCase().includes('uicheat')
        );

        if (uiCheatMods.length > 1) {
            plan.conflicts.push({
                type: 'folder_conflicts',
                description: 'Multiple UI Cheat Extension mods detected - only one should be used',
                affectedFiles: uiCheatMods.map(f => f.filePath),
                severity: 'error'
            });
        }

        // Check for multiple MCCC versions
        const mcccMods = scriptMods.filter(mod => 
            mod.filePath.toLowerCase().includes('mc_') || 
            mod.filePath.toLowerCase().includes('mccommand')
        );

        if (mcccMods.length > 1) {
            plan.conflicts.push({
                type: 'folder_conflicts',
                description: 'Multiple MC Command Center mods detected - only one version should be used',
                affectedFiles: mcccMods.map(f => f.filePath),
                severity: 'error'
            });
        }
    }

    /**
     * Detects override mod conflicts
     */
    private static detectOverrideConflicts(plan: OrganizationPlan, analyses: AnalyzedPackage[]): void {
        const overrideMods = analyses.filter(a => a.isOverride);
        
        if (overrideMods.length > 1) {
            // Group overrides by what they might be overriding
            const overrideGroups = new Map<string, AnalyzedPackage[]>();
            
            overrideMods.forEach(mod => {
                const fileName = path.basename(mod.filePath).toLowerCase();
                
                // Try to identify what's being overridden
                let overrideTarget = 'general';
                
                if (fileName.includes('basegame')) overrideTarget = 'basegame';
                else if (fileName.includes('get_together')) overrideTarget = 'get_together';
                else if (fileName.includes('city_living')) overrideTarget = 'city_living';
                else if (fileName.includes('cats_dogs')) overrideTarget = 'cats_dogs';
                else if (fileName.includes('seasons')) overrideTarget = 'seasons';
                
                if (!overrideGroups.has(overrideTarget)) {
                    overrideGroups.set(overrideTarget, []);
                }
                overrideGroups.get(overrideTarget)!.push(mod);
            });

            // Check for conflicts within groups
            overrideGroups.forEach((mods, target) => {
                if (mods.length > 1) {
                    plan.conflicts.push({
                        type: 'folder_conflicts',
                        description: `Multiple override mods targeting ${target} detected - may conflict`,
                        affectedFiles: mods.map(f => f.filePath),
                        severity: 'warning'
                    });
                }
            });
        }
    }

    /**
     * Validates organization plan for common issues
     */
    public static validateOrganizationPlan(plan: OrganizationPlan): {
        isValid: boolean;
        issues: string[];
        warnings: string[];
    } {
        const issues: string[] = [];
        const warnings: string[] = [];

        // Check for empty suggestions
        if (plan.suggestions.length === 0) {
            issues.push('No organization suggestions generated');
        }

        // Check for too many conflicts
        const errorConflicts = plan.conflicts.filter(c => c.severity === 'error');
        if (errorConflicts.length > plan.totalFiles * 0.1) {
            warnings.push('High number of organization conflicts detected');
        }

        // Check for unbalanced organization
        const largestCategory = Math.max(...Array.from(plan.categories.values()).map(c => c.count));
        if (largestCategory > plan.totalFiles * 0.8) {
            warnings.push('Most files are in a single category - consider reviewing categorization');
        }

        return {
            isValid: issues.length === 0,
            issues,
            warnings
        };
    }
}