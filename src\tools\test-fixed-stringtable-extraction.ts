/**
 * Test the fixed StringTable extraction that works with parsed S4TK objects
 */

import * as fs from 'fs';
import * as path from 'path';
import { EnhancedMetadataExtractionService } from '../services/analysis/metadata/EnhancedMetadataExtractionService';

async function testFixedStringTableExtraction(): Promise<void> {
    console.log('🔧 Testing Fixed StringTable Extraction');
    console.log('=' .repeat(50));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    const fileName = '!LittleMsSam_SimDaDatingApp_NoFWord.package'; // Known to have StringTable resources
    const filePath = path.join(modsPath, fileName);
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ Test file not found');
        return;
    }
    
    try {
        console.log(`🔍 Testing: ${fileName}`);
        console.log('');
        
        const buffer = fs.readFileSync(filePath);
        const metadataService = new EnhancedMetadataExtractionService();
        
        // Test with all extractors enabled
        const result = await metadataService.extractMetadata(
            buffer,
            fileName,
            {
                enabledExtractors: {
                    filename: true,
                    stringTable: true,
                    tuning: true,
                    simData: true,
                    manifest: true
                }
            }
        );
        
        console.log('📊 Extraction Results:');
        console.log(`   Overall Confidence: ${result.metadata.overallConfidence}%`);
        console.log(`   Quality Score: ${result.metadata.qualityScore}%`);
        console.log(`   Source Count: ${result.metadata.sourceCount}`);
        console.log(`   Primary Source: ${result.metadata.primarySource}`);
        console.log(`   Source Types: ${result.metadata.sourceTypes.join(', ')}`);
        console.log('');
        
        console.log('📝 Extracted Metadata:');
        console.log(`   Author: ${result.metadata.author || 'None'}`);
        console.log(`   Mod Name: ${result.metadata.modName || 'None'}`);
        console.log(`   Version: ${result.metadata.version || 'None'}`);
        console.log(`   Description: ${result.metadata.description || 'None'}`);
        console.log('');
        
        console.log('🔍 Extraction Details:');
        console.log(`   Filename: ${result.extractionDetails.filename.attempted ? '✅' : '❌'} attempted, ${result.extractionDetails.filename.successful ? '✅' : '❌'} successful`);
        if (result.extractionDetails.filename.error) {
            console.log(`      Error: ${result.extractionDetails.filename.error}`);
        }
        
        console.log(`   StringTable: ${result.extractionDetails.stringTable.attempted ? '✅' : '❌'} attempted, ${result.extractionDetails.stringTable.successful ? '✅' : '❌'} successful`);
        console.log(`      Resources: ${result.extractionDetails.stringTable.resourceCount}`);
        if (result.extractionDetails.stringTable.error) {
            console.log(`      Error: ${result.extractionDetails.stringTable.error}`);
        }
        
        console.log(`   Tuning: ${result.extractionDetails.tuning.attempted ? '✅' : '❌'} attempted, ${result.extractionDetails.tuning.successful ? '✅' : '❌'} successful`);
        console.log(`      Resources: ${result.extractionDetails.tuning.resourceCount}`);
        if (result.extractionDetails.tuning.error) {
            console.log(`      Error: ${result.extractionDetails.tuning.error}`);
        }
        
        console.log(`   SimData: ${result.extractionDetails.simData.attempted ? '✅' : '❌'} attempted, ${result.extractionDetails.simData.successful ? '✅' : '❌'} successful`);
        console.log(`      Resources: ${result.extractionDetails.simData.resourceCount}`);
        if (result.extractionDetails.simData.error) {
            console.log(`      Error: ${result.extractionDetails.simData.error}`);
        }
        
        console.log(`   Manifest: ${result.extractionDetails.manifest.attempted ? '✅' : '❌'} attempted, ${result.extractionDetails.manifest.successful ? '✅' : '❌'} successful`);
        console.log(`      Manifests: ${result.extractionDetails.manifest.manifestCount}`);
        if (result.extractionDetails.manifest.error) {
            console.log(`      Error: ${result.extractionDetails.manifest.error}`);
        }
        console.log('');
        
        console.log('⚡ Performance:');
        console.log(`   Total Time: ${result.performance.totalTime.toFixed(2)}ms`);
        console.log(`   Filename: ${result.performance.extractorTimes.filename?.toFixed(2) || 0}ms`);
        console.log(`   StringTable: ${result.performance.extractorTimes.stringTable?.toFixed(2) || 0}ms`);
        console.log(`   Tuning: ${result.performance.extractorTimes.tuning?.toFixed(2) || 0}ms`);
        console.log(`   SimData: ${result.performance.extractorTimes.simData?.toFixed(2) || 0}ms`);
        console.log(`   Manifest: ${result.performance.extractorTimes.manifest?.toFixed(2) || 0}ms`);
        console.log(`   Aggregation: ${result.performance.aggregationTime.toFixed(2)}ms`);
        console.log('');
        
        // Show individual sources
        if (result.metadata.sources.length > 0) {
            console.log('🔗 Individual Sources:');
            result.metadata.sources.forEach((source, index) => {
                console.log(`   ${index + 1}. ${source.source} (${source.confidence}% confidence):`);
                if (source.author) console.log(`      Author: "${source.author}"`);
                if (source.modName) console.log(`      Mod Name: "${source.modName}"`);
                if (source.version) console.log(`      Version: "${source.version}"`);
                if (source.description) console.log(`      Description: "${source.description.substring(0, 100)}${source.description.length > 100 ? '...' : ''}"`);
                console.log(`      Processing Time: ${source.processingTime.toFixed(2)}ms`);
                console.log('');
            });
        }
        
        // Test success criteria
        console.log('🎯 Success Analysis:');
        const hasStringTableData = result.extractionDetails.stringTable.successful && result.extractionDetails.stringTable.resourceCount > 0;
        const hasMetadataFromStringTable = result.metadata.sources.some(s => s.source === 'stringtable');
        const hasAnyMetadata = result.metadata.author || result.metadata.modName || result.metadata.version || result.metadata.description;
        
        console.log(`   StringTable Resources Found: ${hasStringTableData ? '✅' : '❌'}`);
        console.log(`   StringTable Metadata Extracted: ${hasMetadataFromStringTable ? '✅' : '❌'}`);
        console.log(`   Any Metadata Found: ${hasAnyMetadata ? '✅' : '❌'}`);
        
        if (hasStringTableData && !hasMetadataFromStringTable) {
            console.log('   ⚠️  StringTable resources found but no metadata extracted - extraction logic needs improvement');
        }
        
        if (hasMetadataFromStringTable) {
            console.log('   🎉 SUCCESS: StringTable metadata extraction is working!');
        } else {
            console.log('   ❌ FAILURE: StringTable metadata extraction is not working');
        }
        
    } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
        console.log(`Stack: ${error.stack}`);
    }
}

// Run the test
if (require.main === module) {
    testFixedStringTableExtraction().catch(console.error);
}

export { testFixedStringTableExtraction };
