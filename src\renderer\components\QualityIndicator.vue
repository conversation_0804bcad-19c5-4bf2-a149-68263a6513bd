<template>
  <div class="quality-indicator" :class="qualityClass">
    <div class="quality-indicator__bar">
      <div 
        class="quality-indicator__fill" 
        :style="{ width: `${percentage}%` }"
      ></div>
    </div>
    <span class="quality-indicator__label">{{ qualityLabel }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
  quality: string;
  showLabel?: boolean;
}>();

const qualityMap: Record<string, { label: string; percentage: number; class: string }> = {
  'excellent': { label: 'Excellent', percentage: 95, class: 'excellent' },
  'very-good': { label: 'Very Good', percentage: 85, class: 'very-good' },
  'good': { label: 'Good', percentage: 75, class: 'good' },
  'fair': { label: 'Fair', percentage: 60, class: 'fair' },
  'poor': { label: 'Poor', percentage: 40, class: 'poor' },
  'very-poor': { label: 'Very Poor', percentage: 20, class: 'very-poor' },
  'unknown': { label: 'Unknown', percentage: 0, class: 'unknown' }
};

const qualityData = computed(() => {
  return qualityMap[props.quality] || qualityMap.unknown;
});

const qualityClass = computed(() => `quality-indicator--${qualityData.value.class}`);
const qualityLabel = computed(() => qualityData.value.label);
const percentage = computed(() => qualityData.value.percentage);
</script>

<style scoped>
.quality-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.quality-indicator__bar {
  width: 40px;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.quality-indicator__fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--duration-300) var(--ease-out);
}

.quality-indicator__label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  white-space: nowrap;
}

/* Quality level colors */
.quality-indicator--excellent .quality-indicator__fill {
  background: var(--success);
}

.quality-indicator--very-good .quality-indicator__fill {
  background: var(--plumbob-green);
}

.quality-indicator--good .quality-indicator__fill {
  background: var(--info);
}

.quality-indicator--fair .quality-indicator__fill {
  background: var(--sims-orange);
}

.quality-indicator--poor .quality-indicator__fill {
  background: var(--warning);
}

.quality-indicator--very-poor .quality-indicator__fill {
  background: var(--error);
}

.quality-indicator--unknown .quality-indicator__fill {
  background: var(--gray-400);
}
</style>
