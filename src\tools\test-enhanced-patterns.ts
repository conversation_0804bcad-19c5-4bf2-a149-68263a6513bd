#!/usr/bin/env node

/**
 * Test Enhanced Patterns
 * 
 * Tests the new enhanced pattern recognition directly
 */

import { FilenameMetadataExtractor } from '../services/analysis/specialized/common/FilenameMetadataExtractor';

const testFiles = [
    'TwistedCat_Lashes_NO3_RingsConflict.package',
    'TwistedCat_Liquorice_Lipstick.package',
    'TwistedCat_Lux_Eyebrows.package',
    'Zero_VanillaPlus_EngagementRings.package',
    'UnderStairsPlatform.package',
    'UnderStairsShelf_Medium.package',
    'UnderstairsShelf_Short.package',
    'tyjokr_SocialVariety_InteractionCooldowns.package'
];

console.log('🔍 TESTING ENHANCED PATTERNS DIRECTLY\n');

for (const filename of testFiles) {
    console.log(`Testing: ${filename}`);
    const result = FilenameMetadataExtractor.extractFromFilename(filename);
    
    console.log(`   Author: ${result.author || 'None'}`);
    console.log(`   Mod Name: ${result.modName || 'None'}`);
    console.log(`   Confidence: ${result.confidence}%`);
    console.log(`   Pattern: ${result.pattern || 'None'}`);
    console.log();
}
