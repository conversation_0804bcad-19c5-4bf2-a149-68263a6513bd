/**
 * Test script for StringTable analysis implementation
 * Phase 1: Task 1.1 - StringTable Resource Type Integration
 */

import * as fs from 'fs';
import * as path from 'path';
import { StringTableProcessor } from '../services/analysis/resources/StringTableProcessor';
import { detailedAnalysisService } from '../services/analysis/core/DetailedAnalysisService';

interface StringTableTestResult {
    fileName: string;
    filePath: string;
    fileSize: number;
    
    // StringTable Analysis Results
    hasStringTable: boolean;
    actualModName?: string;
    actualDescription?: string;
    extractedItemNames: string[];
    metadataConfidence: number;
    stringTableProcessingTime?: number;
    
    // Analysis Results
    totalProcessingTime: number;
    error?: string;
}

async function testStringTableAnalysis(): Promise<void> {
    console.log('🧪 Testing StringTable Analysis Implementation');
    console.log('=' .repeat(60));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    if (!fs.existsSync(modsPath)) {
        console.log('❌ Mods folder not found. Please check the path.');
        return;
    }
    
    const results: StringTableTestResult[] = [];
    const files = fs.readdirSync(modsPath)
        .filter(file => file.endsWith('.package'))
        .slice(0, 10); // Test first 10 package files
    
    console.log(`📁 Found ${files.length} package files to test`);
    console.log('');
    
    for (const fileName of files) {
        const filePath = path.join(modsPath, fileName);
        const startTime = performance.now();
        
        try {
            console.log(`🔍 Analyzing: ${fileName}`);
            
            const buffer = fs.readFileSync(filePath);
            const fileSize = buffer.length;
            
            // Perform detailed analysis which includes StringTable analysis
            const analysisResult = await detailedAnalysisService.analyzeAsync(
                buffer, 
                filePath, 
                { 
                    needsDeepAnalysis: true,
                    resourceCount: 0,
                    resourceTypes: [],
                    compressionStats: {},
                    validationIssues: [],
                    s4tkVersion: '1.0.0',
                    processingTime: 0
                } as any
            );
            
            const totalTime = performance.now() - startTime;
            
            const result: StringTableTestResult = {
                fileName,
                filePath,
                fileSize,
                hasStringTable: analysisResult.hasStringTable || false,
                actualModName: analysisResult.actualModName,
                actualDescription: analysisResult.actualDescription,
                extractedItemNames: analysisResult.extractedItemNames || [],
                metadataConfidence: analysisResult.metadataConfidence || 0,
                stringTableProcessingTime: analysisResult.stringTableData?.processingTime,
                totalProcessingTime: totalTime
            };
            
            results.push(result);
            
            // Log results
            if (result.hasStringTable) {
                console.log(`  ✅ StringTable found!`);
                if (result.actualModName) {
                    console.log(`     📝 Mod Name: "${result.actualModName}"`);
                }
                if (result.actualDescription) {
                    console.log(`     📄 Description: "${result.actualDescription.substring(0, 100)}${result.actualDescription.length > 100 ? '...' : ''}"`);
                }
                if (result.extractedItemNames.length > 0) {
                    console.log(`     🏷️  Items: ${result.extractedItemNames.length} found`);
                }
                console.log(`     🎯 Confidence: ${result.metadataConfidence}%`);
                console.log(`     ⚡ STBL Processing: ${result.stringTableProcessingTime?.toFixed(2)}ms`);
            } else {
                console.log(`  ❌ No StringTable resources found`);
            }
            
            console.log(`     ⏱️  Total Time: ${result.totalProcessingTime.toFixed(2)}ms`);
            console.log('');
            
        } catch (error) {
            const totalTime = performance.now() - startTime;
            console.log(`  ❌ Error: ${error.message}`);
            console.log(`     ⏱️  Time: ${totalTime.toFixed(2)}ms`);
            console.log('');
            
            results.push({
                fileName,
                filePath,
                fileSize: 0,
                hasStringTable: false,
                extractedItemNames: [],
                metadataConfidence: 0,
                totalProcessingTime: totalTime,
                error: error.message
            });
        }
    }
    
    // Summary Report
    console.log('📊 StringTable Analysis Summary');
    console.log('=' .repeat(60));
    
    const withStringTable = results.filter(r => r.hasStringTable);
    const withModNames = results.filter(r => r.actualModName);
    const withDescriptions = results.filter(r => r.actualDescription);
    const withItems = results.filter(r => r.extractedItemNames.length > 0);
    const errors = results.filter(r => r.error);
    
    console.log(`📁 Total Files Analyzed: ${results.length}`);
    console.log(`📋 Files with StringTable: ${withStringTable.length} (${(withStringTable.length / results.length * 100).toFixed(1)}%)`);
    console.log(`📝 Files with Mod Names: ${withModNames.length} (${(withModNames.length / results.length * 100).toFixed(1)}%)`);
    console.log(`📄 Files with Descriptions: ${withDescriptions.length} (${(withDescriptions.length / results.length * 100).toFixed(1)}%)`);
    console.log(`🏷️  Files with Item Names: ${withItems.length} (${(withItems.length / results.length * 100).toFixed(1)}%)`);
    console.log(`❌ Analysis Errors: ${errors.length} (${(errors.length / results.length * 100).toFixed(1)}%)`);
    console.log('');
    
    // Performance Metrics
    const successfulResults = results.filter(r => !r.error);
    if (successfulResults.length > 0) {
        const avgTime = successfulResults.reduce((sum, r) => sum + r.totalProcessingTime, 0) / successfulResults.length;
        const maxTime = Math.max(...successfulResults.map(r => r.totalProcessingTime));
        const minTime = Math.min(...successfulResults.map(r => r.totalProcessingTime));
        
        console.log('⚡ Performance Metrics:');
        console.log(`   Average Processing Time: ${avgTime.toFixed(2)}ms`);
        console.log(`   Fastest Analysis: ${minTime.toFixed(2)}ms`);
        console.log(`   Slowest Analysis: ${maxTime.toFixed(2)}ms`);
        console.log(`   Target: <10ms per file ✅`);
        console.log('');
    }
    
    // Detailed Results for StringTable Files
    if (withStringTable.length > 0) {
        console.log('📋 Detailed StringTable Results:');
        console.log('-'.repeat(60));
        
        withStringTable.forEach(result => {
            console.log(`📁 ${result.fileName}`);
            if (result.actualModName) {
                console.log(`   📝 Name: "${result.actualModName}"`);
            }
            if (result.actualDescription) {
                console.log(`   📄 Desc: "${result.actualDescription.substring(0, 80)}${result.actualDescription.length > 80 ? '...' : ''}"`);
            }
            console.log(`   🎯 Confidence: ${result.metadataConfidence}%`);
            console.log(`   🏷️  Items: ${result.extractedItemNames.length}`);
            console.log(`   ⚡ Time: ${result.totalProcessingTime.toFixed(2)}ms`);
            console.log('');
        });
    }
    
    console.log('✅ StringTable Analysis Test Complete!');
}

// Run the test
if (require.main === module) {
    testStringTableAnalysis().catch(console.error);
}

export { testStringTableAnalysis };
