/**
 * Test script for analyzing real Sims 4 mod files
 * This script demonstrates our robust resource content analysis system
 */

const fs = require('fs');
const path = require('path');

// Import our analysis service
const { PackageAnalysisService } = require('./src/services/analysis/PackageAnalysisService');

async function testRealModFiles() {
    console.log('🔍 Testing Robust Resource Content Analysis System\n');
    
    // Use existing test assets
    const testDir = './tests/assets';
    if (!fs.existsSync(testDir)) {
        console.log('❌ Test assets directory not found.');
        return;
    }
    
    // Find mod files in test assets directory
    const modFiles = fs.readdirSync(testDir)
        .filter(file => file.endsWith('.package') || file.endsWith('.ts4script'))
        .map(file => path.join(testDir, file));
    
    if (modFiles.length === 0) {
        console.log('❌ No mod files found in test assets directory.');
        return;
    }
    
    console.log(`📦 Found ${modFiles.length} mod file(s) to analyze:\n`);
    
    const analysisService = new PackageAnalysisService();
    
    for (const filePath of modFiles) {
        try {
            console.log(`🔍 Analyzing: ${path.basename(filePath)}`);
            console.log(`📍 Path: ${filePath}`);
            
            // Read file buffer
            const buffer = fs.readFileSync(filePath);
            console.log(`📊 File size: ${formatFileSize(buffer.length)}`);
            
            // Analyze the file
            const result = analysisService.analyzePackage(buffer, filePath);
            
            // Display results
            console.log('\n📋 ANALYSIS RESULTS:');
            console.log(`   File Type: ${result.fileType}`);
            console.log(`   Category: ${result.category}`);
            console.log(`   Subcategory: ${result.subcategory || 'None'}`);
            console.log(`   Is Override: ${result.isOverride ? 'Yes' : 'No'}`);
            console.log(`   Resources: ${result.resources.length}`);
            
            if (result.suggestedPath) {
                console.log(`   Suggested Path: ${result.suggestedPath}`);
            }
            
            if (result.dependencies.length > 0) {
                console.log(`   Dependencies: ${result.dependencies.join(', ')}`);
            }
            
            if (result.conflicts.length > 0) {
                console.log(`   Conflicts: ${result.conflicts.join(', ')}`);
            }
            
            // Display deep analysis if available
            if (result.metadata?.deepAnalysis) {
                const deepAnalysis = result.metadata.deepAnalysis;
                console.log('\n🔬 DEEP ANALYSIS:');
                console.log(`   Primary Category: ${deepAnalysis.primaryCategory}`);
                console.log(`   Specific Category: ${deepAnalysis.specificCategory}`);
                console.log(`   Description: ${deepAnalysis.detailedDescription}`);
                
                if (deepAnalysis.casInfo) {
                    console.log('\n👤 CAS DETAILS:');
                    console.log(`   Category: ${deepAnalysis.casInfo.category}`);
                    console.log(`   Subcategory: ${deepAnalysis.casInfo.subcategory}`);
                    if (deepAnalysis.casInfo.genders.length > 0) {
                        console.log(`   Genders: ${deepAnalysis.casInfo.genders.join(', ')}`);
                    }
                    if (deepAnalysis.casInfo.ageGroups.length > 0) {
                        console.log(`   Age Groups: ${deepAnalysis.casInfo.ageGroups.join(', ')}`);
                    }
                }
                
                if (deepAnalysis.objectInfo) {
                    console.log('\n🏠 OBJECT DETAILS:');
                    console.log(`   Category: ${deepAnalysis.objectInfo.category}`);
                    console.log(`   Function: ${deepAnalysis.objectInfo.function}`);
                    if (deepAnalysis.objectInfo.roomAssignment.length > 0) {
                        console.log(`   Room Assignment: ${deepAnalysis.objectInfo.roomAssignment.join(', ')}`);
                    }
                }
                
                if (deepAnalysis.scriptInfo) {
                    console.log('\n📜 SCRIPT DETAILS:');
                    console.log(`   Category: ${deepAnalysis.scriptInfo.category}`);
                    console.log(`   Is Framework: ${deepAnalysis.scriptInfo.isFramework ? 'Yes' : 'No'}`);
                    if (deepAnalysis.scriptInfo.gameplayAreas.length > 0) {
                        console.log(`   Gameplay Areas: ${deepAnalysis.scriptInfo.gameplayAreas.join(', ')}`);
                    }
                }
                
                if (deepAnalysis.suggestedTags && deepAnalysis.suggestedTags.length > 0) {
                    console.log(`\n🏷️  Suggested Tags: ${deepAnalysis.suggestedTags.join(', ')}`);
                }
            }
            
            // Display some resource details
            if (result.resources.length > 0) {
                console.log('\n📦 RESOURCE SUMMARY:');
                const resourceTypes = {};
                result.resources.forEach(resource => {
                    const type = resource.type.split(' ')[0]; // Get base type name
                    resourceTypes[type] = (resourceTypes[type] || 0) + 1;
                });
                
                Object.entries(resourceTypes)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 5) // Show top 5 resource types
                    .forEach(([type, count]) => {
                        console.log(`   ${type}: ${count}`);
                    });
                
                if (Object.keys(resourceTypes).length > 5) {
                    console.log(`   ... and ${Object.keys(resourceTypes).length - 5} more types`);
                }
            }
            
        } catch (error) {
            console.log(`❌ Error analyzing ${path.basename(filePath)}:`);
            console.log(`   ${error.message}`);
        }
        
        console.log('\n' + '='.repeat(80) + '\n');
    }
    
    console.log('✅ Analysis complete!');
    console.log('\n💡 Key Features Demonstrated:');
    console.log('   • Resource content analysis (not filename-based)');
    console.log('   • SimData parsing for CAS parts and objects');
    console.log('   • Detailed categorization and metadata extraction');
    console.log('   • Dependency and conflict detection');
    console.log('   • Organization suggestions');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Run the test
testRealModFiles().catch(console.error);