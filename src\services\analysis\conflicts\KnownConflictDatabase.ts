/**
 * Known Conflict Database
 *
 * Configuration-driven database of known problematic mod combinations,
 * version conflicts, and common issues reported by the Sims 4 modding community.
 *
 * Uses external configuration files instead of hardcoded values,
 * following proper coding principles.
 */

import { ModContentAnalysis } from '../content/ContentAnalysisService';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Known conflict between mods
 */
export interface KnownConflict {
    id: string;
    type: KnownConflictType;
    severity: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    description: string;
    affectedMods: string[];
    detectionPattern: ConflictPattern;
    resolution: string;
    autoFixAvailable: boolean;
    reportedBy: string[];
    lastUpdated: string;
}

/**
 * Types of known conflicts
 */
export enum KnownConflictType {
    VERSION_INCOMPATIBLE = 'version_incompatible',
    AUTHOR_CONFLICT = 'author_conflict',
    CATEGORY_CONFLICT = 'category_conflict',
    DEPENDENCY_CONFLICT = 'dependency_conflict',
    GAME_VERSION_CONFLICT = 'game_version_conflict'
}

/**
 * <PERSON><PERSON> for detecting conflicts
 */
export interface ConflictPattern {
    type: 'filename' | 'author' | 'content' | 'combination';
    patterns: string[];
    conditions: ConflictCondition[];
}

export interface ConflictCondition {
    field: string;
    operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'regex';
    value: string;
    caseSensitive?: boolean;
}

/**
 * Configuration-Driven Known Conflict Database
 */
export class KnownConflictDatabase {

    private static conflictRules: any = null;
    private static conflicts: KnownConflict[] = [];

    /**
     * Loads conflict rules from configuration file
     */
    private static loadConflictRules(): void {
        if (this.conflictRules) return;

        try {
            const configPath = path.join(__dirname, '../../../config/conflictRules.json');
            const configData = fs.readFileSync(configPath, 'utf8');
            this.conflictRules = JSON.parse(configData);

            // Convert configuration to conflict objects
            this.buildConflictsFromConfig();

        } catch (error) {
            console.warn('[KnownConflictDatabase] Could not load conflict rules, using minimal defaults');
            this.conflictRules = { knownConflicts: {} };
            this.conflicts = [];
        }
    }

    /**
     * Builds conflict objects from verified configuration
     */
    private static buildConflictsFromConfig(): void {
        this.conflicts = [];

        if (!this.conflictRules.verifiedConflicts) return;

        for (const [conflictId, config] of Object.entries(this.conflictRules.verifiedConflicts)) {
            const conflictConfig = config as any;

            this.conflicts.push({
                id: conflictId,
                type: this.mapConfigTypeToEnum(conflictConfig.type),
                severity: conflictConfig.severity,
                title: this.generateTitle(conflictId, conflictConfig),
                description: conflictConfig.description,
                affectedMods: [],
                detectionPattern: {
                    type: 'filename',
                    patterns: conflictConfig.patterns || [],
                    conditions: this.buildConditionsFromPatterns(conflictConfig.patterns || [])
                },
                resolution: conflictConfig.resolution,
                autoFixAvailable: conflictConfig.autoFix || false,
                reportedBy: [conflictConfig.source || 'verified'],
                lastUpdated: conflictConfig.lastUpdated || new Date().toISOString().split('T')[0],
                evidence: conflictConfig.evidence,
                verifiedBy: conflictConfig.verifiedBy
            });
        }
    }

    /**
     * Maps configuration type to enum
     */
    private static mapConfigTypeToEnum(configType: string): KnownConflictType {
        switch (configType) {
            case 'version_conflict':
            case 'version_compatibility':
                return KnownConflictType.VERSION_INCOMPATIBLE;
            case 'duplicate_functionality':
            case 'mutually_exclusive':
                return KnownConflictType.AUTHOR_CONFLICT;
            case 'gameplay_overlap':
            case 'comprehensive_overhaul':
                return KnownConflictType.CATEGORY_CONFLICT;
            case 'dependency_missing':
                return KnownConflictType.DEPENDENCY_CONFLICT;
            case 'version_outdated':
                return KnownConflictType.GAME_VERSION_CONFLICT;
            default:
                return KnownConflictType.CATEGORY_CONFLICT;
        }
    }

    /**
     * Generates title from conflict ID and config
     */
    private static generateTitle(conflictId: string, config: any): string {
        if (config.title) return config.title;

        // Generate title from ID
        return conflictId.replace(/([A-Z])/g, ' $1')
                        .replace(/^./, str => str.toUpperCase())
                        .replace('Conflicts', 'Conflict');
    }

    /**
     * Builds conditions from patterns
     */
    private static buildConditionsFromPatterns(patterns: string[]): ConflictCondition[] {
        return patterns.map(pattern => ({
            field: 'filename',
            operator: 'contains' as const,
            value: pattern,
            caseSensitive: false
        }));
    }

    
    /**
     * Checks mod collection against known conflict database
     */
    public static async checkKnownConflicts(
        modAnalyses: Map<string, ModContentAnalysis>
    ): Promise<KnownConflict[]> {
        // Load configuration if not already loaded
        this.loadConflictRules();

        const detectedConflicts: KnownConflict[] = [];
        const modFiles = Array.from(modAnalyses.keys());

        console.log(`[KnownConflictDatabase] Checking ${modFiles.length} mods against ${this.conflicts.length} known conflict patterns...`);

        for (const conflict of this.conflicts) {
            const matchingMods = this.findMatchingMods(modFiles, conflict.detectionPattern);

            if (this.isConflictDetected(matchingMods, conflict)) {
                const detectedConflict: KnownConflict = {
                    ...conflict,
                    affectedMods: matchingMods
                };
                detectedConflicts.push(detectedConflict);
            }
        }

        console.log(`[KnownConflictDatabase] Found ${detectedConflicts.length} known conflicts`);

        return detectedConflicts;
    }
    
    /**
     * Finds mods matching a conflict pattern
     */
    private static findMatchingMods(modFiles: string[], pattern: ConflictPattern): string[] {
        const matchingMods: string[] = [];
        
        for (const modFile of modFiles) {
            if (this.doesModMatchPattern(modFile, pattern)) {
                matchingMods.push(modFile);
            }
        }
        
        return matchingMods;
    }
    
    /**
     * Checks if a mod matches a conflict pattern
     */
    private static doesModMatchPattern(modFile: string, pattern: ConflictPattern): boolean {
        const fileName = modFile.toLowerCase();
        
        switch (pattern.type) {
            case 'filename':
                return pattern.patterns.some(p => fileName.includes(p.toLowerCase()));
                
            case 'author':
                // Extract potential author from filename
                const authorPart = fileName.split(/[_\-\.]/)[0];
                return pattern.patterns.some(p => authorPart.includes(p.toLowerCase()));
                
            case 'content':
                // This would require content analysis - for now, use filename
                return pattern.patterns.some(p => fileName.includes(p.toLowerCase()));
                
            case 'combination':
                // For combination patterns, we need to check if multiple patterns exist
                return pattern.patterns.some(p => fileName.includes(p.toLowerCase()));
                
            default:
                return false;
        }
    }
    
    /**
     * Determines if a conflict is actually detected based on matching mods
     */
    private static isConflictDetected(matchingMods: string[], conflict: KnownConflict): boolean {
        switch (conflict.type) {
            case KnownConflictType.VERSION_INCOMPATIBLE:
            case KnownConflictType.AUTHOR_CONFLICT:
            case KnownConflictType.CATEGORY_CONFLICT:
                // These require multiple matching mods
                return matchingMods.length > 1;
                
            case KnownConflictType.DEPENDENCY_CONFLICT:
                // Check if dependency mods are present but core is missing
                if (conflict.id === 'lot51_core_missing') {
                    const hasLot51Mods = matchingMods.length > 0;
                    const hasLot51Core = matchingMods.some(mod => 
                        mod.toLowerCase().includes('core') || mod.toLowerCase().includes('library')
                    );
                    return hasLot51Mods && !hasLot51Core;
                }
                return matchingMods.length > 0;
                
            case KnownConflictType.GAME_VERSION_CONFLICT:
                // Any matching mod indicates potential issue
                return matchingMods.length > 0;
                
            default:
                return matchingMods.length > 0;
        }
    }
    
    /**
     * Adds a new known conflict to the database
     */
    public static addKnownConflict(conflict: KnownConflict): void {
        this.conflicts.push(conflict);
    }
    
    /**
     * Gets all known conflicts
     */
    public static getAllKnownConflicts(): KnownConflict[] {
        return [...this.conflicts];
    }
    
    /**
     * Gets conflicts by type
     */
    public static getConflictsByType(type: KnownConflictType): KnownConflict[] {
        return this.conflicts.filter(c => c.type === type);
    }
    
    /**
     * Gets conflicts by severity
     */
    public static getConflictsBySeverity(severity: 'low' | 'medium' | 'high' | 'critical'): KnownConflict[] {
        return this.conflicts.filter(c => c.severity === severity);
    }
    
    /**
     * Updates the database with community-reported conflicts
     */
    public static async updateFromCommunity(): Promise<void> {
        // This would fetch updates from a community database
        // For now, it's a placeholder for future implementation
        console.log('[KnownConflictDatabase] Community updates not yet implemented');
    }
}
