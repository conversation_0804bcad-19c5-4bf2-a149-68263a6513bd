/**
 * Enhanced Filename Metadata Extractor
 * 
 * Extracts metadata from package filenames using configurable patterns
 * with security validation and confidence scoring.
 */

import { DEFAULT_METADATA_CONFIG, type MetadataExtractionConfig, type FilenamePattern } from '../../../config/metadata-extraction.config';

export interface FilenameMetadata {
    readonly author?: string;
    readonly modName?: string;
    readonly version?: string;
    readonly description?: string;
    readonly confidence: number;
    readonly source: 'filename';
    readonly pattern: string;
    readonly processingTime: number;
}

export interface FilenameExtractionOptions {
    readonly config?: Partial<MetadataExtractionConfig>;
    readonly maxProcessingTime?: number;
    readonly enableSanitization?: boolean;
}

/**
 * Security utilities for filename processing
 */
class FilenameSecurityValidator {
    private readonly config: MetadataExtractionConfig['security'];
    
    constructor(config: MetadataExtractionConfig['security']) {
        this.config = config;
    }
    
    /**
     * Validates filename for security issues
     */
    validateFilename(filename: string): { isValid: boolean; reason?: string } {
        if (!filename || typeof filename !== 'string') {
            return { isValid: false, reason: 'Invalid filename type' };
        }
        
        if (filename.length > this.config.sanitizationRules.maxLength) {
            return { isValid: false, reason: 'Filename too long' };
        }
        
        // Check for path traversal attempts
        if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
            return { isValid: false, reason: 'Path traversal detected' };
        }
        
        // Check for null bytes
        if (filename.includes('\0')) {
            return { isValid: false, reason: 'Null byte detected' };
        }
        
        return { isValid: true };
    }
    
    /**
     * Sanitizes extracted metadata strings
     */
    sanitizeString(input: string): string {
        if (!input || typeof input !== 'string') {
            return '';
        }
        
        let sanitized = input.trim();
        
        if (this.config.sanitizationRules.removeHtml) {
            sanitized = sanitized.replace(/<[^>]*>/g, '');
        }
        
        if (this.config.sanitizationRules.removeScripts) {
            sanitized = sanitized.replace(/javascript:/gi, '');
            sanitized = sanitized.replace(/on\w+\s*=/gi, '');
        }
        
        // Remove control characters except newlines and tabs
        sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
        
        // Limit length
        if (sanitized.length > this.config.sanitizationRules.maxLength) {
            sanitized = sanitized.substring(0, this.config.sanitizationRules.maxLength);
        }
        
        return sanitized;
    }
}

/**
 * Enhanced filename metadata extractor
 */
export class FilenameMetadataExtractor {
    private readonly config: MetadataExtractionConfig;
    private readonly securityValidator: FilenameSecurityValidator;
    
    constructor(options: FilenameExtractionOptions = {}) {
        this.config = {
            ...DEFAULT_METADATA_CONFIG,
            ...options.config
        };
        this.securityValidator = new FilenameSecurityValidator(this.config.security);
    }
    
    /**
     * Extracts metadata from filename with timeout protection
     */
    async extractMetadata(filename: string, options: FilenameExtractionOptions = {}): Promise<FilenameMetadata | null> {
        const startTime = performance.now();
        const maxTime = options.maxProcessingTime || this.config.security.maxProcessingTime;
        
        try {
            // Security validation
            const validation = this.securityValidator.validateFilename(filename);
            if (!validation.isValid) {
                console.warn(`[FilenameExtractor] Security validation failed: ${validation.reason}`);
                return null;
            }
            
            // Extract with timeout
            const result = await this.extractWithTimeout(filename, maxTime);
            
            const processingTime = performance.now() - startTime;
            
            if (result) {
                return {
                    ...result,
                    processingTime
                };
            }
            
            return null;
            
        } catch (error) {
            const processingTime = performance.now() - startTime;
            console.warn(`[FilenameExtractor] Extraction failed:`, error);
            return null;
        }
    }
    
    /**
     * Extracts metadata with timeout protection
     */
    private async extractWithTimeout(filename: string, maxTime: number): Promise<Omit<FilenameMetadata, 'processingTime'> | null> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Filename extraction timeout'));
            }, maxTime);
            
            try {
                const result = this.extractSync(filename);
                clearTimeout(timeout);
                resolve(result);
            } catch (error) {
                clearTimeout(timeout);
                reject(error);
            }
        });
    }
    
    /**
     * Synchronous extraction logic
     */
    private extractSync(filename: string): Omit<FilenameMetadata, 'processingTime'> | null {
        // Sort patterns by priority (higher priority first)
        const sortedPatterns = [...this.config.filename.patterns]
            .sort((a, b) => b.priority - a.priority);
        
        for (const pattern of sortedPatterns) {
            const match = this.tryPattern(filename, pattern);
            if (match) {
                return match;
            }
        }
        
        return null;
    }
    
    /**
     * Attempts to match filename against a specific pattern
     */
    private tryPattern(filename: string, pattern: FilenamePattern): Omit<FilenameMetadata, 'processingTime'> | null {
        try {
            const match = filename.match(pattern.pattern);
            if (!match) {
                return null;
            }
            
            const extracted: Partial<FilenameMetadata> = {
                source: 'filename',
                pattern: pattern.name,
                confidence: pattern.confidence
            };
            
            // Extract groups based on pattern configuration
            if (pattern.groups.author && match[pattern.groups.author]) {
                const rawAuthor = this.securityValidator.sanitizeString(match[pattern.groups.author]);
                if (this.isValidAuthor(rawAuthor)) {
                    // Clean common prefixes from author name
                    const cleanAuthor = rawAuthor.replace(/^[!@#$%^&*()]+/, '');
                    extracted.author = cleanAuthor;
                }
            }
            
            if (pattern.groups.modName && match[pattern.groups.modName]) {
                const modName = this.securityValidator.sanitizeString(match[pattern.groups.modName]);
                if (this.isValidModName(modName)) {
                    extracted.modName = modName;
                }
            }
            
            if (pattern.groups.version && match[pattern.groups.version]) {
                const version = this.securityValidator.sanitizeString(match[pattern.groups.version]);
                if (this.isValidVersion(version)) {
                    extracted.version = version;
                }
            }
            
            if (pattern.groups.description && match[pattern.groups.description]) {
                const description = this.securityValidator.sanitizeString(match[pattern.groups.description]);
                if (description.length > 0) {
                    extracted.description = description;
                }
            }
            
            // Adjust confidence based on extracted data quality
            const adjustedConfidence = this.calculateAdjustedConfidence(extracted, pattern.confidence);
            
            // Only return if we extracted meaningful data
            if (extracted.author || extracted.modName || extracted.version) {
                return {
                    ...extracted,
                    confidence: adjustedConfidence
                } as Omit<FilenameMetadata, 'processingTime'>;
            }
            
            return null;
            
        } catch (error) {
            console.warn(`[FilenameExtractor] Pattern ${pattern.name} failed:`, error);
            return null;
        }
    }
    
    /**
     * Validates author name
     */
    private isValidAuthor(author: string): boolean {
        // Remove common prefixes like ! or @
        const cleanAuthor = author.replace(/^[!@#$%^&*()]+/, '');

        return cleanAuthor.length >= this.config.filename.minAuthorLength &&
               author.length <= 50 &&
               !/^\d+$/.test(cleanAuthor) && // Not just numbers
               /[a-zA-Z]/.test(cleanAuthor); // Contains letters
    }
    
    /**
     * Validates mod name
     */
    private isValidModName(modName: string): boolean {
        return modName.length >= this.config.filename.minModNameLength &&
               modName.length <= 100 &&
               !/^\d+$/.test(modName) && // Not just numbers
               /[a-zA-Z]/.test(modName); // Contains letters
    }
    
    /**
     * Validates version string
     */
    private isValidVersion(version: string): boolean {
        return this.config.filename.versionFormats.some(format => format.test(version));
    }
    
    /**
     * Calculates adjusted confidence based on data quality
     */
    private calculateAdjustedConfidence(metadata: Partial<FilenameMetadata>, baseConfidence: number): number {
        let confidence = baseConfidence;
        
        // Boost confidence for having multiple fields
        const fieldCount = [metadata.author, metadata.modName, metadata.version].filter(Boolean).length;
        confidence += fieldCount * 5;
        
        // Boost for having version
        if (metadata.version) {
            confidence += 10;
        }
        
        // Boost for having author
        if (metadata.author) {
            confidence += 5;
        }
        
        // Cap at 100
        return Math.min(confidence, 100);
    }
}
