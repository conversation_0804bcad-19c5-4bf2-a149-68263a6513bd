/**
 * Debug a single mod to understand StringTable issues
 */

import * as fs from 'fs';
import * as path from 'path';
import { detailedAnalysisService } from '../services/analysis/core/DetailedAnalysisService';

async function debugSingleMod(): Promise<void> {
    console.log('🔍 Debugging Single Mod StringTable Analysis');
    console.log('=' .repeat(60));
    
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    const fileName = '!LittleMsSam_SimDaDatingApp_NoFWord.package'; // This one had StringTable
    const filePath = path.join(modsPath, fileName);
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ Test file not found');
        return;
    }
    
    try {
        console.log(`🔍 Analyzing: ${fileName}`);
        console.log('');
        
        const buffer = fs.readFileSync(filePath);
        console.log(`📁 File size: ${buffer.length} bytes`);
        
        // Perform detailed analysis
        const result = await detailedAnalysisService.analyzeAsync(
            buffer, 
            filePath, 
            { 
                needsDeepAnalysis: true,
                resourceCount: 0,
                resourceTypes: [],
                compressionStats: {},
                validationIssues: [],
                s4tkVersion: '1.0.0',
                processingTime: 0
            } as any
        );
        
        console.log('');
        console.log('📊 Analysis Results:');
        console.log(`  Has StringTable: ${result.hasStringTable}`);
        console.log(`  Actual Mod Name: ${result.actualModName || 'None'}`);
        console.log(`  Actual Description: ${result.actualDescription || 'None'}`);
        console.log(`  Extracted Items: ${result.extractedItemNames?.length || 0}`);
        console.log(`  Metadata Confidence: ${result.metadataConfidence}%`);
        
        if (result.stringTableData) {
            console.log('');
            console.log('📋 StringTable Data:');
            console.log(`  Mod Name: ${result.stringTableData.modName || 'None'}`);
            console.log(`  Description: ${result.stringTableData.description || 'None'}`);
            console.log(`  Item Names: ${result.stringTableData.itemNames.length}`);
            console.log(`  Custom Strings: ${result.stringTableData.customStringCount}`);
            console.log(`  Locale: ${result.stringTableData.locale}`);
            console.log(`  Confidence: ${result.stringTableData.confidence}%`);
            console.log(`  Processing Time: ${result.stringTableData.processingTime}ms`);
        }
        
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
        console.log(`Stack: ${error.stack}`);
    }
}

// Run the debug
if (require.main === module) {
    debugSingleMod().catch(console.error);
}

export { debugSingleMod };
