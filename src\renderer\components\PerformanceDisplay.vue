<template>
  <div class="performance-display">
    <!-- Performance Overview -->
    <div class="performance-overview">
      <div class="impact-indicator" :class="`impact-${performance.estimatedImpact}`">
        <div class="impact-icon">
          <BoltIcon v-if="performance.estimatedImpact === 'minimal'" />
          <ClockIcon v-else-if="performance.estimatedImpact === 'low'" />
          <ExclamationTriangleIcon v-else-if="performance.estimatedImpact === 'medium'" />
          <FireIcon v-else />
        </div>
        <div class="impact-content">
          <div class="impact-level">{{ formatImpact(performance.estimatedImpact) }}</div>
          <div class="impact-label">Performance Impact</div>
        </div>
      </div>
      
      <div class="performance-metrics">
        <div class="metric">
          <div class="metric-label">Memory</div>
          <div class="metric-value" :class="`usage-${performance.memoryUsage}`">
            {{ formatUsage(performance.memoryUsage) }}
          </div>
        </div>
        
        <div class="metric">
          <div class="metric-label">CPU</div>
          <div class="metric-value" :class="`usage-${performance.cpuUsage}`">
            {{ formatUsage(performance.cpuUsage) }}
          </div>
        </div>
        
        <div class="metric">
          <div class="metric-label">Load Time</div>
          <div class="metric-value" :class="`load-${performance.loadTime}`">
            {{ formatLoadTime(performance.loadTime) }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Optimization Suggestions -->
    <div v-if="performance.optimizationSuggestions?.length > 0" class="optimization-section">
      <h6 class="section-title">
        <LightBulbIcon class="title-icon" />
        Optimization Suggestions
      </h6>
      <div class="suggestions-list">
        <div 
          v-for="suggestion in performance.optimizationSuggestions" 
          :key="suggestion"
          class="suggestion-item"
        >
          <CheckCircleIcon class="suggestion-icon" />
          <span class="suggestion-text">{{ suggestion }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  BoltIcon, 
  ClockIcon, 
  ExclamationTriangleIcon, 
  FireIcon,
  LightBulbIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline';

interface Performance {
  estimatedImpact: string;
  memoryUsage?: string;
  cpuUsage?: string;
  loadTime?: string;
  optimizationSuggestions?: string[];
}

defineProps<{
  performance: Performance;
}>();

const formatImpact = (impact: string): string => {
  const impacts: Record<string, string> = {
    'minimal': 'Minimal',
    'low': 'Low',
    'medium': 'Medium',
    'high': 'High'
  };
  return impacts[impact] || impact;
};

const formatUsage = (usage: string): string => {
  const usages: Record<string, string> = {
    'low': 'Low',
    'medium': 'Medium',
    'high': 'High'
  };
  return usages[usage] || usage;
};

const formatLoadTime = (loadTime: string): string => {
  const times: Record<string, string> = {
    'fast': 'Fast',
    'medium': 'Medium',
    'slow': 'Slow'
  };
  return times[loadTime] || loadTime;
};
</script>

<style scoped>
.performance-display {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Overview */
.performance-overview {
  display: flex;
  gap: var(--space-6);
  align-items: center;
}

.impact-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.impact-indicator.impact-minimal {
  background: var(--success-bg);
  border: 1px solid var(--success-border);
}

.impact-indicator.impact-low {
  background: var(--info-bg);
  border: 1px solid var(--info-border);
}

.impact-indicator.impact-medium {
  background: var(--warning-bg);
  border: 1px solid var(--warning-border);
}

.impact-indicator.impact-high {
  background: var(--error-bg);
  border: 1px solid var(--error-border);
}

.impact-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.impact-indicator.impact-minimal .impact-icon {
  color: var(--success);
}

.impact-indicator.impact-low .impact-icon {
  color: var(--info);
}

.impact-indicator.impact-medium .impact-icon {
  color: var(--warning);
}

.impact-indicator.impact-high .impact-icon {
  color: var(--error);
}

.impact-content {
  flex: 1;
}

.impact-level {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-1);
}

.impact-indicator.impact-minimal .impact-level {
  color: var(--success);
}

.impact-indicator.impact-low .impact-level {
  color: var(--info);
}

.impact-indicator.impact-medium .impact-level {
  color: var(--warning);
}

.impact-indicator.impact-high .impact-level {
  color: var(--error);
}

.impact-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.performance-metrics {
  display: flex;
  gap: var(--space-4);
  flex: 1;
}

.metric {
  text-align: center;
  padding: var(--space-3);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  flex: 1;
}

.metric-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-2);
}

.metric-value {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

.metric-value.usage-low,
.metric-value.load-fast {
  background: var(--success-bg);
  color: var(--success);
}

.metric-value.usage-medium,
.metric-value.load-medium {
  background: var(--warning-bg);
  color: var(--warning);
}

.metric-value.usage-high,
.metric-value.load-slow {
  background: var(--error-bg);
  color: var(--error);
}

/* Optimization */
.optimization-section {
  background: var(--info-bg);
  border: 1px solid var(--info-border);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--info);
  margin: 0 0 var(--space-3) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.title-icon {
  width: 16px;
  height: 16px;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  padding: var(--space-2);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.suggestion-icon {
  width: 16px;
  height: 16px;
  color: var(--info);
  flex-shrink: 0;
  margin-top: 2px;
}

.suggestion-text {
  font-size: var(--text-sm);
  color: var(--text-primary);
  line-height: var(--leading-relaxed);
}
</style>
