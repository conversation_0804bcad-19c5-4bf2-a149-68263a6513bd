#!/usr/bin/env node

/**
 * Intelligence Integration Test Tool
 * 
 * Tests the Phase 4A integration of intelligence components
 * (DependencyAnalyzer, ResourceIntelligenceAnalyzer, QualityAssessmentAnalyzer)
 * into the main analysis pipeline.
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface IntelligenceTestResult {
    filePath: string;
    fileType: string;
    hasIntelligence: boolean;
    hasDependencies: boolean;
    hasResourceIntelligence: boolean;
    hasQualityAssessment: boolean;
    overallScore?: number;
    riskLevel?: string;
    processingTime: number;
    error?: string;
}

class IntelligenceIntegrationTester {
    private analysisService: PackageAnalysisService;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
    }
    
    /**
     * Tests intelligence integration on all files in assets directory
     */
    public async testIntelligenceIntegration(): Promise<void> {
        console.log('🧠 Testing Intelligence Integration (Phase 4A)...\n');
        
        const assetsDir = path.join(process.cwd(), 'assets');
        
        if (!fs.existsSync(assetsDir)) {
            console.error('❌ Assets directory not found:', assetsDir);
            return;
        }
        
        const files = fs.readdirSync(assetsDir)
            .filter(file => file.endsWith('.package') || file.endsWith('.ts4script'))
            .slice(0, 5); // Limit to first 5 files for testing
        
        console.log(`📁 Found ${files.length} test files\n`);
        
        const results: IntelligenceTestResult[] = [];
        
        for (const file of files) {
            const filePath = path.join(assetsDir, file);
            const result = await this.testFile(filePath);
            results.push(result);
        }
        
        this.generateReport(results);
    }
    
    /**
     * Tests intelligence integration for a single file
     */
    private async testFile(filePath: string): Promise<IntelligenceTestResult> {
        const fileName = path.basename(filePath);
        console.log(`🧠 Testing Intelligence: ${fileName}`);
        
        const startTime = Date.now();
        
        try {
            const buffer = fs.readFileSync(filePath);
            const analysis = await this.analysisService.detailedAnalyzeAsync(buffer, filePath);
            
            const processingTime = Date.now() - startTime;
            
            const result: IntelligenceTestResult = {
                filePath: fileName,
                fileType: analysis.fileType,
                hasIntelligence: !!analysis.intelligence,
                hasDependencies: !!(analysis.intelligence?.dependencies),
                hasResourceIntelligence: !!(analysis.intelligence?.resourceIntelligence),
                hasQualityAssessment: !!(analysis.intelligence?.qualityAssessment),
                overallScore: analysis.intelligence?.qualityAssessment?.overallScore,
                riskLevel: analysis.intelligence?.dependencies?.riskLevel,
                processingTime
            };
            
            // Display results
            console.log(`   🧠 Intelligence Data: ${result.hasIntelligence ? '✅ Present' : '❌ Missing'}`);
            console.log(`   📊 Dependencies: ${result.hasDependencies ? '✅ Analyzed' : '❌ Missing'}`);
            console.log(`   🔍 Resource Intelligence: ${result.hasResourceIntelligence ? '✅ Analyzed' : '❌ Missing'}`);
            console.log(`   ⭐ Quality Assessment: ${result.hasQualityAssessment ? '✅ Analyzed' : '❌ Missing'}`);
            
            if (result.overallScore !== undefined) {
                console.log(`   📈 Quality Score: ${result.overallScore}/100`);
            }
            if (result.riskLevel) {
                console.log(`   ⚠️ Risk Level: ${result.riskLevel}`);
            }
            console.log(`   ⏱️ Processing Time: ${result.processingTime}ms`);
            console.log(`   ✅ Success\n`);
            
            return result;
            
        } catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            console.log(`   ❌ Error: ${errorMessage}`);
            console.log(`   ⏱️ Processing Time: ${processingTime}ms\n`);
            
            return {
                filePath: fileName,
                fileType: 'unknown',
                hasIntelligence: false,
                hasDependencies: false,
                hasResourceIntelligence: false,
                hasQualityAssessment: false,
                processingTime,
                error: errorMessage
            };
        }
    }
    
    /**
     * Generates a comprehensive intelligence integration report
     */
    private generateReport(results: IntelligenceTestResult[]): void {
        console.log('🧠 INTELLIGENCE INTEGRATION REPORT');
        console.log('==================================================\n');
        
        // Calculate statistics
        const totalFiles = results.length;
        const successfulFiles = results.filter(r => !r.error).length;
        const filesWithIntelligence = results.filter(r => r.hasIntelligence).length;
        const filesWithDependencies = results.filter(r => r.hasDependencies).length;
        const filesWithResourceIntelligence = results.filter(r => r.hasResourceIntelligence).length;
        const filesWithQualityAssessment = results.filter(r => r.hasQualityAssessment).length;
        
        const successRate = Math.round((successfulFiles / totalFiles) * 100);
        const intelligenceRate = Math.round((filesWithIntelligence / totalFiles) * 100);
        const dependencyRate = Math.round((filesWithDependencies / totalFiles) * 100);
        const resourceIntelligenceRate = Math.round((filesWithResourceIntelligence / totalFiles) * 100);
        const qualityAssessmentRate = Math.round((filesWithQualityAssessment / totalFiles) * 100);
        
        console.log('📈 SUMMARY:');
        console.log(`   Total Files: ${totalFiles}`);
        console.log(`   Successful Analysis: ${successfulFiles}/${totalFiles} (${successRate}%)`);
        console.log(`   Intelligence Data: ${filesWithIntelligence}/${totalFiles} (${intelligenceRate}%)`);
        console.log(`   Dependency Analysis: ${filesWithDependencies}/${totalFiles} (${dependencyRate}%)`);
        console.log(`   Resource Intelligence: ${filesWithResourceIntelligence}/${totalFiles} (${resourceIntelligenceRate}%)`);
        console.log(`   Quality Assessment: ${filesWithQualityAssessment}/${totalFiles} (${qualityAssessmentRate}%)\n`);
        
        // Performance metrics
        const avgProcessingTime = Math.round(
            results.reduce((sum, r) => sum + r.processingTime, 0) / totalFiles
        );
        const maxProcessingTime = Math.max(...results.map(r => r.processingTime));
        
        console.log('⏱️ PERFORMANCE:');
        console.log(`   Average Processing Time: ${avgProcessingTime}ms`);
        console.log(`   Maximum Processing Time: ${maxProcessingTime}ms`);
        console.log(`   Target: <10ms average (current system: 7-8ms)\n`);
        
        // Quality scores
        const qualityScores = results
            .filter(r => r.overallScore !== undefined)
            .map(r => r.overallScore!);
        
        if (qualityScores.length > 0) {
            const avgQualityScore = Math.round(
                qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length
            );
            console.log('⭐ QUALITY SCORES:');
            console.log(`   Average Quality Score: ${avgQualityScore}/100`);
            console.log(`   Range: ${Math.min(...qualityScores)} - ${Math.max(...qualityScores)}\n`);
        }
        
        // Success assessment
        const overallSuccess = intelligenceRate >= 80 ? '🎉 EXCELLENT' : 
                             intelligenceRate >= 60 ? '✅ GOOD' : 
                             intelligenceRate >= 40 ? '⚠️ FAIR' : '❌ NEEDS IMPROVEMENT';
        
        console.log(`🎯 OVERALL ASSESSMENT: ${overallSuccess}`);
        console.log(`   Target: 100% intelligence integration`);
        console.log(`   Current: ${intelligenceRate}% intelligence data`);
        
        if (intelligenceRate >= 80 && avgProcessingTime <= 15) {
            console.log('   🏆 PHASE 4A INTEGRATION SUCCESSFUL!');
        } else if (intelligenceRate >= 60) {
            console.log('   📈 Good progress! Continue with optimization.');
        } else {
            console.log('   📋 Continue implementing intelligence integration.');
        }
        
        // Error summary
        const errors = results.filter(r => r.error);
        if (errors.length > 0) {
            console.log('\n❌ ERRORS:');
            errors.forEach(error => {
                console.log(`   ${error.filePath}: ${error.error}`);
            });
        }
    }
}

// Run the test
async function main() {
    const tester = new IntelligenceIntegrationTester();
    await tester.testIntelligenceIntegration();
}

if (require.main === module) {
    main().catch(console.error);
}
