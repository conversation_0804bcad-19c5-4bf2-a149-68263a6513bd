/**
 * Generic Resource Processor
 * 
 * Provides basic resource processing functionality for all resource types.
 * Used as a fallback processor and base functionality for specialized processors.
 * 
 * This processor extracts basic metadata without specialized analysis.
 */

import type { ResourceEntry } from '@s4tk/models/types';
import type { IResourceProcessor, ProcessedResource, ResourceProcessingOptions } from './types';
import { ResourceTypeHelpers } from '../../../constants/unifiedResourceTypes';

/**
 * Generic processor that handles basic resource information extraction
 */
export class GenericResourceProcessor implements IResourceProcessor {
    
    canProcess(resourceType: number): boolean {
        // Generic processor can handle any resource type
        return true;
    }
    
    async process(entry: ResourceEntry, options?: ResourceProcessingOptions): Promise<ProcessedResource> {
        try {
            const resourceKey = entry.key;
            const resourceValue = entry.value;
            
            // Extract basic information
            const result: ProcessedResource = {
                id: entry.id?.toString() || `${resourceKey.type}-${resourceKey.group}-${resourceKey.instance}`,
                type: this.getResourceTypeName(resourceKey.type),
                size: this.getResourceSize(resourceValue),
                compressed: this.isCompressed(resourceValue),
                metadata: {
                    resourceType: resourceKey.type,
                    group: resourceKey.group,
                    instance: resourceKey.instance,
                    specialized: false,
                    processorUsed: this.getProcessorName()
                },
                issues: []
            };
            
            // Add compression information if available
            if (result.compressed) {
                result.metadata.compressionType = this.getCompressionType(resourceValue);
            }
            
            // Add basic validation
            this.validateResource(entry, result);
            
            return result;
            
        } catch (error) {
            return this.createErrorResult(entry, error);
        }
    }
    
    getProcessorName(): string {
        return 'GenericResourceProcessor';
    }
    
    /**
     * Gets a human-readable name for the resource type
     */
    private getResourceTypeName(resourceType: number): string {
        // Try to get a friendly name from ResourceTypeHelpers
        if (ResourceTypeHelpers.isTextureResource(resourceType)) {
            return 'Texture';
        }
        if (ResourceTypeHelpers.isCASResource(resourceType)) {
            return 'CAS Resource';
        }
        if (ResourceTypeHelpers.isBuildBuyResource(resourceType)) {
            return 'Build/Buy Resource';
        }
        if (ResourceTypeHelpers.isTuningResource(resourceType)) {
            return 'Tuning Resource';
        }
        if (ResourceTypeHelpers.isScriptResource(resourceType)) {
            return 'Script Resource';
        }
        
        // Fallback to hex representation
        return `Resource (0x${resourceType.toString(16).toUpperCase()})`;
    }
    
    /**
     * Gets the size of the resource
     */
    private getResourceSize(resourceValue: any): number {
        if (!resourceValue) {
            return 0;
        }
        
        // Handle different value formats
        if (Buffer.isBuffer(resourceValue)) {
            return resourceValue.length;
        }
        
        if (resourceValue.buffer && Buffer.isBuffer(resourceValue.buffer)) {
            return resourceValue.buffer.length;
        }
        
        if (resourceValue._bufferCache && resourceValue._bufferCache.buffer) {
            return resourceValue._bufferCache.buffer.length;
        }
        
        if (typeof resourceValue === 'string') {
            return Buffer.byteLength(resourceValue, 'utf8');
        }
        
        // Estimate size for objects
        if (typeof resourceValue === 'object') {
            try {
                return Buffer.byteLength(JSON.stringify(resourceValue), 'utf8');
            } catch {
                return 0;
            }
        }
        
        return 0;
    }
    
    /**
     * Determines if the resource is compressed
     */
    private isCompressed(resourceValue: any): boolean {
        // Basic heuristic - check for compression indicators
        if (!resourceValue) {
            return false;
        }
        
        // Check for S4TK compression indicators
        if (resourceValue.compressionType !== undefined) {
            return resourceValue.compressionType !== 0;
        }
        
        // Check buffer headers for compression signatures
        const buffer = this.extractBuffer(resourceValue);
        if (buffer && buffer.length >= 4) {
            // Check for common compression signatures
            const header = buffer.readUInt32LE(0);
            
            // DBPF compression signature
            if (header === 0x5A42DB) {
                return true;
            }
            
            // Other compression signatures could be added here
        }
        
        return false;
    }
    
    /**
     * Gets the compression type if available
     */
    private getCompressionType(resourceValue: any): string {
        if (resourceValue && resourceValue.compressionType !== undefined) {
            switch (resourceValue.compressionType) {
                case 0: return 'None';
                case 1: return 'StreamZlib';
                case 2: return 'InternalCompression';
                case 3: return 'DeletedRecord';
                default: return `Unknown (${resourceValue.compressionType})`;
            }
        }
        
        return 'Unknown';
    }
    
    /**
     * Extracts buffer from various resource value formats
     */
    private extractBuffer(resourceValue: any): Buffer | null {
        if (!resourceValue) {
            return null;
        }
        
        if (Buffer.isBuffer(resourceValue)) {
            return resourceValue;
        }
        
        if (resourceValue.buffer && Buffer.isBuffer(resourceValue.buffer)) {
            return resourceValue.buffer;
        }
        
        if (resourceValue._bufferCache && resourceValue._bufferCache.buffer) {
            return resourceValue._bufferCache.buffer;
        }
        
        return null;
    }
    
    /**
     * Performs basic validation on the resource
     */
    private validateResource(entry: ResourceEntry, result: ProcessedResource): void {
        // Check for empty resources
        if (result.size === 0) {
            result.issues.push('Resource appears to be empty');
        }
        
        // Check for reasonable size limits
        if (result.size > 100 * 1024 * 1024) { // 100MB
            result.issues.push('Resource is unusually large (>100MB)');
        }
        
        // Check for valid resource key
        if (!entry.key || entry.key.type === 0) {
            result.issues.push('Resource has invalid or missing type');
        }
    }
    
    /**
     * Creates an error result when processing fails
     */
    private createErrorResult(entry: ResourceEntry, error: any): ProcessedResource {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
            id: entry.id?.toString() || 'unknown',
            type: 'Error',
            size: 0,
            compressed: false,
            metadata: {
                error: errorMessage,
                processorUsed: this.getProcessorName()
            },
            issues: [`Processing failed: ${errorMessage}`]
        };
    }
}
