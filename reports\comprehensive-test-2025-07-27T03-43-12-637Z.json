{"systemValidation": {"configurationLoaded": true, "componentsWorking": true, "fileTypeSupport": true, "sampleTestPassed": true, "issues": []}, "contentAnalysis": {"totalAnalyzed": 100, "successRate": 1, "contentTypeBreakdown": {"unknown": 11, "cas_only": 18, "objects_only": 43, "script_mod": 4, "gameplay_only": 12, "mixed_content": 12}, "confidenceScores": [0, 30, 70, 0.9, 65, 65, 65, 65, 65, 65, 65, 65, 65, 30, 0, 30, 70, 70, 100, 100, 100, 100, 30, 100, 100, 100, 70, 30, 100, 100, 70, 30, 70, 30, 70, 0, 70, 0, 0, 0, 0, 65, 95, 0, 65, 70, 70, 70, 100, 100, 30, 30, 0.9, 30, 70, 0, 0.9, 0, 70, 70, 70, 70, 70, 30, 70, 70, 70, 70, 70, 70, 70, 70, 100, 70, 70, 70, 70, 70, 70, 70, 0, 30, 30, 70, 30, 30, 70, 70, 65, 30, 70, 30, 30, 70, 70, 70, 70, 70, 70, 70], "spotCheckResults": [{"fileName": "Aurum_HairstyleFMM_146_Lotus.package", "expectedType": "cas_only", "detectedType": "cas_only", "confidence": 30, "correct": true}, {"fileName": "brazenlotus_ModCORE.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "GROVE_Accent_Armchair.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "GROVE_Modern_Armchair.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "LIVIN'RUM_Rocking_Chair.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "LIVIN'R<PERSON>_Wooden_Chair.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "mc_pregnancy.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "miiko-3d-eyelashes-part-3_piercings-nc_(makeup).package", "expectedType": "cas_only", "detectedType": "cas_only", "confidence": 30, "correct": true}, {"fileName": "Meerigold_KonbiniAttendant.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "SBStoneWallShelf_Version2.package", "expectedType": "object_only", "detectedType": "objects_only", "confidence": 70, "correct": false}, {"fileName": "SIXAMcc_artz-shelfwall-modelc.package", "expectedType": "object_only", "detectedType": "objects_only", "confidence": 70, "correct": false}, {"fileName": "SIXAMcc_BohoBedroom-Confort-HangingChair-Med.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "UnderstairsShelf_Short.package", "expectedType": "object_only", "detectedType": "objects_only", "confidence": 70, "correct": false}, {"fileName": "[QICC]June_2022_Collection_Kira_Hair.package", "expectedType": "cas_only", "detectedType": "cas_only", "confidence": 30, "correct": true}]}, "fullCollection": {"totalMods": 1339, "packageFiles": 1274, "scriptFiles": 65, "successfulAnalyses": 1329, "failedAnalyses": 10, "totalProcessingTime": 61889, "averageTimePerMod": 46.22031366691561, "memoryUsage": {"peak": 84.84275817871094, "average": 24.262958745977787, "final": 27.722328186035156}, "errorSummary": {"corruptedFiles": [], "analysisFailures": ["LittleMsSam_AutoGardening_Addon_MoistureWeedInfestationDecay.package", "LittleMsSam_AutoGardening_Addon_NoPuddlesOutside.package", "LittleMsSam_Babysitter.package", "LittleMsSam_CanIComeOver.package", "LittleMsSam_Chores.package", "LittleMsSam_<PERSON>res_Addon_NotOnDuty.package", "LittleMsSam_Chores_Addon_UniversityBoard.package", "LittleMsSam_RomanticMassage.package", "thepancake1-MoreTraitsInCAS-v1t-1.112.package", "XmlInjector_Test_v4.2.package"], "memoryIssues": [], "otherErrors": []}}, "conflictDetection": {"knownConflicts": 0, "resourceConflicts": 1, "falsePositives": 0, "evidenceBasedAccuracy": true, "detectedIssues": []}, "organization": {"organizationScore": 90, "recommendations": ["Large collection detected - consider using mod management tools"], "optimizationOpportunities": ["Regular cleanup of unused mods", "Backup important mod configurations"], "collectionHealth": "excellent"}, "performance": {"overallScore": "excellent", "scalabilityAssessment": "excellent", "memoryEfficiency": "excellent", "processingSpeed": "excellent", "recommendations": []}}