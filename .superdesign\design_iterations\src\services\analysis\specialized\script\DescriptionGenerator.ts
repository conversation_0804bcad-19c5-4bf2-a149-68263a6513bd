/**
 * Script Description Generator
 * 
 * Specialized component for generating human-readable descriptions of script mods.
 * Creates meaningful descriptions based on extracted script information.
 * 
 * Extracted from ScriptAnalyzer.ts as part of Phase 2 refactoring.
 */

import { SCRIPT_CATEGORY_DISPLAY_NAMES, type ScriptModInfo } from './types';
import { CategoryClassifier } from './CategoryClassifier';
import { GameplayAreaDetector } from './GameplayAreaDetector';

/**
 * Handles generation of human-readable descriptions for script mods
 */
export class DescriptionGenerator {
    
    /**
     * Generates a comprehensive description for a script mod
     * 
     * @param scriptInfo - Script mod information
     * @returns Human-readable description
     */
    public static generateScriptDescription(scriptInfo: ScriptModInfo): string {
        const parts: string[] = [];
        
        // Add category
        const categoryName = SCRIPT_CATEGORY_DISPLAY_NAMES[scriptInfo.category] || 'Script Mod';
        parts.push(categoryName);
        
        // Add subcategory if meaningful
        if (scriptInfo.subcategory && scriptInfo.subcategory !== 'unknown') {
            const subcategoryName = CategoryClassifier.getSubcategoryDisplayName(scriptInfo.subcategory);
            if (subcategoryName !== categoryName) {
                parts.push(`(${subcategoryName})`);
            }
        }
        
        // Add gameplay areas if relevant
        if (scriptInfo.gameplayAreas.length > 0 && !this.isGenericGameplayArea(scriptInfo.gameplayAreas)) {
            const areaDescription = this.formatGameplayAreas(scriptInfo.gameplayAreas);
            parts.push(`affecting ${areaDescription}`);
        }
        
        // Add author if available
        if (scriptInfo.author) {
            parts.push(`by ${scriptInfo.author}`);
        }
        
        // Add version if available
        if (scriptInfo.version) {
            parts.push(`v${scriptInfo.version}`);
        }
        
        return parts.join(' ');
    }
    
    /**
     * Generates a detailed description with technical information
     * 
     * @param scriptInfo - Script mod information
     * @returns Detailed technical description
     */
    public static generateDetailedDescription(scriptInfo: ScriptModInfo): string {
        const parts: string[] = [];
        
        // Basic description
        parts.push(this.generateScriptDescription(scriptInfo));
        
        // Add Python file information
        if (scriptInfo.pythonFiles.length > 0) {
            const fileCount = scriptInfo.pythonFiles.length;
            parts.push(`Contains ${fileCount} Python file${fileCount > 1 ? 's' : ''}`);
        }
        
        // Add dependency information
        if (scriptInfo.dependencies.length > 0) {
            const depCount = scriptInfo.dependencies.length;
            parts.push(`with ${depCount} dependenc${depCount > 1 ? 'ies' : 'y'}`);
        }
        
        // Add framework/library flags
        if (scriptInfo.isFramework) {
            parts.push('(Framework)');
        } else if (scriptInfo.isLibrary) {
            parts.push('(Library)');
        }
        
        return parts.join(' ');
    }
    
    /**
     * Formats gameplay areas for display
     * 
     * @param gameplayAreas - Array of gameplay areas
     * @returns Formatted gameplay areas string
     */
    private static formatGameplayAreas(gameplayAreas: string[]): string {
        if (gameplayAreas.length === 0) {
            return 'unknown areas';
        }
        
        if (gameplayAreas.length === 1) {
            return this.formatGameplayArea(gameplayAreas[0]);
        }
        
        const formattedAreas = gameplayAreas.map(area => this.formatGameplayArea(area));
        
        if (formattedAreas.length === 2) {
            return formattedAreas.join(' and ');
        }
        
        return formattedAreas.slice(0, -1).join(', ') + ', and ' + formattedAreas[formattedAreas.length - 1];
    }
    
    /**
     * Formats a single gameplay area for display
     * 
     * @param gameplayArea - Gameplay area string
     * @returns Formatted gameplay area
     */
    private static formatGameplayArea(gameplayArea: string): string {
        const areaDisplayNames: Record<string, string> = {
            'careers': 'careers',
            'relationships': 'relationships',
            'family': 'family life',
            'pregnancy': 'pregnancy',
            'skills': 'skills',
            'traits': 'traits',
            'aspirations': 'aspirations',
            'emotions': 'emotions',
            'interactions': 'social interactions',
            'events': 'events',
            'autonomy': 'autonomous behavior',
            'ui': 'user interface',
            'objects': 'objects',
            'unknown': 'gameplay'
        };
        
        return areaDisplayNames[gameplayArea] || gameplayArea.replace(/_/g, ' ');
    }
    
    /**
     * Checks if gameplay areas are too generic to mention
     * 
     * @param gameplayAreas - Array of gameplay areas
     * @returns true if areas are generic
     */
    private static isGenericGameplayArea(gameplayAreas: string[]): boolean {
        return gameplayAreas.length === 1 && gameplayAreas[0] === 'unknown';
    }
    
    /**
     * Generates a summary for multiple script mods
     * 
     * @param scriptInfos - Array of script mod information
     * @returns Summary description
     */
    public static summarizeScriptMods(scriptInfos: ScriptModInfo[]): {
        primaryCategory: string;
        subcategory: string;
        description: string;
    } {
        if (scriptInfos.length === 0) {
            return {
                primaryCategory: 'Unknown Script',
                subcategory: 'unknown',
                description: 'Unknown script content'
            };
        }
        
        // Find the most common category
        const categoryCount = new Map<string, number>();
        scriptInfos.forEach(info => {
            const category = info.category;
            categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
        });
        
        const primaryCategory = Array.from(categoryCount.entries())
            .sort((a, b) => b[1] - a[1])[0][0];
        
        // Get the most specific subcategory
        const subcategories = scriptInfos
            .filter(info => info.category === primaryCategory)
            .map(info => info.subcategory);
        
        const subcategory = subcategories[0] || 'unknown';
        
        // Generate summary description
        const categoryDisplayName = SCRIPT_CATEGORY_DISPLAY_NAMES[primaryCategory as keyof typeof SCRIPT_CATEGORY_DISPLAY_NAMES] || 'Script Mod';
        const description = `${categoryDisplayName} content with ${scriptInfos.length} script${scriptInfos.length > 1 ? 's' : ''}`;
        
        return {
            primaryCategory: categoryDisplayName,
            subcategory,
            description
        };
    }
    
    /**
     * Generates installation notes based on script type
     * 
     * @param scriptInfo - Script mod information
     * @returns Installation notes
     */
    public static generateInstallationNotes(scriptInfo: ScriptModInfo): string[] {
        const notes: string[] = [];
        
        // Framework-specific notes
        if (scriptInfo.isFramework) {
            switch (scriptInfo.subcategory) {
                case 'xml_injector':
                    notes.push('Must be installed before any mods that require XML injection');
                    break;
                case 'mc_command_center':
                    notes.push('Core framework - install before other MCCC modules');
                    break;
                case 'ui_cheats':
                    notes.push('UI enhancement framework - may affect other UI mods');
                    break;
            }
        }
        
        // Library-specific notes
        if (scriptInfo.isLibrary) {
            notes.push('Library mod - should be installed before mods that depend on it');
        }
        
        // Dependency notes
        if (scriptInfo.dependencies.length > 0) {
            notes.push(`Requires: ${scriptInfo.dependencies.join(', ')}`);
        }
        
        // Conflict warnings
        if (scriptInfo.conflicts.length > 0) {
            notes.push(`Potential conflicts: ${scriptInfo.conflicts.join(', ')}`);
        }
        
        return notes;
    }
    
    /**
     * Generates compatibility information
     * 
     * @param scriptInfo - Script mod information
     * @returns Compatibility information
     */
    public static generateCompatibilityInfo(scriptInfo: ScriptModInfo): {
        gameVersion: string;
        requiredFrameworks: string[];
        potentialConflicts: string[];
    } {
        return {
            gameVersion: 'Unknown', // Would need additional detection
            requiredFrameworks: scriptInfo.dependencies.filter(dep => 
                ['mc_command_center', 'xml_injector', 'ui_cheats'].includes(dep)
            ),
            potentialConflicts: scriptInfo.conflicts
        };
    }
}
