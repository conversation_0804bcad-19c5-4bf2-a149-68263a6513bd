<template>
  <div class="analysis-results">
    <!-- Results Header -->
    <div class="results-header card-header">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-xl font-semibold mb-sm">Analysis Results</h2>
          <div class="results-summary text-sm text-muted">
            <span class="font-medium">{{ results.length }}</span> file(s) analyzed • 
            <span class="font-medium">{{ totalResources }}</span> total resources
          </div>
        </div>
        <div class="results-actions flex gap-sm">
          <button class="btn btn-secondary btn-sm" @click="exportResults('json')">
            Export JSON
          </button>
          <button class="btn btn-secondary btn-sm" @click="exportResults('csv')">
            Export CSV
          </button>
        </div>
      </div>
    </div>

    <!-- Filters and Search -->
    <div class="results-filters mb-lg">
      <div class="flex gap-md items-center flex-wrap">
        <div class="form-group" style="margin-bottom: 0; min-width: 200px;">
          <input
            v-model="searchQuery"
            type="text"
            class="form-input"
            placeholder="Search resources..."
          />
        </div>
        
        <div class="form-group" style="margin-bottom: 0;">
          <select v-model="selectedTypeFilter" class="form-input">
            <option value="">All Types</option>
            <option v-for="type in availableTypes" :key="type" :value="type">
              {{ type }}
            </option>
          </select>
        </div>
        
        <div class="form-group" style="margin-bottom: 0;">
          <select v-model="selectedFileFilter" class="form-input">
            <option value="">All Files</option>
            <option v-for="file in availableFiles" :key="file" :value="file">
              {{ getFileName(file) }}
            </option>
          </select>
        </div>
        
        <button 
          v-if="hasActiveFilters" 
          class="btn btn-secondary btn-sm"
          @click="clearFilters"
        >
          Clear Filters
        </button>
      </div>
    </div>

    <!-- Results by File -->
    <div class="results-content">
      <div v-for="result in filteredResults" :key="result.filePath" class="file-result mb-lg">
        <!-- File Header -->
        <div class="file-header">
          <div class="flex justify-between items-center">
            <div>
              <h3 class="font-medium text-lg">{{ getFileName(result.filePath) }}</h3>
              <div class="file-path text-sm text-muted font-mono">{{ result.filePath }}</div>
              <div class="file-stats text-sm text-muted mt-xs">
                {{ getFilteredResourcesForFile(result).length }} resources • 
                {{ formatFileSize(result.fileSize) }} • 
                {{ getCategoryDisplayName(result.category) }}
                <span v-if="result.isOverride" class="badge badge-warning ml-sm">Override</span>
                <span v-if="result.subcategory" class="badge badge-info ml-sm">{{ formatSubcategory(result.subcategory) }}</span>
              </div>
              
              <!-- Enhanced metadata display -->
              <div v-if="result.dependencies.length > 0 || result.conflicts.length > 0 || result.suggestedPath || hasDeepAnalysis(result)" class="file-metadata text-sm mt-sm">
                <div v-if="result.suggestedPath" class="metadata-item">
                  <strong>Suggested folder:</strong> {{ result.suggestedPath }}
                </div>
                
                <!-- Deep Analysis Results -->
                <div v-if="hasDeepAnalysis(result)" class="deep-analysis-section">
                  <div v-if="result.metadata.deepAnalysis" class="metadata-item">
                    <strong>Detailed Analysis:</strong> {{ result.metadata.deepAnalysis.detailedDescription }}
                  </div>
                  
                  <!-- CAS-specific info -->
                  <div v-if="result.metadata.deepAnalysis?.casInfo" class="cas-info mt-xs">
                    <div class="metadata-item">
                      <strong>CAS Category:</strong> {{ formatSubcategory(result.metadata.deepAnalysis.casInfo.category) }}
                    </div>
                    <div v-if="result.metadata.deepAnalysis.casInfo.genders.length > 0" class="metadata-item">
                      <strong>Gender:</strong> {{ result.metadata.deepAnalysis.casInfo.genders.map(g => formatSubcategory(g)).join(', ') }}
                    </div>
                    <div v-if="result.metadata.deepAnalysis.casInfo.ageGroups.length > 0" class="metadata-item">
                      <strong>Age Groups:</strong> {{ result.metadata.deepAnalysis.casInfo.ageGroups.map(a => formatSubcategory(a)).join(', ') }}
                    </div>
                  </div>
                  
                  <!-- Object-specific info -->
                  <div v-if="result.metadata.deepAnalysis?.objectInfo" class="object-info mt-xs">
                    <div class="metadata-item">
                      <strong>Object Function:</strong> {{ formatSubcategory(result.metadata.deepAnalysis.objectInfo.function) }}
                    </div>
                    <div v-if="result.metadata.deepAnalysis.objectInfo.roomAssignment.length > 0" class="metadata-item">
                      <strong>Room Assignment:</strong> {{ result.metadata.deepAnalysis.objectInfo.roomAssignment.map(r => formatSubcategory(r)).join(', ') }}
                    </div>
                  </div>
                  
                  <!-- Script-specific info -->
                  <div v-if="result.metadata.deepAnalysis?.scriptInfo" class="script-info mt-xs">
                    <div class="metadata-item">
                      <strong>Script Type:</strong> {{ formatSubcategory(result.metadata.deepAnalysis.scriptInfo.category) }}
                      <span v-if="result.metadata.deepAnalysis.scriptInfo.isFramework" class="badge badge-primary ml-sm">Framework</span>
                    </div>
                    <div v-if="result.metadata.deepAnalysis.scriptInfo.gameplayAreas.length > 0" class="metadata-item">
                      <strong>Affects:</strong> {{ result.metadata.deepAnalysis.scriptInfo.gameplayAreas.join(', ') }}
                    </div>
                  </div>
                  
                  <!-- Suggested tags -->
                  <div v-if="result.metadata.deepAnalysis?.suggestedTags?.length > 0" class="metadata-item">
                    <strong>Suggested Tags:</strong>
                    <span v-for="tag in result.metadata.deepAnalysis.suggestedTags" :key="tag" class="badge badge-secondary ml-xs">
                      {{ tag }}
                    </span>
                  </div>
                </div>
                
                <div v-if="result.dependencies.length > 0" class="metadata-item">
                  <strong>Dependencies:</strong> {{ result.dependencies.join(', ') }}
                </div>
                <div v-if="result.conflicts.length > 0" class="metadata-item text-warning">
                  <strong>Potential conflicts:</strong> {{ result.conflicts.join(', ') }}
                </div>
              </div>
            </div>
            <button 
              class="btn btn-secondary btn-sm"
              @click="toggleFileExpanded(result.filePath)"
            >
              {{ expandedFiles.has(result.filePath) ? 'Collapse' : 'Expand' }}
            </button>
          </div>
        </div>

        <!-- Resources Table -->
        <div v-if="expandedFiles.has(result.filePath)" class="resources-table mt-md">
          <div class="table-container">
            <table class="table">
              <thead>
                <tr>
                  <th @click="setSortField('key')" class="sortable">
                    Resource Key
                    <span v-if="sortField === 'key'" class="sort-indicator">
                      {{ sortDirection === 'asc' ? '↑' : '↓' }}
                    </span>
                  </th>
                  <th @click="setSortField('type')" class="sortable">
                    Type
                    <span v-if="sortField === 'type'" class="sort-indicator">
                      {{ sortDirection === 'asc' ? '↑' : '↓' }}
                    </span>
                  </th>
                  <th @click="setSortField('group')" class="sortable">
                    Group
                    <span v-if="sortField === 'group'" class="sort-indicator">
                      {{ sortDirection === 'asc' ? '↑' : '↓' }}
                    </span>
                  </th>
                  <th @click="setSortField('instance')" class="sortable">
                    Instance
                    <span v-if="sortField === 'instance'" class="sort-indicator">
                      {{ sortDirection === 'asc' ? '↑' : '↓' }}
                    </span>
                  </th>
                  <th>Content</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="resource in getSortedResourcesForFile(result)" :key="resource.key">
                  <td class="table-mono">{{ resource.key }}</td>
                  <td>
                    <span class="resource-type">{{ resource.type }}</span>
                    <span v-if="resource.isOverride" class="badge badge-warning ml-sm">Override</span>
                  </td>
                  <td class="table-mono">{{ formatNumber(resource.group) }}</td>
                  <td class="table-mono">{{ resource.instance }}</td>
                  <td class="content-cell">{{ resource.contentSnippet }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="filteredResults.length === 0 && results.length > 0" class="empty-state text-center py-xl">
      <div class="text-muted">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" class="mb-md">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
        <p>No resources match your current filters.</p>
        <button class="btn btn-secondary btn-sm mt-sm" @click="clearFilters">
          Clear Filters
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';

// Props
import type { AnalyzedPackage, ResourceInfo } from '../../types/analysis';

const props = defineProps<{
  results: AnalyzedPackage[];
}>();

// Reactive state
const searchQuery = ref('');
const selectedTypeFilter = ref('');
const selectedFileFilter = ref('');
const sortField = ref<keyof ResourceInfo>('key');
const sortDirection = ref<'asc' | 'desc'>('asc');
const expandedFiles = ref(new Set<string>());

// Computed properties
const totalResources = computed(() => 
  props.results.reduce((total, result) => total + result.resources.length, 0)
);

const availableTypes = computed(() => {
  const types = new Set<string>();
  props.results.forEach(result => {
    result.resources.forEach(resource => {
      types.add(resource.type);
    });
  });
  return Array.from(types).sort();
});

const availableFiles = computed(() => 
  props.results.map(result => result.filePath).sort()
);

const hasActiveFilters = computed(() => 
  searchQuery.value || selectedTypeFilter.value || selectedFileFilter.value
);

const filteredResults = computed(() => {
  let filtered = props.results;
  
  // Filter by selected file
  if (selectedFileFilter.value) {
    filtered = filtered.filter(result => result.filePath === selectedFileFilter.value);
  }
  
  // Filter by search query or type
  if (searchQuery.value || selectedTypeFilter.value) {
    filtered = filtered.map(result => ({
      ...result,
      resources: result.resources.filter(resource => {
        const matchesSearch = !searchQuery.value || 
          resource.key.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          resource.type.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          resource.contentSnippet.toLowerCase().includes(searchQuery.value.toLowerCase());
          
        const matchesType = !selectedTypeFilter.value || 
          resource.type === selectedTypeFilter.value;
          
        return matchesSearch && matchesType;
      })
    })).filter(result => result.resources.length > 0);
  }
  
  return filtered;
});

// Methods
function getFileName(filePath: string): string {
  return filePath.split(/[/\\]/).pop() || filePath;
}

function getFilteredResourcesForFile(result: AnalyzedPackage): ResourceInfo[] {
  if (!searchQuery.value && !selectedTypeFilter.value) {
    return result.resources;
  }
  
  return result.resources.filter(resource => {
    const matchesSearch = !searchQuery.value || 
      resource.key.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      resource.type.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      resource.contentSnippet.toLowerCase().includes(searchQuery.value.toLowerCase());
      
    const matchesType = !selectedTypeFilter.value || 
      resource.type === selectedTypeFilter.value;
      
    return matchesSearch && matchesType;
  });
}

function getSortedResourcesForFile(result: AnalyzedPackage): ResourceInfo[] {
  const resources = getFilteredResourcesForFile(result);
  
  return [...resources].sort((a, b) => {
    let aVal = a[sortField.value];
    let bVal = b[sortField.value];
    
    // Handle numeric sorting for group
    if (sortField.value === 'group') {
      aVal = Number(aVal);
      bVal = Number(bVal);
    }
    
    // Convert to string for comparison
    const aStr = String(aVal).toLowerCase();
    const bStr = String(bVal).toLowerCase();
    
    const comparison = aStr.localeCompare(bStr, undefined, { numeric: true });
    return sortDirection.value === 'asc' ? comparison : -comparison;
  });
}

function setSortField(field: keyof ResourceInfo) {
  if (sortField.value === field) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    sortField.value = field;
    sortDirection.value = 'asc';
  }
}

function toggleFileExpanded(filePath: string) {
  if (expandedFiles.value.has(filePath)) {
    expandedFiles.value.delete(filePath);
  } else {
    expandedFiles.value.add(filePath);
  }
}

function clearFilters() {
  searchQuery.value = '';
  selectedTypeFilter.value = '';
  selectedFileFilter.value = '';
}

function formatNumber(num: number): string {
  return num.toLocaleString();
}

function exportResults(format: 'json' | 'csv') {
  if (format === 'json') {
    exportAsJSON();
  } else {
    exportAsCSV();
  }
}

function exportAsJSON() {
  const dataStr = JSON.stringify(filteredResults.value, null, 2);
  downloadFile(dataStr, 'simonitor-analysis.json', 'application/json');
}

function exportAsCSV() {
  const headers = ['File', 'Resource Key', 'Type', 'Group', 'Instance', 'Is Override', 'Content'];
  const rows = [headers];
  
  filteredResults.value.forEach(result => {
    getFilteredResourcesForFile(result).forEach(resource => {
      rows.push([
        getFileName(result.filePath),
        resource.key,
        resource.type,
        resource.group.toString(),
        resource.instance,
        resource.isOverride.toString(),
        resource.contentSnippet
      ]);
    });
  });
  
  const csvContent = rows.map(row => 
    row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
  ).join('\n');
  
  downloadFile(csvContent, 'simonitor-analysis.csv', 'text/csv');
}

function downloadFile(content: string, filename: string, mimeType: string) {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

// Helper functions for enhanced metadata display
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getCategoryDisplayName(category: string): string {
  const displayNames: Record<string, string> = {
    'cas_cc': 'CAS Custom Content',
    'build_buy_cc': 'Build/Buy Custom Content',
    'script_mod': 'Script Mod',
    'tuning_mod': 'Tuning Mod',
    'override': 'Override',
    'framework': 'Framework',
    'library': 'Library',
    'unknown': 'Unknown'
  };
  
  return displayNames[category] || category;
}

function formatSubcategory(subcategory: string): string {
  return subcategory
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function hasDeepAnalysis(result: AnalyzedPackage): boolean {
  return !!(result.metadata?.deepAnalysis && (
    result.metadata.deepAnalysis.casInfo ||
    result.metadata.deepAnalysis.objectInfo ||
    result.metadata.deepAnalysis.scriptInfo ||
    result.metadata.deepAnalysis.detailedDescription !== 'Unknown content' ||
    (result.metadata.deepAnalysis.suggestedTags && result.metadata.deepAnalysis.suggestedTags.length > 0)
  ));
}

// Auto-expand first file when results are loaded
if (props.results.length > 0) {
  expandedFiles.value.add(props.results[0].filePath);
}
</script>

<style scoped>
.results-header {
  margin-bottom: var(--spacing-lg);
}

.results-filters {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.file-result {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.file-header {
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.file-path {
  word-break: break-all;
}

.resources-table {
  background-color: var(--bg-primary);
}

.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.sortable:hover {
  background-color: var(--bg-tertiary);
}

.sort-indicator {
  margin-left: var(--spacing-xs);
  font-weight: bold;
}

.resource-type {
  font-weight: 500;
}

.content-cell {
  max-width: 300px;
  word-wrap: break-word;
}

.empty-state svg {
  color: var(--text-muted);
  margin: 0 auto;
}

.ml-sm {
  margin-left: var(--spacing-sm);
}

.file-metadata {
  background-color: var(--bg-tertiary);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--primary-color);
}

.metadata-item {
  margin-bottom: var(--spacing-xs);
}

.metadata-item:last-child {
  margin-bottom: 0;
}

.text-warning {
  color: var(--warning-color);
}
</style>