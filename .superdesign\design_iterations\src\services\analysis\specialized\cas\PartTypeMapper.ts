/**
 * CAS Part Type Mapper
 * 
 * Specialized component for mapping CAS Part type values to category system.
 * Handles the conversion from numeric part types to our structured categories.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring.
 */

import { CASCategory, BodyLocation, CAS_PART_TYPES, type CASPartInfo } from './types';

/**
 * Handles mapping of CAS Part type values to our category system
 */
export class PartTypeMapper {
    
    /**
     * Maps CAS Part type values to our category system
     * 
     * @param partTypeValue - The numeric part type value from SimData
     * @param casPartInfo - CAS part info object to populate
     */
    public static mapPartTypeToCASCategory(partTypeValue: any, casPartInfo: CASPartInfo): void {
        const numericValue = this.parsePartTypeValue(partTypeValue);
        
        if (numericValue === null) {
            console.warn('Invalid part type value:', partTypeValue);
            this.setDefaultPartType(casPartInfo);
            return;
        }
        
        this.mapPartTypeToCategory(numericValue, casPartInfo);
    }
    
    /**
     * Parses various input formats to numeric part type value
     * 
     * @param value - Input value in various formats
     * @returns Numeric value or null if parsing fails
     */
    private static parsePartTypeValue(value: any): number | null {
        if (typeof value === 'number') {
            return value;
        }
        
        if (typeof value === 'string') {
            const parsed = parseInt(value, 10);
            return isNaN(parsed) ? null : parsed;
        }
        
        // Handle object with value property
        if (value && typeof value.value !== 'undefined') {
            return this.parsePartTypeValue(value.value);
        }
        
        return null;
    }
    
    /**
     * Maps numeric part type to CAS category and sets related properties
     * 
     * @param partType - Numeric part type value
     * @param casPartInfo - CAS part info to populate
     */
    private static mapPartTypeToCategory(partType: number, casPartInfo: CASPartInfo): void {
        switch (partType) {
            case CAS_PART_TYPES.HAIR:
                this.setHairCategory(casPartInfo);
                break;
                
            case CAS_PART_TYPES.FACE_PAINT:
                this.setMakeupCategory(casPartInfo);
                break;
                
            case CAS_PART_TYPES.TOP:
                this.setTopClothingCategory(casPartInfo);
                break;
                
            case CAS_PART_TYPES.BOTTOM:
                this.setBottomClothingCategory(casPartInfo);
                break;
                
            case CAS_PART_TYPES.FULL_BODY:
                this.setFullBodyClothingCategory(casPartInfo);
                break;
                
            case CAS_PART_TYPES.SHOES:
                this.setShoesCategory(casPartInfo);
                break;
                
            case CAS_PART_TYPES.ACCESSORIES:
                this.setAccessoryCategory(casPartInfo);
                break;
                
            case CAS_PART_TYPES.MAKEUP:
                this.setMakeupCategory(casPartInfo);
                break;
                
            default:
                this.setDefaultClothingCategory(casPartInfo);
                break;
        }
    }
    
    /**
     * Sets hair category properties
     */
    private static setHairCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.category = CASCategory.HAIR;
        casPartInfo.isHair = true;
        casPartInfo.subcategory = 'hair';
        casPartInfo.bodyLocation.push(BodyLocation.HAIR);
    }
    
    /**
     * Sets makeup category properties
     */
    private static setMakeupCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.category = CASCategory.MAKEUP;
        casPartInfo.isMakeup = true;
        casPartInfo.subcategory = 'face_paint';
        casPartInfo.bodyLocation.push(BodyLocation.FACE);
    }
    
    /**
     * Sets top clothing category properties
     */
    private static setTopClothingCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.category = CASCategory.CLOTHING;
        casPartInfo.isClothing = true;
        casPartInfo.subcategory = 'tops';
        casPartInfo.bodyLocation.push(BodyLocation.UPPER_BODY);
    }
    
    /**
     * Sets bottom clothing category properties
     */
    private static setBottomClothingCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.category = CASCategory.CLOTHING;
        casPartInfo.isClothing = true;
        casPartInfo.subcategory = 'bottoms';
        casPartInfo.bodyLocation.push(BodyLocation.LOWER_BODY);
    }
    
    /**
     * Sets full body clothing category properties
     */
    private static setFullBodyClothingCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.category = CASCategory.CLOTHING;
        casPartInfo.isClothing = true;
        casPartInfo.subcategory = 'full_body';
        casPartInfo.bodyLocation.push(BodyLocation.FULL_BODY);
    }
    
    /**
     * Sets shoes category properties
     */
    private static setShoesCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.category = CASCategory.CLOTHING;
        casPartInfo.isClothing = true;
        casPartInfo.subcategory = 'shoes';
        casPartInfo.bodyLocation.push(BodyLocation.FEET);
    }
    
    /**
     * Sets accessory category properties
     */
    private static setAccessoryCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.category = CASCategory.ACCESSORIES;
        casPartInfo.isAccessory = true;
        casPartInfo.subcategory = 'accessories';
        // Body location will be determined by other fields
    }
    
    /**
     * Sets default clothing category when part type is unknown
     */
    private static setDefaultClothingCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.category = CASCategory.CLOTHING;
        casPartInfo.isClothing = true;
        casPartInfo.subcategory = 'clothing';
    }
    
    /**
     * Sets default part type when parsing fails
     */
    private static setDefaultPartType(casPartInfo: CASPartInfo): void {
        this.setDefaultClothingCategory(casPartInfo);
    }
    
    /**
     * Gets human-readable description for part type
     * 
     * @param partType - Numeric part type value
     * @returns Human-readable description
     */
    public static getPartTypeDescription(partType: number): string {
        const descriptions: Record<number, string> = {
            [CAS_PART_TYPES.HAIR]: 'Hair',
            [CAS_PART_TYPES.FACE_PAINT]: 'Face Paint/Makeup',
            [CAS_PART_TYPES.TOP]: 'Top Clothing',
            [CAS_PART_TYPES.BOTTOM]: 'Bottom Clothing',
            [CAS_PART_TYPES.FULL_BODY]: 'Full Body Clothing',
            [CAS_PART_TYPES.SHOES]: 'Shoes',
            [CAS_PART_TYPES.ACCESSORIES]: 'Accessories',
            [CAS_PART_TYPES.MAKEUP]: 'Makeup'
        };
        
        return descriptions[partType] || 'Unknown Part Type';
    }
}
