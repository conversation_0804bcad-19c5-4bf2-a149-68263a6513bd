/**
 * Hair Classification Test
 * 
 * Tests the enhanced hair style classification system to verify
 * that length, style, and texture detection is working correctly.
 */

import { HairStyleClassifier } from '../services/analysis/specialized/cas/HairStyleClassifier';
import { EnhancedCASDetector } from '../services/analysis/specialized/cas/EnhancedCASDetector';

/**
 * Test hair classification with various filename patterns
 */
async function testHairClassification(): Promise<void> {
    console.log('🎯 Testing Hair Style Classification System');
    console.log('======================================================================');
    
    // Test filenames from real hair mods
    const testFilenames = [
        'Aurum_HairstyleFMM_145_Daiki.package',
        'Aurum_HairstyleFMM_146_Lotus.package', 
        'Aurum_HairstyleFMM_153_Agatha.package',
        'Long_Curly_Ponytail_Hair.package',
        'Short_Bob_Straight_Hair.package',
        'Medium_Wavy_Bun_Hair.package',
        'Braided_Long_Hair_With_Bangs.package',
        'Pixie_Short_Hair.package',
        'Messy_Bun_Medium_Hair.package',
        'Side_Swept_Wavy_Hair.package'
    ];
    
    console.log('🔍 Testing Hair Style Detection:');
    console.log('');
    
    for (const filename of testFilenames) {
        console.log(`📁 Analyzing: ${filename}`);
        
        // Test CAS detection
        const isCAS = EnhancedCASDetector.isCASFilename(filename);
        const category = EnhancedCASDetector.getCASCategoryFromFilename(filename);
        
        console.log(`   🎯 Is CAS: ${isCAS}`);
        console.log(`   📂 Category: ${category}`);
        
        if (category === 'hair') {
            // Test detailed hair classification
            const hairInfo = HairStyleClassifier.classifyHairStyle(filename);
            const description = HairStyleClassifier.getHairDescription(hairInfo);
            
            console.log(`   📏 Length: ${hairInfo.length}`);
            console.log(`   💇 Style: ${hairInfo.style.join(', ')}`);
            console.log(`   🌊 Texture: ${hairInfo.texture}`);
            console.log(`   ✨ Has Accessories: ${hairInfo.hasAccessories}`);
            console.log(`   🎯 Confidence: ${(hairInfo.confidence * 100).toFixed(1)}%`);
            console.log(`   🔍 Detection Method: ${hairInfo.detectionMethod}`);
            console.log(`   🏷️ Keywords: ${hairInfo.keywords.join(', ')}`);
            console.log(`   📝 Description: ${description}`);
        }
        
        console.log('');
    }
    
    // Test enhanced CAS detection
    console.log('🔍 Testing Enhanced CAS Detection:');
    console.log('');
    
    const testCASParts = EnhancedCASDetector.detectCASFromFilename(
        'Aurum_HairstyleFMM_145_Daiki.package', 
        []
    );
    
    if (testCASParts.length > 0) {
        const casPartInfo = testCASParts[0];
        console.log('📊 Enhanced CAS Part Info:');
        console.log(`   📂 Category: ${casPartInfo.category}`);
        console.log(`   🏷️ Subcategory: ${casPartInfo.subcategory}`);
        console.log(`   📝 Description: ${casPartInfo.description}`);
        console.log(`   💇 Is Hair: ${casPartInfo.isHair}`);
        console.log(`   👗 Is Clothing: ${casPartInfo.isClothing}`);
        console.log(`   💄 Is Makeup: ${casPartInfo.isMakeup}`);
        console.log(`   💍 Is Accessory: ${casPartInfo.isAccessory}`);
        
        if (casPartInfo.hairDetails) {
            console.log('   🎨 Hair Details:');
            console.log(`      📏 Length: ${casPartInfo.hairDetails.length}`);
            console.log(`      💇 Style: ${casPartInfo.hairDetails.style.join(', ')}`);
            console.log(`      🌊 Texture: ${casPartInfo.hairDetails.texture}`);
            console.log(`      ✨ Has Accessories: ${casPartInfo.hairDetails.hasAccessories}`);
            console.log(`      🎯 Confidence: ${(casPartInfo.hairDetails.confidence * 100).toFixed(1)}%`);
        }
        
        console.log(`   🏷️ Tags: ${casPartInfo.tags.join(', ')}`);
    }
    
    console.log('');
    console.log('✅ Hair Classification Test Complete!');
    console.log('🎯 Enhanced subcategory detection is working!');
}

// Run the test
testHairClassification().catch(console.error);
