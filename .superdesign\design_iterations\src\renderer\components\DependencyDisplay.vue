<template>
  <div class="dependency-display">
    <!-- Dependencies List -->
    <div v-if="dependencies.dependencies?.length > 0" class="dependencies-section">
      <h6 class="section-title">Dependencies ({{ dependencies.dependencies.length }})</h6>
      <div class="dependencies-list">
        <div 
          v-for="dependency in dependencies.dependencies" 
          :key="dependency"
          class="dependency-item"
        >
          <LinkIcon class="dependency-icon" />
          <code class="dependency-name">{{ dependency }}</code>
        </div>
      </div>
    </div>
    
    <!-- Conflicts -->
    <div v-if="dependencies.conflicts?.length > 0" class="conflicts-section">
      <h6 class="section-title conflicts-title">
        <ExclamationTriangleIcon class="title-icon" />
        Conflicts ({{ dependencies.conflicts.length }})
      </h6>
      <div class="conflicts-list">
        <div 
          v-for="conflict in dependencies.conflicts" 
          :key="conflict"
          class="conflict-item"
        >
          <ExclamationTriangleIcon class="conflict-icon" />
          <span class="conflict-text">{{ conflict }}</span>
        </div>
      </div>
    </div>
    
    <!-- Risk Level -->
    <div class="risk-section">
      <div class="risk-indicator" :class="`risk-${dependencies.riskLevel}`">
        <ShieldCheckIcon v-if="dependencies.riskLevel === 'low'" class="risk-icon" />
        <ShieldExclamationIcon v-else-if="dependencies.riskLevel === 'medium'" class="risk-icon" />
        <ExclamationTriangleIcon v-else class="risk-icon" />
        <div class="risk-content">
          <div class="risk-level">{{ formatRiskLevel(dependencies.riskLevel) }}</div>
          <div class="risk-description">{{ getRiskDescription(dependencies.riskLevel) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  LinkIcon, 
  ExclamationTriangleIcon, 
  ShieldCheckIcon, 
  ShieldExclamationIcon 
} from '@heroicons/vue/24/outline';

interface Dependencies {
  dependencies?: string[];
  conflicts?: string[];
  riskLevel: string;
}

defineProps<{
  dependencies: Dependencies;
}>();

const formatRiskLevel = (level: string): string => {
  const levels: Record<string, string> = {
    'low': 'Low Risk',
    'medium': 'Medium Risk',
    'high': 'High Risk'
  };
  return levels[level] || level;
};

const getRiskDescription = (level: string): string => {
  const descriptions: Record<string, string> = {
    'low': 'Safe to use with minimal conflicts',
    'medium': 'May have some compatibility issues',
    'high': 'Potential for significant conflicts'
  };
  return descriptions[level] || 'Unknown risk level';
};
</script>

<style scoped>
.dependency-display {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.section-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.conflicts-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--warning);
}

.title-icon {
  width: 16px;
  height: 16px;
}

/* Dependencies */
.dependencies-section {
  background: var(--info-bg);
  border: 1px solid var(--info-border);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.dependencies-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.dependency-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.dependency-icon {
  width: 16px;
  height: 16px;
  color: var(--info);
  flex-shrink: 0;
}

.dependency-name {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--text-primary);
  background: none;
  padding: 0;
}

/* Conflicts */
.conflicts-section {
  background: var(--warning-bg);
  border: 1px solid var(--warning-border);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.conflicts-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.conflict-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.conflict-icon {
  width: 16px;
  height: 16px;
  color: var(--warning);
  flex-shrink: 0;
}

.conflict-text {
  font-size: var(--text-sm);
  color: var(--text-primary);
}

/* Risk */
.risk-section {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.risk-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.risk-icon {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.risk-indicator.risk-low .risk-icon {
  color: var(--success);
}

.risk-indicator.risk-medium .risk-icon {
  color: var(--warning);
}

.risk-indicator.risk-high .risk-icon {
  color: var(--error);
}

.risk-content {
  flex: 1;
}

.risk-level {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
}

.risk-indicator.risk-low .risk-level {
  color: var(--success);
}

.risk-indicator.risk-medium .risk-level {
  color: var(--warning);
}

.risk-indicator.risk-high .risk-level {
  color: var(--error);
}

.risk-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}
</style>
