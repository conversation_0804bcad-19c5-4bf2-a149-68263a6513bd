import type { AnalyzedPackage } from './analysis';
import { ModCategory } from './analysis';
import type { DependencyInfo } from '../services/analysis/specialized/common/DependencyAnalyzer';
import type { ResourceIntelligence } from '../services/analysis/specialized/common/ResourceIntelligenceAnalyzer';
import type { QualityAssessment } from '../services/analysis/specialized/common/QualityAssessmentAnalyzer';
import type { AggregatedMetadata, ExtractionResult } from '../services/analysis/metadata/MetadataAggregator';

/**
 * Quick analysis result for fast UI feedback
 */
export interface QuickAnalysisResult {
    category: ModCategory;
    resourceCount: number;
    hasConflictPotential: boolean;
    needsDeepAnalysis: boolean;
    resourceTypes: number[];
}

/**
 * Enhanced quick analysis result with additional S4TK data
 */
export interface EnhancedQuickAnalysisResult extends QuickAnalysisResult {
    compressionStats: Record<string, number>;
    validationIssues: string[];
    s4tkVersion: string;
    processingTime: number;
}

/**
 * Detailed analysis result for comprehensive mod information
 */
export interface DetailedAnalysisResult extends AnalyzedPackage {
    // Additional detailed analysis data can be added here
}

/**
 * Enhanced detailed analysis result with S4TK improvements and intelligence data
 */
export interface EnhancedDetailedAnalysisResult extends DetailedAnalysisResult {
    specializedResources: any[];
    resourceValidation: any;
    performanceMetrics: {
        totalTime: number;
        quickAnalysisTime: number;
        detailedAnalysisTime: number;
        resourceCount: number;
        memoryUsage?: number;
    };
    intelligence?: {
        dependencies: DependencyInfo;
        resourceIntelligence: ResourceIntelligence;
        qualityAssessment: QualityAssessment;
    };

    // Enhanced metadata extraction results
    enhancedMetadata?: AggregatedMetadata;

    // File system metadata
    fileMetadata?: {
        size: number;
        lastModified: Date;
        createdAt: Date;
    };
}

/**
 * Async operation cancellation token
 */
export interface CancellationToken {
    isCancelled: boolean;
    cancel(): void;
}

/**
 * Batch analysis options
 */
export interface BatchAnalysisOptions {
    concurrency?: number;
    onProgress?: (completed: number, total: number) => void;
    cancellationToken?: CancellationToken;
}

/**
 * Conflict analysis result
 */
export interface ConflictAnalysisResult {
    filePath: string;
    tgiSignatures: Array<{ 
        type: number; 
        group: number; 
        instance: bigint; 
        hash?: string;
        formattedKey?: string;
        tgiString?: string;
    }>;
    hasConflicts: boolean;
    validationIssues: string[];
}