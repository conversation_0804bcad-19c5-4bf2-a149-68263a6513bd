/**
 * Test Enhanced Object Categorization
 * 
 * Tests the new enhanced object classification system to verify
 * it correctly categorizes furniture like beds, desks, chairs, etc.
 */

import { EnhancedObjectClassifier } from '../services/analysis/specialized/objects/EnhancedObjectClassifier';

/**
 * Test cases for enhanced categorization
 */
const TEST_CASES = [
    // Beds
    { filename: 'FAIRYLICIOUS Castle Bedframe.package', expected: { category: 'furniture', subcategory: 'bed', specificType: 'bed' } },
    { filename: 'Modern Double Bed.package', expected: { category: 'furniture', subcategory: 'bed', specificType: 'double bed' } },
    { filename: 'Kids Bunk Bed.package', expected: { category: 'furniture', subcategory: 'bed', specificType: 'bunk bed' } },
    
    // Desks & Tables
    { filename: 'FAIRYLICIOUS Desk.package', expected: { category: 'furniture', subcategory: 'table', specificType: 'desk' } },
    { filename: 'Office Computer Desk.package', expected: { category: 'furniture', subcategory: 'table', specificType: 'desk' } },
    { filename: 'Dining Table Set.package', expected: { category: 'furniture', subcategory: 'table', specificType: 'dining table' } },
    { filename: 'Coffee Table Modern.package', expected: { category: 'furniture', subcategory: 'table', specificType: 'coffee table' } },
    
    // Seating
    { filename: 'Comfortable Armchair.package', expected: { category: 'furniture', subcategory: 'seating', specificType: 'armchair' } },
    { filename: 'Living Room Sofa.package', expected: { category: 'furniture', subcategory: 'seating', specificType: 'sofa' } },
    { filename: 'Dining Chair Set.package', expected: { category: 'furniture', subcategory: 'seating', specificType: 'dining chair' } },
    { filename: 'Office Chair Ergonomic.package', expected: { category: 'furniture', subcategory: 'seating', specificType: 'office chair' } },
    
    // Storage
    { filename: 'Bedroom Dresser.package', expected: { category: 'furniture', subcategory: 'storage', specificType: 'storage' } },
    { filename: 'Walk-in Closet.package', expected: { category: 'furniture', subcategory: 'storage', specificType: 'storage' } },
    { filename: 'Bookshelf Large.package', expected: { category: 'furniture', subcategory: 'storage', specificType: 'storage' } },
    
    // Electronics
    { filename: 'Smart TV 65 inch.package', expected: { category: 'electronics', subcategory: 'entertainment', specificType: 'entertainment' } },
    { filename: 'Gaming Computer Setup.package', expected: { category: 'electronics', subcategory: 'entertainment', specificType: 'entertainment' } },
    { filename: 'Stereo System.package', expected: { category: 'electronics', subcategory: 'entertainment', specificType: 'entertainment' } },
    
    // Appliances
    { filename: 'Kitchen Refrigerator.package', expected: { category: 'appliances', subcategory: 'kitchen', specificType: 'kitchen' } },
    { filename: 'Stove and Oven Combo.package', expected: { category: 'appliances', subcategory: 'kitchen', specificType: 'kitchen' } },
    { filename: 'Microwave Modern.package', expected: { category: 'appliances', subcategory: 'kitchen', specificType: 'kitchen' } },
    
    // Plumbing
    { filename: 'Bathroom Toilet.package', expected: { category: 'plumbing', subcategory: 'bathroom', specificType: 'bathroom' } },
    { filename: 'Shower Bathtub Combo.package', expected: { category: 'plumbing', subcategory: 'bathroom', specificType: 'bathroom' } },
    { filename: 'Kitchen Sink.package', expected: { category: 'plumbing', subcategory: 'bathroom', specificType: 'bathroom' } },
    
    // Lighting
    { filename: 'Ceiling Chandelier.package', expected: { category: 'lighting', subcategory: 'light', specificType: 'light' } },
    { filename: 'Floor Lamp Modern.package', expected: { category: 'lighting', subcategory: 'light', specificType: 'light' } },
    { filename: 'Table Lamp Bedside.package', expected: { category: 'lighting', subcategory: 'light', specificType: 'light' } },
    
    // Decorations
    { filename: 'Wall Art Painting.package', expected: { category: 'decorations', subcategory: 'decoration', specificType: 'decoration' } },
    { filename: 'Indoor Plant Large.package', expected: { category: 'decorations', subcategory: 'decoration', specificType: 'decoration' } },
    { filename: 'Mirror Bathroom.package', expected: { category: 'decorations', subcategory: 'decoration', specificType: 'decoration' } },
    
    // Outdoor
    { filename: 'Outdoor Grill BBQ.package', expected: { category: 'outdoor', subcategory: 'outdoor', specificType: 'outdoor' } },
    { filename: 'Pool Swimming.package', expected: { category: 'outdoor', subcategory: 'outdoor', specificType: 'outdoor' } },
    { filename: 'Garden Fence.package', expected: { category: 'outdoor', subcategory: 'outdoor', specificType: 'outdoor' } }
];

/**
 * Run categorization tests
 */
function runCategorizationTests(): void {
    console.log('🧪 Testing Enhanced Object Categorization System\n');
    console.log('=' .repeat(80));
    
    let totalTests = 0;
    let passedTests = 0;
    let failedTests: any[] = [];
    
    for (const testCase of TEST_CASES) {
        totalTests++;
        
        try {
            const result = EnhancedObjectClassifier.classifyObject(testCase.filename);
            
            const categoryMatch = result.category === testCase.expected.category;
            const subcategoryMatch = result.subcategory === testCase.expected.subcategory;
            const specificTypeMatch = result.specificType === testCase.expected.specificType;
            
            const passed = categoryMatch && subcategoryMatch;
            
            if (passed) {
                passedTests++;
                console.log(`✅ ${testCase.filename}`);
                console.log(`   Category: ${result.category} | Subcategory: ${result.subcategory} | Type: ${result.specificType}`);
                console.log(`   Confidence: ${(result.confidence * 100).toFixed(1)}% | Method: ${result.detectionMethod}`);
                console.log(`   Room Types: ${result.roomTypes.join(', ')}`);
                console.log(`   Tags: ${result.tags.slice(0, 3).join(', ')}${result.tags.length > 3 ? '...' : ''}`);
            } else {
                failedTests.push({
                    filename: testCase.filename,
                    expected: testCase.expected,
                    actual: {
                        category: result.category,
                        subcategory: result.subcategory,
                        specificType: result.specificType
                    },
                    confidence: result.confidence
                });
                console.log(`❌ ${testCase.filename}`);
                console.log(`   Expected: ${testCase.expected.category}/${testCase.expected.subcategory}`);
                console.log(`   Actual: ${result.category}/${result.subcategory}`);
                console.log(`   Confidence: ${(result.confidence * 100).toFixed(1)}%`);
            }
            console.log('');
            
        } catch (error) {
            totalTests++;
            console.log(`💥 ${testCase.filename} - ERROR: ${error}`);
            failedTests.push({
                filename: testCase.filename,
                error: error
            });
        }
    }
    
    // Summary
    console.log('=' .repeat(80));
    console.log('📊 TEST SUMMARY');
    console.log('=' .repeat(80));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} (${((passedTests / totalTests) * 100).toFixed(1)}%)`);
    console.log(`Failed: ${failedTests.length} (${((failedTests.length / totalTests) * 100).toFixed(1)}%)`);
    
    if (failedTests.length > 0) {
        console.log('\n❌ FAILED TESTS:');
        failedTests.forEach(failure => {
            console.log(`   • ${failure.filename}`);
            if (failure.error) {
                console.log(`     Error: ${failure.error}`);
            } else {
                console.log(`     Expected: ${failure.expected.category}/${failure.expected.subcategory}`);
                console.log(`     Actual: ${failure.actual.category}/${failure.actual.subcategory}`);
            }
        });
    }
    
    console.log('\n🎯 ACCURACY ANALYSIS:');
    console.log(`   Category Detection: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`   Overall Performance: ${passedTests >= totalTests * 0.8 ? '🟢 EXCELLENT' : passedTests >= totalTests * 0.6 ? '🟡 GOOD' : '🔴 NEEDS IMPROVEMENT'}`);
    
    if (passedTests >= totalTests * 0.8) {
        console.log('\n🎉 Enhanced categorization system is working excellently!');
        console.log('   The system should now correctly identify:');
        console.log('   • Beds as "Furniture > Bed" instead of generic categories');
        console.log('   • Desks as "Furniture > Desk" with proper room assignments');
        console.log('   • Chairs, sofas, and seating with specific types');
        console.log('   • Kitchen appliances, electronics, and plumbing fixtures');
        console.log('   • Proper room type assignments and functionality detection');
    }
}

// Run the tests
if (require.main === module) {
    runCategorizationTests();
}

export { runCategorizationTests };
