/**
 * Script ZIP File Analyzer
 * 
 * Specialized component for analyzing ZIP archive content in .ts4script files.
 * Most script mods are packaged as ZIP archives containing Python files.
 * 
 * Extracted from ScriptAnalyzer.ts as part of Phase 2 refactoring.
 */

import { ZIP_SIGNATURE, type ScriptModInfo } from './types';
import { MetadataExtractor } from './MetadataExtractor';
import { GameplayAreaDetector } from './GameplayAreaDetector';

/**
 * Handles analysis of ZIP archive content in script files
 */
export class ZipFileAnalyzer {
    
    /**
     * Checks if the buffer contains a ZIP file
     * 
     * @param buffer - Buffer to check
     * @returns true if buffer contains ZIP file signature
     */
    public static isZipFile(buffer: Buffer): boolean {
        if (buffer.length < 2) {
            return false;
        }
        
        // ZIP files start with 'PK' (0x504B)
        return buffer[0] === ZIP_SIGNATURE.HEADER[0] && 
               buffer[1] === ZIP_SIGNATURE.HEADER[1];
    }
    
    /**
     * Analyzes ZIP archive content to extract script information
     * 
     * @param buffer - ZIP file buffer
     * @param scriptInfo - Script info object to populate
     * @returns Array of Python file names found in the archive
     */
    public static analyzeZipContent(buffer: <PERSON>uffer, scriptInfo: ScriptModInfo): string[] {
        try {
            if (!this.isZipFile(buffer)) {
                console.warn('Buffer does not contain a valid ZIP file');
                return [];
            }
            
            // Convert buffer to binary string for pattern matching
            const bufferStr = buffer.toString('binary');
            
            // Extract Python file names
            const pythonFiles = this.extractPythonFileNames(bufferStr);
            
            // PHASE 3A: Extract metadata from manifest files first (high priority)
            const manifestMetadata = this.extractManifestMetadata(bufferStr);
            if (manifestMetadata.author && !scriptInfo.author) scriptInfo.author = manifestMetadata.author;
            if (manifestMetadata.version && !scriptInfo.version) scriptInfo.version = manifestMetadata.version;

            // Extract metadata from ZIP content (Python files, etc.)
            MetadataExtractor.extractFromZipContent(bufferStr, scriptInfo);

            // Detect gameplay areas from ZIP content
            GameplayAreaDetector.detectFromContent(bufferStr, scriptInfo);
            
            return pythonFiles;
            
        } catch (error) {
            console.warn('Error analyzing ZIP content:', error);
            return [];
        }
    }
    
    /**
     * Extracts Python file names from ZIP central directory
     * 
     * @param bufferStr - ZIP content as binary string
     * @returns Array of unique Python file names
     */
    private static extractPythonFileNames(bufferStr: string): string[] {
        try {
            // Look for .py files in the ZIP central directory
            // This is a simplified approach - a full implementation would parse the ZIP structure
            const pyFileMatches = bufferStr.match(/[\w\-_]+\.py/g);
            
            if (!pyFileMatches) {
                return [];
            }
            
            // Remove duplicates and filter out invalid matches
            const pythonFiles = [...new Set(pyFileMatches)]
                .filter(filename => this.isValidPythonFileName(filename));
            
            return pythonFiles;
            
        } catch (error) {
            console.warn('Error extracting Python file names:', error);
            return [];
        }
    }
    
    /**
     * Validates if a filename is a valid Python file name
     * 
     * @param filename - Filename to validate
     * @returns true if filename appears to be a valid Python file
     */
    private static isValidPythonFileName(filename: string): boolean {
        // Basic validation for Python file names
        return filename.length > 3 && 
               filename.endsWith('.py') &&
               /^[\w\-_]+\.py$/.test(filename);
    }
    
    /**
     * Estimates the complexity of the script based on ZIP content
     * 
     * @param pythonFiles - Array of Python file names
     * @param bufferSize - Size of the ZIP buffer
     * @returns Complexity estimate ('low', 'medium', 'high')
     */
    public static estimateComplexity(pythonFiles: string[], bufferSize: number): string {
        // Simple heuristics for complexity estimation
        if (pythonFiles.length === 0) {
            return 'unknown';
        }
        
        if (pythonFiles.length === 1 && bufferSize < 10000) {
            return 'low';
        }
        
        if (pythonFiles.length <= 3 && bufferSize < 50000) {
            return 'medium';
        }
        
        return 'high';
    }
    
    /**
     * Checks if ZIP contains common framework indicators
     * 
     * @param bufferStr - ZIP content as binary string
     * @returns true if ZIP appears to contain framework code
     */
    public static containsFrameworkIndicators(bufferStr: string): boolean {
        const frameworkIndicators = [
            'mc_command_center',
            'xml_injector',
            'ui_cheats_extension',
            'framework',
            'core_library'
        ];
        
        return frameworkIndicators.some(indicator => 
            bufferStr.toLowerCase().includes(indicator)
        );
    }
    
    /**
     * Extracts directory structure information from ZIP
     * 
     * @param bufferStr - ZIP content as binary string
     * @returns Information about the directory structure
     */
    public static analyzeDirectoryStructure(bufferStr: string): {
        hasSubdirectories: boolean;
        estimatedFileCount: number;
        hasConfigFiles: boolean;
    } {
        // Look for directory separators
        const hasSubdirectories = bufferStr.includes('/') || bufferStr.includes('\\');
        
        // Estimate file count based on file extension patterns
        const fileExtensions = ['.py', '.txt', '.md', '.json', '.xml'];
        let estimatedFileCount = 0;
        
        fileExtensions.forEach(ext => {
            const matches = bufferStr.match(new RegExp(`\\w+\\${ext}`, 'g'));
            if (matches) {
                estimatedFileCount += matches.length;
            }
        });
        
        // Check for configuration files
        const configPatterns = ['.json', '.xml', '.ini', '.cfg', 'config'];
        const hasConfigFiles = configPatterns.some(pattern => 
            bufferStr.toLowerCase().includes(pattern)
        );
        
        return {
            hasSubdirectories,
            estimatedFileCount,
            hasConfigFiles
        };
    }
    
    /**
     * Validates ZIP file integrity (basic check)
     * 
     * @param buffer - ZIP file buffer
     * @returns true if ZIP appears to be valid
     */
    public static isValidZipFile(buffer: Buffer): boolean {
        if (!this.isZipFile(buffer)) {
            return false;
        }
        
        // Basic integrity check - look for end of central directory signature
        // This is a simplified check - full validation would require complete ZIP parsing
        const endSignature = Buffer.from([0x50, 0x4B, 0x05, 0x06]);
        
        // Search for end signature in the last 1KB of the file
        const searchStart = Math.max(0, buffer.length - 1024);
        const searchBuffer = buffer.slice(searchStart);
        
        return searchBuffer.includes(endSignature);
    }

    /**
     * Extracts metadata from manifest files within ZIP archives
     *
     * @param bufferStr - ZIP content as binary string
     * @returns Extracted metadata from manifest files
     */
    public static extractManifestMetadata(bufferStr: string): {
        author?: string;
        version?: string;
        modName?: string;
        description?: string;
    } {
        const metadata: any = {};

        try {
            // Look for manifest file patterns (prioritize Llama-Logic standard)
            const manifestPatterns = [
                // PRIORITY 1: Llama-Logic standard manifest (highest confidence)
                /llamalogic\.modfilemanifest\.yml/i,
                // PRIORITY 2: Other common manifest patterns
                /manifest\.json/i,
                /mod_info\.json/i,
                /package\.json/i,
                /info\.txt/i,
                /readme\.txt/i,
                /readme\.md/i,
                /mod\.json/i
            ];

            // Search for manifest files and extract their content
            for (const pattern of manifestPatterns) {
                const manifestContent = this.extractFileContentByPattern(bufferStr, pattern);
                if (manifestContent) {
                    const parsedMetadata = this.parseManifestContent(manifestContent);
                    Object.assign(metadata, parsedMetadata);

                    // If we found good metadata, prioritize it
                    if (parsedMetadata.author || parsedMetadata.version) {
                        break;
                    }
                }
            }

            // Also look for Python files with rich metadata
            if (!metadata.author || !metadata.version) {
                const pythonMetadata = this.extractPythonFileMetadata(bufferStr);
                if (!metadata.author && pythonMetadata.author) metadata.author = pythonMetadata.author;
                if (!metadata.version && pythonMetadata.version) metadata.version = pythonMetadata.version;
            }

        } catch (error) {
            console.warn('Error extracting manifest metadata:', error);
        }

        return metadata;
    }

    /**
     * Extracts file content by pattern from ZIP
     *
     * @param bufferStr - ZIP content as binary string
     * @param pattern - Pattern to match file names
     * @returns File content if found
     */
    private static extractFileContentByPattern(bufferStr: string, pattern: RegExp): string | null {
        try {
            // This is a simplified approach - in a full implementation we'd parse the ZIP structure
            // Look for the pattern in the buffer and try to extract nearby content
            const match = bufferStr.match(pattern);
            if (!match) return null;

            const matchIndex = match.index!;

            // Try to find content after the filename
            // Look for common file content patterns
            const contentStart = bufferStr.indexOf('\n', matchIndex);
            if (contentStart === -1) return null;

            // Extract up to 2KB of content (reasonable for manifest files)
            const contentEnd = Math.min(contentStart + 2048, bufferStr.length);
            const content = bufferStr.substring(contentStart + 1, contentEnd);

            // Basic validation - should contain some text
            if (content.length < 10 || !/[a-zA-Z]/.test(content)) return null;

            return content;

        } catch (error) {
            return null;
        }
    }

    /**
     * Parses manifest file content for metadata
     *
     * @param content - Manifest file content
     * @returns Parsed metadata
     */
    private static parseManifestContent(content: string): any {
        const metadata: any = {};

        try {
            // Try YAML parsing first (Llama-Logic format)
            if (content.includes('Name:') || content.includes('Creators:') || content.includes('Version:')) {
                const yamlMetadata = this.parseLlamaLogicYAML(content);
                if (yamlMetadata.author || yamlMetadata.version) {
                    return yamlMetadata;
                }
            }

            // Try JSON parsing
            if (content.trim().startsWith('{')) {
                const json = JSON.parse(content);
                if (json.author) metadata.author = json.author;
                if (json.version) metadata.version = json.version;
                if (json.name) metadata.modName = json.name;
                if (json.description) metadata.description = json.description;
                return metadata;
            }
        } catch {
            // Fall back to text parsing
        }

        // Text-based parsing for README files, etc.
        const lines = content.split('\n');
        for (const line of lines) {
            const cleanLine = line.trim();

            // Author patterns
            const authorMatch = cleanLine.match(/(?:author|creator|made by|by):?\s*(.+)/i);
            if (authorMatch && !metadata.author) {
                metadata.author = authorMatch[1].trim().replace(/['"]/g, '');
            }

            // Version patterns
            const versionMatch = cleanLine.match(/(?:version|ver):?\s*(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/i);
            if (versionMatch && !metadata.version) {
                metadata.version = versionMatch[1];
            }

            // Name patterns
            const nameMatch = cleanLine.match(/(?:name|title|mod name):?\s*(.+)/i);
            if (nameMatch && !metadata.modName) {
                metadata.modName = nameMatch[1].trim().replace(/['"]/g, '');
            }
        }

        return metadata;
    }

    /**
     * Extracts metadata from Python files within ZIP
     *
     * @param bufferStr - ZIP content as binary string
     * @returns Metadata from Python files
     */
    private static extractPythonFileMetadata(bufferStr: string): any {
        const metadata: any = {};

        try {
            // Look for Python metadata patterns in the ZIP content
            // This searches the entire ZIP content for Python-style metadata

            // Author patterns
            const authorPatterns = [
                /__author__\s*=\s*['"](.+?)['"]/,
                /# Author:\s*(.+?)$/m,
                /# Created by:\s*(.+?)$/m,
                /"""[\s\S]*?Author:\s*(.+?)$/m
            ];

            for (const pattern of authorPatterns) {
                const match = bufferStr.match(pattern);
                if (match && match[1] && !metadata.author) {
                    metadata.author = match[1].trim();
                    break;
                }
            }

            // Version patterns
            const versionPatterns = [
                /__version__\s*=\s*['"](.+?)['"]/,
                /# Version:\s*(.+?)$/m,
                /# v(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/m,
                /"""[\s\S]*?Version:\s*(.+?)$/m
            ];

            for (const pattern of versionPatterns) {
                const match = bufferStr.match(pattern);
                if (match && match[1] && !metadata.version) {
                    metadata.version = match[1].trim();
                    break;
                }
            }

        } catch (error) {
            console.warn('Error extracting Python metadata:', error);
        }

        return metadata;
    }

    /**
     * Parses Llama-Logic YAML manifest content
     *
     * @param content - YAML manifest content
     * @returns Parsed metadata
     */
    private static parseLlamaLogicYAML(content: string): any {
        const metadata: any = {};

        try {
            // Simple YAML parsing for Llama-Logic manifest format
            const lines = content.split('\n');

            for (const line of lines) {
                const trimmedLine = line.trim();

                // Name field
                const nameMatch = trimmedLine.match(/^Name:\s*(.+)$/);
                if (nameMatch && !metadata.modName) {
                    metadata.modName = nameMatch[1].trim().replace(/['"]/g, '');
                }

                // Version field
                const versionMatch = trimmedLine.match(/^Version:\s*(.+)$/);
                if (versionMatch && !metadata.version) {
                    metadata.version = versionMatch[1].trim().replace(/['"]/g, '');
                }

                // Description field
                const descMatch = trimmedLine.match(/^Description:\s*(.+)$/);
                if (descMatch && !metadata.description) {
                    metadata.description = descMatch[1].trim().replace(/['"]/g, '');
                }

                // Creators field (look for first creator in list)
                if (trimmedLine.startsWith('Creators:') && !metadata.author) {
                    // Look for the next line with a list item
                    const nextLineIndex = lines.indexOf(line) + 1;
                    if (nextLineIndex < lines.length) {
                        const nextLine = lines[nextLineIndex].trim();
                        const creatorMatch = nextLine.match(/^-\s*(.+)$/);
                        if (creatorMatch) {
                            metadata.author = creatorMatch[1].trim().replace(/['"]/g, '');
                        }
                    }
                }

                // Single-line creator format
                const creatorMatch = trimmedLine.match(/^Creators:\s*\[\s*"?([^"]+)"?\s*\]/);
                if (creatorMatch && !metadata.author) {
                    metadata.author = creatorMatch[1].trim();
                }
            }

        } catch (error) {
            console.warn('Error parsing Llama-Logic YAML:', error);
        }

        return metadata;
    }
}
