/**
 * Object Analyzer
 * 
 * Specialized analyzer for Build/Buy objects and furniture.
 * Analyzes object definitions, catalogs, and related resources.
 */

import type { ResourceEntry } from '@s4tk/models/types';
import type { IResourceProcessor, ProcessedResource, ResourceProcessingOptions } from '../types';
import { ResourceTypeHelpers, URT } from '../../../../constants/unifiedResourceTypes';

/**
 * Information extracted from object resources
 */
export interface ObjectInfo {
    objectType: string;
    category: string;
    subcategory?: string;
    price?: number;
    catalogFlags?: number[];
    roomFlags?: number[];
    functionFlags?: number[];
    buyDebugFlags?: number[];
    buildDebugFlags?: number[];
    isCustomContent: boolean;
    hasCustomStrings: boolean;
    hasCustomThumbnails: boolean;
    meshCount: number;
    textureCount: number;
    animationCount: number;
    slotCount: number;
    footprintSize?: { width: number; height: number };
    tags: string[];
    description?: string;
}

/**
 * Analyzer for Build/Buy objects and furniture
 */
export class ObjectAnalyzer implements IResourceProcessor {
    
    canProcess(resourceType: number): boolean {
        return ResourceTypeHelpers.isBuildBuyResource(resourceType) ||
               resourceType === URT.ObjectDefinition ||
               resourceType === URT.ObjectCatalog ||
               resourceType === URT.ObjectCatalogSet ||
               resourceType === URT.Footprint ||
               resourceType === URT.Slot;
    }
    
    async process(entry: ResourceEntry, options?: ResourceProcessingOptions): Promise<ProcessedResource> {
        try {
            const objectInfo = await this.analyzeObject(entry);
            
            const result: ProcessedResource = {
                id: entry.id?.toString() || `object-${entry.key.type}-${entry.key.instance}`,
                type: this.getObjectTypeName(entry.key.type),
                size: this.getResourceSize(entry.value),
                compressed: this.isCompressed(entry.value),
                metadata: {
                    objectInfo,
                    resourceType: entry.key.type,
                    group: entry.key.group,
                    instance: entry.key.instance,
                    specialized: true,
                    processorUsed: this.getProcessorName()
                },
                issues: []
            };
            
            // Add validation issues
            this.validateObject(objectInfo, result);
            
            return result;
            
        } catch (error) {
            return this.createErrorResult(entry, error);
        }
    }
    
    getProcessorName(): string {
        return 'ObjectAnalyzer';
    }
    
    /**
     * Analyzes object-specific information
     */
    private async analyzeObject(entry: ResourceEntry): Promise<ObjectInfo> {
        const resourceType = entry.key.type;
        const resourceValue = entry.value;
        
        const objectInfo: ObjectInfo = {
            objectType: this.determineObjectType(resourceType),
            category: 'Unknown',
            isCustomContent: this.isCustomContent(entry.key.group),
            hasCustomStrings: false,
            hasCustomThumbnails: false,
            meshCount: 0,
            textureCount: 0,
            animationCount: 0,
            slotCount: 0,
            tags: []
        };
        
        // Analyze based on resource type
        if (resourceType === URT.ObjectDefinition) {
            await this.analyzeObjectDefinition(resourceValue, objectInfo);
        } else if (resourceType === URT.ObjectCatalog) {
            await this.analyzeObjectCatalog(resourceValue, objectInfo);
        } else if (resourceType === URT.Footprint) {
            await this.analyzeFootprint(resourceValue, objectInfo);
        } else if (resourceType === URT.Slot) {
            await this.analyzeSlot(resourceValue, objectInfo);
        }
        
        // Generate tags based on analysis
        this.generateObjectTags(objectInfo);
        
        return objectInfo;
    }
    
    /**
     * Analyzes object definition resources
     */
    private async analyzeObjectDefinition(resourceValue: any, objectInfo: ObjectInfo): Promise<void> {
        objectInfo.objectType = 'Object Definition';
        
        // Try to extract object definition data
        try {
            // This would require parsing the actual object definition format
            // For now, provide basic analysis
            objectInfo.category = 'Build/Buy Object';
            objectInfo.tags.push('object-definition');
            
        } catch (error) {
            objectInfo.tags.push('parse-error');
        }
    }
    
    /**
     * Analyzes object catalog resources
     */
    private async analyzeObjectCatalog(resourceValue: any, objectInfo: ObjectInfo): Promise<void> {
        objectInfo.objectType = 'Object Catalog';
        objectInfo.category = 'Catalog Entry';
        objectInfo.tags.push('catalog');
        
        // Try to extract catalog information
        try {
            // This would require parsing the catalog format
            // For now, provide basic analysis
            
        } catch (error) {
            objectInfo.tags.push('parse-error');
        }
    }
    
    /**
     * Analyzes footprint resources
     */
    private async analyzeFootprint(resourceValue: any, objectInfo: ObjectInfo): Promise<void> {
        objectInfo.objectType = 'Footprint';
        objectInfo.category = 'Placement Data';
        objectInfo.tags.push('footprint', 'placement');
        
        // Try to extract footprint dimensions
        try {
            // This would require parsing the footprint format
            // For now, provide basic analysis
            
        } catch (error) {
            objectInfo.tags.push('parse-error');
        }
    }
    
    /**
     * Analyzes slot resources
     */
    private async analyzeSlot(resourceValue: any, objectInfo: ObjectInfo): Promise<void> {
        objectInfo.objectType = 'Slot';
        objectInfo.category = 'Interaction Data';
        objectInfo.tags.push('slot', 'interaction');
        objectInfo.slotCount = 1;
        
        // Try to extract slot information
        try {
            // This would require parsing the slot format
            // For now, provide basic analysis
            
        } catch (error) {
            objectInfo.tags.push('parse-error');
        }
    }
    
    /**
     * Determines the object type based on resource type
     */
    private determineObjectType(resourceType: number): string {
        switch (resourceType) {
            case URT.ObjectDefinition:
                return 'Object Definition';
            case URT.ObjectCatalog:
                return 'Object Catalog';
            case URT.ObjectCatalogSet:
                return 'Catalog Set';
            case URT.Footprint:
                return 'Footprint';
            case URT.Slot:
                return 'Slot';
            default:
                return 'Build/Buy Object';
        }
    }
    
    /**
     * Checks if this is custom content based on group ID
     */
    private isCustomContent(group: number): boolean {
        // Common custom content group IDs
        const customGroups = [
            0x80000000, // Common override group
            0x00000000  // Base game group (when used for overrides)
        ];
        
        return customGroups.includes(group);
    }
    
    /**
     * Generates descriptive tags based on object analysis
     */
    private generateObjectTags(objectInfo: ObjectInfo): void {
        // Add category-based tags
        if (objectInfo.category.toLowerCase().includes('furniture')) {
            objectInfo.tags.push('furniture');
        }
        if (objectInfo.category.toLowerCase().includes('decor')) {
            objectInfo.tags.push('decorative');
        }
        if (objectInfo.category.toLowerCase().includes('appliance')) {
            objectInfo.tags.push('appliance');
        }
        
        // Add content type tags
        if (objectInfo.isCustomContent) {
            objectInfo.tags.push('custom-content');
        }
        
        if (objectInfo.hasCustomStrings) {
            objectInfo.tags.push('custom-strings');
        }
        
        if (objectInfo.hasCustomThumbnails) {
            objectInfo.tags.push('custom-thumbnails');
        }
        
        // Add complexity tags
        if (objectInfo.meshCount > 0) {
            objectInfo.tags.push('has-mesh');
        }
        if (objectInfo.textureCount > 0) {
            objectInfo.tags.push('has-textures');
        }
        if (objectInfo.animationCount > 0) {
            objectInfo.tags.push('has-animations');
        }
        if (objectInfo.slotCount > 0) {
            objectInfo.tags.push('has-slots');
        }
    }
    
    /**
     * Gets the human-readable name for the object type
     */
    private getObjectTypeName(resourceType: number): string {
        return ResourceTypeHelpers.getTypeName(resourceType);
    }
    
    /**
     * Gets the size of the resource
     */
    private getResourceSize(resourceValue: any): number {
        if (!resourceValue) return 0;
        
        if (Buffer.isBuffer(resourceValue)) {
            return resourceValue.length;
        }
        
        if (resourceValue.buffer && Buffer.isBuffer(resourceValue.buffer)) {
            return resourceValue.buffer.length;
        }
        
        return 0;
    }
    
    /**
     * Determines if the resource is compressed
     */
    private isCompressed(resourceValue: any): boolean {
        if (!resourceValue) return false;
        
        if (resourceValue.compressionType !== undefined) {
            return resourceValue.compressionType !== 0;
        }
        
        return false;
    }
    
    /**
     * Validates the object analysis results
     */
    private validateObject(objectInfo: ObjectInfo, result: ProcessedResource): void {
        if (objectInfo.objectType === 'Unknown') {
            result.issues.push('Could not determine object type');
        }
        
        if (objectInfo.category === 'Unknown') {
            result.issues.push('Could not determine object category');
        }
        
        if (objectInfo.tags.includes('parse-error')) {
            result.issues.push('Error parsing object data');
        }
        
        if (result.size === 0) {
            result.issues.push('Object resource appears to be empty');
        }
    }
    
    /**
     * Creates an error result when processing fails
     */
    private createErrorResult(entry: ResourceEntry, error: any): ProcessedResource {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
            id: entry.id?.toString() || 'unknown',
            type: 'Error',
            size: 0,
            compressed: false,
            metadata: {
                error: errorMessage,
                processorUsed: this.getProcessorName()
            },
            issues: [`Processing failed: ${errorMessage}`]
        };
    }
}
