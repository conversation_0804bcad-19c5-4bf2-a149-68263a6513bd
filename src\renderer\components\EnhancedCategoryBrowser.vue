<template>
  <div class="category-browser">
    <div class="category-browser__header">
      <h3 class="category-browser__title">Browse by Category</h3>
      <div class="category-browser__stats">
        {{ totalMods }} mods organized
      </div>
    </div>

    <div class="category-browser__content">
      <!-- Main Categories -->
      <div class="category-grid">
        <div 
          v-for="category in mainCategories" 
          :key="category.id"
          class="category-card"
          :class="{ 'category-card--active': selectedCategory === category.id }"
          @click="selectCategory(category.id)"
        >
          <div class="category-card__icon" :class="`category-icon--${category.id}`">
            <component :is="category.icon" />
          </div>
          <div class="category-card__content">
            <h4 class="category-card__title">{{ category.name }}</h4>
            <p class="category-card__description">{{ category.description }}</p>
            <div class="category-card__count">{{ category.count }} mods</div>
          </div>
          <div class="category-card__arrow">
            <ChevronRightIcon />
          </div>
        </div>
      </div>

      <!-- Enhanced Hair Subcategories -->
      <div v-if="selectedCategory === 'cas'" class="subcategory-section">
        <h4 class="subcategory-section__title">
          <ScissorsIcon class="subcategory-section__icon" />
          Hair Style Categories
        </h4>
        
        <div class="hair-subcategory-grid">
          <!-- Hair Length Categories -->
          <div class="hair-category-group">
            <h5 class="hair-category-group__title">By Length</h5>
            <div class="hair-subcategory-list">
              <div 
                v-for="length in hairLengths" 
                :key="length.id"
                class="hair-subcategory-item"
                :class="`hair-length--${length.id}`"
                @click="filterByHairAttribute('length', length.id)"
              >
                <span class="hair-subcategory-item__icon">{{ length.icon }}</span>
                <span class="hair-subcategory-item__name">{{ length.name }}</span>
                <span class="hair-subcategory-item__count">{{ length.count }}</span>
              </div>
            </div>
          </div>

          <!-- Hair Style Categories -->
          <div class="hair-category-group">
            <h5 class="hair-category-group__title">By Style</h5>
            <div class="hair-subcategory-list">
              <div 
                v-for="style in hairStyles" 
                :key="style.id"
                class="hair-subcategory-item"
                :class="`hair-style--${style.id}`"
                @click="filterByHairAttribute('style', style.id)"
              >
                <span class="hair-subcategory-item__icon">{{ style.icon }}</span>
                <span class="hair-subcategory-item__name">{{ style.name }}</span>
                <span class="hair-subcategory-item__count">{{ style.count }}</span>
              </div>
            </div>
          </div>

          <!-- Hair Texture Categories -->
          <div class="hair-category-group">
            <h5 class="hair-category-group__title">By Texture</h5>
            <div class="hair-subcategory-list">
              <div 
                v-for="texture in hairTextures" 
                :key="texture.id"
                class="hair-subcategory-item"
                :class="`hair-texture--${texture.id}`"
                @click="filterByHairAttribute('texture', texture.id)"
              >
                <span class="hair-subcategory-item__icon">{{ texture.icon }}</span>
                <span class="hair-subcategory-item__name">{{ texture.name }}</span>
                <span class="hair-subcategory-item__count">{{ texture.count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Other Subcategories -->
      <div v-else-if="selectedCategory && selectedCategory !== 'cas'" class="subcategory-section">
        <h4 class="subcategory-section__title">
          {{ getSelectedCategoryName() }} Subcategories
        </h4>
        <div class="subcategory-grid">
          <div 
            v-for="subcategory in getSubcategories(selectedCategory)" 
            :key="subcategory.id"
            class="subcategory-card"
            @click="filterBySubcategory(subcategory.id)"
          >
            <div class="subcategory-card__content">
              <h5 class="subcategory-card__title">{{ subcategory.name }}</h5>
              <div class="subcategory-card__count">{{ subcategory.count }} mods</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  ChevronRightIcon,
  UserIcon,
  HomeIcon,
  PuzzlePieceIcon,
  GlobeAltIcon,
  CommandLineIcon,
  CogIcon
} from '@heroicons/vue/24/outline';

// Custom icon for scissors (hair)
const ScissorsIcon = () => '✂️';

interface Props {
  mods: any[];
}

const props = defineProps<Props>();
const emit = defineEmits<{
  categorySelected: [category: string];
  subcategorySelected: [subcategory: string];
  hairAttributeSelected: [attribute: string, value: string];
}>();

const selectedCategory = ref<string | null>(null);

const totalMods = computed(() => props.mods.length);

const mainCategories = computed(() => [
  {
    id: 'cas',
    name: 'Create-a-Sim',
    description: 'Hair, clothing, makeup, and accessories',
    icon: UserIcon,
    count: props.mods.filter(mod => isCASMod(mod)).length
  },
  {
    id: 'buildbuy',
    name: 'Build/Buy',
    description: 'Furniture, decorations, and build items',
    icon: HomeIcon,
    count: props.mods.filter(mod => isBuildBuyMod(mod)).length
  },
  {
    id: 'gameplay',
    name: 'Gameplay',
    description: 'Traits, careers, skills, and mechanics',
    icon: PuzzlePieceIcon,
    count: props.mods.filter(mod => isGameplayMod(mod)).length
  },
  {
    id: 'world',
    name: 'World/Lots',
    description: 'Lots, venues, and world modifications',
    icon: GlobeAltIcon,
    count: props.mods.filter(mod => isWorldMod(mod)).length
  },
  {
    id: 'script',
    name: 'Script Mods',
    description: 'Advanced functionality and overrides',
    icon: CommandLineIcon,
    count: props.mods.filter(mod => isScriptMod(mod)).length
  }
]);

const hairLengths = computed(() => [
  {
    id: 'very_short',
    name: 'Very Short',
    icon: '🪒',
    count: getHairCount('length', 'very_short')
  },
  {
    id: 'short',
    name: 'Short',
    icon: '✂️',
    count: getHairCount('length', 'short')
  },
  {
    id: 'medium',
    name: 'Medium',
    icon: '💇',
    count: getHairCount('length', 'medium')
  },
  {
    id: 'long',
    name: 'Long',
    icon: '🦱',
    count: getHairCount('length', 'long')
  },
  {
    id: 'very_long',
    name: 'Very Long',
    icon: '👸',
    count: getHairCount('length', 'very_long')
  }
]);

const hairStyles = computed(() => [
  {
    id: 'loose',
    name: 'Loose',
    icon: '🌊',
    count: getHairCount('style', 'loose')
  },
  {
    id: 'ponytail',
    name: 'Ponytail',
    icon: '🎀',
    count: getHairCount('style', 'ponytail')
  },
  {
    id: 'bun',
    name: 'Bun',
    icon: '🍞',
    count: getHairCount('style', 'bun')
  },
  {
    id: 'braids',
    name: 'Braids',
    icon: '🪢',
    count: getHairCount('style', 'braids')
  },
  {
    id: 'bangs',
    name: 'Bangs',
    icon: '✨',
    count: getHairCount('style', 'bangs')
  }
]);

const hairTextures = computed(() => [
  {
    id: 'straight',
    name: 'Straight',
    icon: '📏',
    count: getHairCount('texture', 'straight')
  },
  {
    id: 'wavy',
    name: 'Wavy',
    icon: '〰️',
    count: getHairCount('texture', 'wavy')
  },
  {
    id: 'curly',
    name: 'Curly',
    icon: '🌀',
    count: getHairCount('texture', 'curly')
  },
  {
    id: 'coily',
    name: 'Coily',
    icon: '🌪️',
    count: getHairCount('texture', 'coily')
  }
]);

// Helper functions
const isCASMod = (mod: any): boolean => {
  const fileName = (mod.fileName || '').toLowerCase();
  return fileName.includes('hair') || 
         fileName.includes('clothing') || 
         fileName.includes('makeup') || 
         fileName.includes('cas');
};

const isBuildBuyMod = (mod: any): boolean => {
  const fileName = (mod.fileName || '').toLowerCase();
  return fileName.includes('furniture') || 
         fileName.includes('build') || 
         fileName.includes('decor');
};

const isGameplayMod = (mod: any): boolean => {
  const fileName = (mod.fileName || '').toLowerCase();
  return fileName.includes('trait') || 
         fileName.includes('career') || 
         fileName.includes('skill');
};

const isWorldMod = (mod: any): boolean => {
  const fileName = (mod.fileName || '').toLowerCase();
  return fileName.includes('lot') || 
         fileName.includes('world') || 
         fileName.includes('venue');
};

const isScriptMod = (mod: any): boolean => {
  return (mod.fileExtension || '.package') === '.ts4script';
};

const getHairCount = (attribute: string, value: string): number => {
  return props.mods.filter(mod => {
    if (!isCASMod(mod) || !mod.casContent?.items) return false;
    
    return mod.casContent.items.some((item: any) => {
      if (!item.hairDetails) return false;
      
      if (attribute === 'length') {
        return item.hairDetails.length === value;
      } else if (attribute === 'style') {
        return item.hairDetails.style?.includes(value);
      } else if (attribute === 'texture') {
        return item.hairDetails.texture === value;
      }
      
      return false;
    });
  }).length;
};

const selectCategory = (categoryId: string) => {
  selectedCategory.value = selectedCategory.value === categoryId ? null : categoryId;
  emit('categorySelected', categoryId);
};

const filterByHairAttribute = (attribute: string, value: string) => {
  emit('hairAttributeSelected', attribute, value);
};

const filterBySubcategory = (subcategoryId: string) => {
  emit('subcategorySelected', subcategoryId);
};

const getSelectedCategoryName = (): string => {
  const category = mainCategories.value.find(c => c.id === selectedCategory.value);
  return category?.name || '';
};

const getSubcategories = (categoryId: string) => {
  // Return subcategories for non-CAS categories
  // This would be implemented based on the specific category
  return [];
};
</script>

<style scoped>
.category-browser {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border-subtle);
}

.category-browser__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-subtle);
}

.category-browser__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.category-browser__stats {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.category-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-card:hover {
  background: var(--bg-hover);
  border-color: var(--plumbob-green-light);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 255, 65, 0.1);
}

.category-card--active {
  background: var(--plumbob-green-bg);
  border-color: var(--plumbob-green);
}

.category-card__icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green-dark);
}

.category-card__content {
  flex: 1;
}

.category-card__title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.category-card__description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
}

.category-card__count {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--plumbob-green-dark);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.category-card__arrow {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-tertiary);
}

.subcategory-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-subtle);
}

.subcategory-section__title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
}

.subcategory-section__icon {
  font-size: 1.25rem;
}

.hair-subcategory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.hair-category-group__title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin: 0 0 1rem 0;
}

.hair-subcategory-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.hair-subcategory-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.hair-subcategory-item:hover {
  background: var(--bg-hover);
  border-color: var(--plumbob-green-light);
}

.hair-subcategory-item__icon {
  font-size: 1.125rem;
}

.hair-subcategory-item__name {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.hair-subcategory-item__count {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
}
</style>
