/**
 * Build/Buy object categories
 */
export enum ObjectCategory {
    SEATING = 'seating',
    SURFACES = 'surfaces',
    STORAGE = 'storage',
    LIGHTING = 'lighting',
    DECORATIVE = 'decorative',
    APPLIANCES = 'appliances',
    PLUMBING = 'plumbing',
    ELECTRONICS = 'electronics',
    PLANTS = 'plants',
    MISC = 'misc',
    UNKNOWN = 'unknown'
}

/**
 * Room types for object placement
 */
export enum RoomType {
    LIVING_ROOM = 'living_room',
    KITCHEN = 'kitchen',
    BEDROOM = 'bedroom',
    BATHROOM = 'bathroom',
    DINING_ROOM = 'dining_room',
    OFFICE = 'office',
    KIDS_ROOM = 'kids_room',
    OUTDOOR = 'outdoor',
    BASEMENT = 'basement',
    GARAGE = 'garage',
    ANY_ROOM = 'any_room'
}

/**
 * Object functions
 */
export enum ObjectFunction {
    SEATING = 'seating',
    SLEEPING = 'sleeping',
    STORAGE = 'storage',
    COOKING = 'cooking',
    CLEANING = 'cleaning',
    ENTERTAINMENT = 'entertainment',
    LIGHTING = 'lighting',
    DECORATION = 'decoration',
    EXERCISE = 'exercise',
    STUDY = 'study',
    HYGIENE = 'hygiene',
    UNKNOWN = 'unknown'
}

/**
 * Object styles
 */
export enum ObjectStyle {
    MODERN = 'modern',
    TRADITIONAL = 'traditional',
    RUSTIC = 'rustic',
    INDUSTRIAL = 'industrial',
    MINIMALIST = 'minimalist',
    VINTAGE = 'vintage',
    LUXURY = 'luxury',
    CASUAL = 'casual',
    UNKNOWN = 'unknown'
}

/**
 * Detailed Build/Buy object information
 */
export interface ObjectInfo {
    category: ObjectCategory;
    subcategory: string;
    roomAssignment: RoomType[];
    function: ObjectFunction;
    style: ObjectStyle[];
    price: number;
    isDecor: boolean;
    isFunctional: boolean;
    tags: string[];
    description: string;
}

/**
 * Object analysis summary for multiple objects
 */
export interface ObjectAnalysisSummary {
    primaryCategory: string;
    subcategory: string;
    objectCount: number;
    roomAssignments: string[];
    functions: string[];
    styles: string[];
    priceRange: {
        min: number;
        max: number;
        average: number;
    };
    isDecorOnly: boolean;
    isFunctionalOnly: boolean;
    isMixed: boolean;
}