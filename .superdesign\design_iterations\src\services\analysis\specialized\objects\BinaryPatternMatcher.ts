import { ObjectInfo, ObjectCategory, ObjectFunction, ObjectStyle, RoomType } from './ObjectTypes';

/**
 * Specialized pattern matcher for binary buffer analysis
 * Handles pattern detection when S4TK parsing fails
 */
export class BinaryPatternMatcher {

    /**
     * Extracts object data from binary buffer
     */
    public static extractObjectData(buffer: <PERSON>uffer, objectInfo: ObjectInfo): void {
        const bufferStr = buffer.toString('binary');

        // Detect object category and function
        this.detectObjectCategory(bufferStr, objectInfo);
        this.detectRoomAssignment(bufferStr, objectInfo);
        this.detectObjectStyle(bufferStr, objectInfo);
        this.detectObjectFunction(bufferStr, objectInfo);

        // Generate description
        objectInfo.description = this.generateObjectDescription(objectInfo);
    }

    /**
     * Detects object category from binary data patterns
     */
    private static detectObjectCategory(bufferStr: string, objectInfo: ObjectInfo): void {
        // Seating detection
        if (this.containsSeatingPatterns(bufferStr)) {
            objectInfo.category = ObjectCategory.SEATING;
            objectInfo.function = ObjectFunction.SEATING;
            objectInfo.isFunctional = true;
            objectInfo.subcategory = this.detectSeatingSubcategory(bufferStr);
        }
        // Storage detection
        else if (this.containsStoragePatterns(bufferStr)) {
            objectInfo.category = ObjectCategory.STORAGE;
            objectInfo.function = ObjectFunction.STORAGE;
            objectInfo.isFunctional = true;
            objectInfo.subcategory = this.detectStorageSubcategory(bufferStr);
        }
        // Surface detection
        else if (this.containsSurfacePatterns(bufferStr)) {
            objectInfo.category = ObjectCategory.SURFACES;
            objectInfo.isFunctional = true;
            objectInfo.subcategory = this.detectSurfaceSubcategory(bufferStr);
        }
        // Lighting detection
        else if (this.containsLightingPatterns(bufferStr)) {
            objectInfo.category = ObjectCategory.LIGHTING;
            objectInfo.function = ObjectFunction.LIGHTING;
            objectInfo.isFunctional = true;
            objectInfo.subcategory = this.detectLightingSubcategory(bufferStr);
        }
        // Appliance detection
        else if (this.containsAppliancePatterns(bufferStr)) {
            objectInfo.category = ObjectCategory.APPLIANCES;
            objectInfo.isFunctional = true;
            objectInfo.subcategory = this.detectApplianceSubcategory(bufferStr);
        }
        // Decorative detection
        else if (this.containsDecorativePatterns(bufferStr)) {
            objectInfo.category = ObjectCategory.DECORATIVE;
            objectInfo.function = ObjectFunction.DECORATION;
            objectInfo.isDecor = true;
            objectInfo.subcategory = this.detectDecorativeSubcategory(bufferStr);
        }
        // Electronics detection
        else if (this.containsElectronicsPatterns(bufferStr)) {
            objectInfo.category = ObjectCategory.ELECTRONICS;
            objectInfo.function = ObjectFunction.ENTERTAINMENT;
            objectInfo.isFunctional = true;
            objectInfo.subcategory = this.detectElectronicsSubcategory(bufferStr);
        }
        else {
            // Default categorization
            objectInfo.category = ObjectCategory.MISC;
            objectInfo.subcategory = 'miscellaneous';
        }
    }

    /**
     * Detects room assignment from patterns
     */
    private static detectRoomAssignment(bufferStr: string, objectInfo: ObjectInfo): void {
        if (bufferStr.includes('kitchen') || bufferStr.includes('Kitchen')) {
            objectInfo.roomAssignment.push(RoomType.KITCHEN);
        }
        if (bufferStr.includes('bathroom') || bufferStr.includes('Bathroom') ||
            bufferStr.includes('bath') || bufferStr.includes('Bath')) {
            objectInfo.roomAssignment.push(RoomType.BATHROOM);
        }
        if (bufferStr.includes('bedroom') || bufferStr.includes('Bedroom') ||
            bufferStr.includes('bed') || bufferStr.includes('Bed')) {
            objectInfo.roomAssignment.push(RoomType.BEDROOM);
        }
        if (bufferStr.includes('living') || bufferStr.includes('Living')) {
            objectInfo.roomAssignment.push(RoomType.LIVING_ROOM);
        }
        if (bufferStr.includes('dining') || bufferStr.includes('Dining')) {
            objectInfo.roomAssignment.push(RoomType.DINING_ROOM);
        }
        if (bufferStr.includes('office') || bufferStr.includes('Office') ||
            bufferStr.includes('study') || bufferStr.includes('Study')) {
            objectInfo.roomAssignment.push(RoomType.OFFICE);
        }
        if (bufferStr.includes('outdoor') || bufferStr.includes('Outdoor') ||
            bufferStr.includes('garden') || bufferStr.includes('Garden')) {
            objectInfo.roomAssignment.push(RoomType.OUTDOOR);
        }
        if (bufferStr.includes('kids') || bufferStr.includes('Kids') ||
            bufferStr.includes('child') || bufferStr.includes('Child')) {
            objectInfo.roomAssignment.push(RoomType.KIDS_ROOM);
        }

        // Default to any room if no specific assignment
        if (objectInfo.roomAssignment.length === 0) {
            objectInfo.roomAssignment.push(RoomType.ANY_ROOM);
        }
    }

    /**
     * Detects object style from patterns
     */
    private static detectObjectStyle(bufferStr: string, objectInfo: ObjectInfo): void {
        if (bufferStr.includes('modern') || bufferStr.includes('Modern')) {
            objectInfo.style.push(ObjectStyle.MODERN);
        }
        if (bufferStr.includes('traditional') || bufferStr.includes('Traditional')) {
            objectInfo.style.push(ObjectStyle.TRADITIONAL);
        }
        if (bufferStr.includes('rustic') || bufferStr.includes('Rustic')) {
            objectInfo.style.push(ObjectStyle.RUSTIC);
        }
        if (bufferStr.includes('industrial') || bufferStr.includes('Industrial')) {
            objectInfo.style.push(ObjectStyle.INDUSTRIAL);
        }
        if (bufferStr.includes('luxury') || bufferStr.includes('Luxury')) {
            objectInfo.style.push(ObjectStyle.LUXURY);
        }
        if (bufferStr.includes('vintage') || bufferStr.includes('Vintage')) {
            objectInfo.style.push(ObjectStyle.VINTAGE);
        }

        // Default style if none detected
        if (objectInfo.style.length === 0) {
            objectInfo.style.push(ObjectStyle.CASUAL);
        }
    }

    /**
     * Detects object function from patterns
     */
    private static detectObjectFunction(bufferStr: string, objectInfo: ObjectInfo): void {
        if (objectInfo.function !== ObjectFunction.UNKNOWN) return; // Already set

        if (bufferStr.includes('sleep') || bufferStr.includes('Sleep')) {
            objectInfo.function = ObjectFunction.SLEEPING;
        } else if (bufferStr.includes('cook') || bufferStr.includes('Cook')) {
            objectInfo.function = ObjectFunction.COOKING;
        } else if (bufferStr.includes('clean') || bufferStr.includes('Clean')) {
            objectInfo.function = ObjectFunction.CLEANING;
        } else if (bufferStr.includes('exercise') || bufferStr.includes('Exercise')) {
            objectInfo.function = ObjectFunction.EXERCISE;
        } else if (bufferStr.includes('study') || bufferStr.includes('Study')) {
            objectInfo.function = ObjectFunction.STUDY;
        }
    }

    // Pattern detection helper methods
    private static containsSeatingPatterns(bufferStr: string): boolean {
        const seatingPatterns = ['chair', 'Chair', 'seat', 'Seat', 'sofa', 'Sofa', 'couch', 'Couch', 'bench', 'Bench'];
        return seatingPatterns.some(pattern => bufferStr.includes(pattern));
    }

    private static containsStoragePatterns(bufferStr: string): boolean {
        const storagePatterns = ['storage', 'Storage', 'shelf', 'Shelf', 'cabinet', 'Cabinet', 'dresser', 'Dresser', 'wardrobe', 'Wardrobe'];
        return storagePatterns.some(pattern => bufferStr.includes(pattern));
    }

    private static containsSurfacePatterns(bufferStr: string): boolean {
        const surfacePatterns = ['table', 'Table', 'desk', 'Desk', 'counter', 'Counter', 'surface', 'Surface'];
        return surfacePatterns.some(pattern => bufferStr.includes(pattern));
    }

    private static containsLightingPatterns(bufferStr: string): boolean {
        const lightingPatterns = ['light', 'Light', 'lamp', 'Lamp', 'lighting', 'Lighting', 'chandelier', 'Chandelier'];
        return lightingPatterns.some(pattern => bufferStr.includes(pattern));
    }

    private static containsAppliancePatterns(bufferStr: string): boolean {
        const appliancePatterns = ['appliance', 'Appliance', 'fridge', 'Fridge', 'stove', 'Stove', 'oven', 'Oven', 'microwave', 'Microwave'];
        return appliancePatterns.some(pattern => bufferStr.includes(pattern));
    }

    private static containsDecorativePatterns(bufferStr: string): boolean {
        const decorativePatterns = ['decor', 'Decor', 'decoration', 'Decoration', 'art', 'Art', 'painting', 'Painting', 'plant', 'Plant'];
        return decorativePatterns.some(pattern => bufferStr.includes(pattern));
    }

    private static containsElectronicsPatterns(bufferStr: string): boolean {
        const electronicsPatterns = ['tv', 'TV', 'television', 'Television', 'computer', 'Computer', 'stereo', 'Stereo', 'radio', 'Radio'];
        return electronicsPatterns.some(pattern => bufferStr.includes(pattern));
    }

    // Subcategory detection methods
    private static detectSeatingSubcategory(bufferStr: string): string {
        if (bufferStr.includes('chair') || bufferStr.includes('Chair')) return 'chairs';
        if (bufferStr.includes('sofa') || bufferStr.includes('Sofa') || bufferStr.includes('couch') || bufferStr.includes('Couch')) return 'sofas';
        if (bufferStr.includes('bench') || bufferStr.includes('Bench')) return 'benches';
        return 'seating';
    }

    private static detectStorageSubcategory(bufferStr: string): string {
        if (bufferStr.includes('shelf') || bufferStr.includes('Shelf')) return 'shelving';
        if (bufferStr.includes('cabinet') || bufferStr.includes('Cabinet')) return 'cabinets';
        if (bufferStr.includes('dresser') || bufferStr.includes('Dresser')) return 'dressers';
        if (bufferStr.includes('wardrobe') || bufferStr.includes('Wardrobe')) return 'wardrobes';
        return 'storage';
    }

    private static detectSurfaceSubcategory(bufferStr: string): string {
        if (bufferStr.includes('desk') || bufferStr.includes('Desk')) return 'desks';
        if (bufferStr.includes('counter') || bufferStr.includes('Counter')) return 'counters';
        if (bufferStr.includes('table') || bufferStr.includes('Table')) return 'tables';
        return 'surfaces';
    }

    private static detectLightingSubcategory(bufferStr: string): string {
        if (bufferStr.includes('chandelier') || bufferStr.includes('Chandelier')) return 'ceiling_lights';
        if (bufferStr.includes('lamp') || bufferStr.includes('Lamp')) return 'lamps';
        return 'lighting';
    }

    private static detectApplianceSubcategory(bufferStr: string): string {
        if (bufferStr.includes('fridge') || bufferStr.includes('Fridge')) return 'refrigeration';
        if (bufferStr.includes('stove') || bufferStr.includes('Stove') || bufferStr.includes('oven') || bufferStr.includes('Oven')) return 'cooking';
        if (bufferStr.includes('microwave') || bufferStr.includes('Microwave')) return 'small_appliances';
        return 'appliances';
    }

    private static detectDecorativeSubcategory(bufferStr: string): string {
        if (bufferStr.includes('plant') || bufferStr.includes('Plant')) return 'plants';
        if (bufferStr.includes('art') || bufferStr.includes('Art') || bufferStr.includes('painting') || bufferStr.includes('Painting')) return 'wall_art';
        return 'decorative';
    }

    private static detectElectronicsSubcategory(bufferStr: string): string {
        if (bufferStr.includes('tv') || bufferStr.includes('TV') || bufferStr.includes('television') || bufferStr.includes('Television')) return 'entertainment';
        if (bufferStr.includes('computer') || bufferStr.includes('Computer')) return 'computers';
        if (bufferStr.includes('stereo') || bufferStr.includes('Stereo') || bufferStr.includes('radio') || bufferStr.includes('Radio')) return 'audio';
        return 'electronics';
    }

    /**
     * Extracts buffer from various resource value formats
     */
    public static extractBuffer(resourceValue: any): Buffer | null {
        if (!resourceValue) return null;

        // Handle different buffer formats
        if (resourceValue._bufferCache && resourceValue._bufferCache.buffer) {
            return resourceValue._bufferCache.buffer;
        } else if (Buffer.isBuffer(resourceValue)) {
            return resourceValue;
        } else if (resourceValue.buffer) {
            return resourceValue.buffer;
        } else if (resourceValue.value && Buffer.isBuffer(resourceValue.value)) {
            return resourceValue.value;
        } else {
            // Try to convert to buffer
            try {
                return Buffer.from(resourceValue);
            } catch {
                return null;
            }
        }
    }

    /**
     * Generates object description based on extracted data
     */
    private static generateObjectDescription(objectInfo: ObjectInfo): string {
        const parts: string[] = [];

        // Add style if available
        if (objectInfo.style.length > 0 && objectInfo.style[0] !== ObjectStyle.UNKNOWN) {
            parts.push(objectInfo.style[0]);
        }

        // Add category
        parts.push(objectInfo.category);

        // Add function if different from category
        if (objectInfo.function !== ObjectFunction.UNKNOWN && 
            objectInfo.function.toLowerCase() !== objectInfo.category.toLowerCase()) {
            parts.push(`for ${objectInfo.function}`);
        }

        // Add room assignment if specific
        if (objectInfo.roomAssignment.length === 1 && objectInfo.roomAssignment[0] !== RoomType.ANY_ROOM) {
            parts.push(`(${objectInfo.roomAssignment[0].replace('_', ' ')})`);
        }

        return parts.join(' ').replace(/^\w/, c => c.toUpperCase());
    }
}