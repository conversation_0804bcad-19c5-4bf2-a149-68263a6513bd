/**
 * Test Universal Classifier - Phase 2
 * 
 * Tests all CAS categories (hair, clothing, makeup, accessories, skin details)
 * to verify comprehensive subcategory detection across all content types.
 */

import { UniversalSubcategoryClassifier } from '../services/analysis/specialized/cas/UniversalSubcategoryClassifier';
import { SubcategoryPatternConfig } from '../services/analysis/specialized/cas/SubcategoryPatternConfig';
import { EnhancedCASDetector } from '../services/analysis/specialized/cas/EnhancedCASDetector';

/**
 * Test comprehensive CAS category detection
 */
function testAllCASCategories() {
    console.log('🎨 Testing All CAS Categories...\n');
    
    const testCases = [
        // Hair tests
        { filename: 'creator_long_wavy_ponytail_hair.package', category: 'hair', expectedSubcategories: ['long', 'wavy', 'ponytail'] },
        { filename: 'short_curly_bob_hair.package', category: 'hair', expectedSubcategories: ['very_short', 'curly'] },
        
        // Clothing tests
        { filename: 'formal_business_suit_clothing.package', category: 'clothing', expectedSubcategories: ['formal'] },
        { filename: 'casual_t_shirt_jeans.package', category: 'clothing', expectedSubcategories: ['casual', 'top'] },
        { filename: 'athletic_workout_gear.package', category: 'clothing', expectedSubcategories: ['athletic'] },
        { filename: 'party_cocktail_dress.package', category: 'clothing', expectedSubcategories: ['party', 'dress'] },
        { filename: 'summer_bikini_swimwear.package', category: 'clothing', expectedSubcategories: ['swimwear'] },
        
        // Makeup tests
        { filename: 'red_matte_lipstick_makeup.package', category: 'makeup', expectedSubcategories: ['lipstick'] },
        { filename: 'smoky_eyeshadow_palette.package', category: 'makeup', expectedSubcategories: ['eyeshadow'] },
        { filename: 'black_eyeliner_makeup.package', category: 'makeup', expectedSubcategories: ['eyeliner'] },
        { filename: 'pink_blush_cheeks.package', category: 'makeup', expectedSubcategories: ['blush'] },
        
        // Accessory tests
        { filename: 'gold_necklace_jewelry.package', category: 'accessories', expectedSubcategories: ['jewelry'] },
        { filename: 'designer_sunglasses_eyewear.package', category: 'accessories', expectedSubcategories: ['glasses'] },
        { filename: 'winter_beanie_hat.package', category: 'accessories', expectedSubcategories: ['hat'] },
        { filename: 'leather_handbag_purse.package', category: 'accessories', expectedSubcategories: ['bag'] },
        
        // Skin detail tests
        { filename: 'tribal_arm_tattoo_ink.package', category: 'skin_details', expectedSubcategories: ['tattoo'] },
        { filename: 'face_freckles_spots.package', category: 'skin_details', expectedSubcategories: ['freckles'] },
        { filename: 'battle_scar_mark.package', category: 'skin_details', expectedSubcategories: ['scar'] }
    ];
    
    let successCount = 0;
    let totalTests = testCases.length;
    
    for (const testCase of testCases) {
        console.log(`📁 Testing: ${testCase.filename} (${testCase.category})`);
        
        const result = UniversalSubcategoryClassifier.classifySubcategories(
            testCase.filename, 
            testCase.category
        );
        
        console.log(`   Detected subcategories: ${result.subcategories.join(', ')}`);
        console.log(`   Expected subcategories: ${testCase.expectedSubcategories.join(', ')}`);
        console.log(`   Confidence: ${result.confidence.toFixed(2)}`);
        console.log(`   Tags: ${result.tags.join(', ')}`);
        
        // Check if at least one expected subcategory was detected
        const hasExpectedSubcategory = testCase.expectedSubcategories.some(expected => 
            result.subcategories.includes(expected)
        );
        
        if (hasExpectedSubcategory && result.confidence > 0.5) {
            console.log(`   ✅ Success`);
            successCount++;
        } else {
            console.log(`   ❌ Failed`);
        }
        
        console.log('');
    }
    
    console.log(`📊 Results: ${successCount}/${totalTests} tests passed (${(successCount/totalTests*100).toFixed(1)}%)\n`);
    return successCount / totalTests;
}

/**
 * Test pattern configuration completeness
 */
function testPatternConfiguration() {
    console.log('⚙️ Testing Pattern Configuration Completeness...\n');
    
    const allPatterns = SubcategoryPatternConfig.getAllCASPatterns();
    const categories = SubcategoryPatternConfig.getAllCategories();
    
    console.log(`📊 Total patterns loaded: ${allPatterns.length}`);
    console.log(`📊 Categories available: ${categories.join(', ')}`);
    
    for (const category of categories) {
        const categoryPatterns = SubcategoryPatternConfig.getPatternsByCategory(category);
        const subcategories = SubcategoryPatternConfig.getSubcategoriesForCategory(category);
        
        console.log(`   ${category}: ${categoryPatterns.length} patterns, ${subcategories.length} subcategories`);
        console.log(`      Subcategories: ${subcategories.join(', ')}`);
    }
    
    console.log('');
}

/**
 * Test EnhancedCASDetector integration
 */
function testEnhancedCASDetectorIntegration() {
    console.log('🔗 Testing EnhancedCASDetector Integration...\n');
    
    const testFiles = [
        'formal_business_dress.package',
        'red_lipstick_makeup.package',
        'gold_earrings_jewelry.package',
        'tribal_tattoo_ink.package'
    ];
    
    for (const filename of testFiles) {
        console.log(`📁 Testing: ${filename}`);
        
        // Test if filename is detected as CAS
        const isCAS = EnhancedCASDetector.isCASFilename(filename);
        const category = EnhancedCASDetector.getCASCategoryFromFilename(filename);
        const confidence = EnhancedCASDetector.calculateCASConfidence(filename, []);
        
        console.log(`   Is CAS: ${isCAS}`);
        console.log(`   Category: ${category}`);
        console.log(`   Confidence: ${confidence.toFixed(2)}`);
        
        if (isCAS) {
            const casPartInfo = EnhancedCASDetector.detectCASFromFilename(filename, []);
            if (casPartInfo.length > 0) {
                console.log(`   Subcategory: ${casPartInfo[0].subcategory}`);
                console.log(`   Description: ${casPartInfo[0].description}`);
                console.log(`   Tags: ${casPartInfo[0].tags.join(', ')}`);
            }
        }
        
        console.log('');
    }
}

/**
 * Test performance with multiple categories
 */
function testPerformance() {
    console.log('⚡ Testing Performance Across Categories...\n');
    
    const testFiles = [
        'hair_long_wavy_ponytail.package',
        'clothing_formal_business_suit.package',
        'makeup_red_lipstick.package',
        'accessories_gold_necklace.package',
        'skin_tribal_tattoo.package'
    ];
    
    const categories = ['hair', 'clothing', 'makeup', 'accessories', 'skin_details'];
    
    const startTime = Date.now();
    let totalClassifications = 0;
    
    for (const filename of testFiles) {
        for (const category of categories) {
            const result = UniversalSubcategoryClassifier.classifySubcategories(filename, category);
            totalClassifications++;
        }
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / totalClassifications;
    
    console.log(`📊 Performance Results:`);
    console.log(`   Total classifications: ${totalClassifications}`);
    console.log(`   Total time: ${totalTime}ms`);
    console.log(`   Average time per classification: ${avgTime.toFixed(2)}ms`);
    console.log(`   Target: <50ms per mod (✅ ${avgTime < 50 ? 'PASSED' : 'FAILED'})`);
    console.log('');
    
    return avgTime;
}

/**
 * Main test execution
 */
function main() {
    console.log('🚀 Universal Subcategory Classifier - Phase 2 Testing\n');
    console.log('Testing comprehensive CAS category detection...\n');
    
    try {
        testPatternConfiguration();
        const successRate = testAllCASCategories();
        testEnhancedCASDetectorIntegration();
        const avgTime = testPerformance();
        
        console.log('📋 Phase 2 Summary:');
        console.log(`   Success Rate: ${(successRate * 100).toFixed(1)}%`);
        console.log(`   Performance: ${avgTime.toFixed(2)}ms per classification`);
        console.log(`   Target Success: >70% (${successRate > 0.7 ? '✅ PASSED' : '❌ FAILED'})`);
        console.log(`   Target Performance: <50ms (${avgTime < 50 ? '✅ PASSED' : '❌ FAILED'})`);
        
        if (successRate > 0.7 && avgTime < 50) {
            console.log('\n✅ Phase 2 completed successfully!');
            console.log('🎯 All CAS categories enhanced with unified subcategory detection');
        } else {
            console.log('\n⚠️ Phase 2 needs optimization');
        }
        
    } catch (error) {
        console.error('❌ Testing failed:', error);
        process.exit(1);
    }
}

// Run tests
main();
