/**
 * Quick test to verify the data structure returned by analysis
 * and ensure it matches what the UI expects
 */

const { PackageAnalysisService } = require('./dist/services/analysis/PackageAnalysisService.js');
const fs = require('fs');
const path = require('path');

async function testUIDataStructure() {
    console.log('🧪 Testing UI Data Structure...\n');
    
    const modsFolder = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    if (!fs.existsSync(modsFolder)) {
        console.error('❌ Mods folder not found:', modsFolder);
        return;
    }
    
    const analysisService = new PackageAnalysisService();
    const files = fs.readdirSync(modsFolder)
        .filter(file => file.endsWith('.package') || file.endsWith('.ts4script'))
        .slice(0, 3); // Test first 3 files
    
    console.log(`📁 Found ${files.length} test files\n`);
    
    for (const fileName of files) {
        const filePath = path.join(modsFolder, fileName);
        console.log(`🔍 Testing: ${fileName}`);
        
        try {
            const buffer = fs.readFileSync(filePath);
            const stats = fs.statSync(filePath);
            const analysis = analysisService.detailedAnalyze(buffer, filePath);
            
            // Map the data the same way App.vue does
            const mappedResult = {
                ...analysis,
                fileName: fileName,
                hasResourceIntelligence: !!analysis.intelligence?.resourceIntelligence,
                intelligenceType: getIntelligenceType(analysis),
                qualityScore: analysis.intelligence?.qualityAssessment?.overallScore || 0,
                riskLevel: analysis.intelligence?.resourceIntelligence?.riskLevel || 'unknown',
                resourceIntelligenceData: analysis.intelligence?.resourceIntelligence,
                scriptIntelligenceData: analysis.intelligence?.resourceIntelligence,
                qualityAssessmentData: analysis.intelligence?.qualityAssessment,
                dependencyData: analysis.intelligence?.dependencies,
                metadataConfidence: analysis.metadataConfidence || 0,
                processingTime: analysis.processingTime || 0,
                author: analysis.metadata?.author || analysis.author || 'Unknown',
                version: analysis.metadata?.version || analysis.version || 'Unknown',
                modName: analysis.metadata?.modName || analysis.modName || fileName,
                fileSize: stats.size,
                fileExtension: path.extname(fileName)
            };
            
            console.log('✅ Raw analysis keys:', Object.keys(analysis));
            console.log('✅ Mapped result keys:', Object.keys(mappedResult));
            console.log('✅ Sample mapped data:');
            console.log('   - fileName:', mappedResult.fileName);
            console.log('   - fileType:', mappedResult.fileType);
            console.log('   - fileSize:', mappedResult.fileSize);
            console.log('   - author:', mappedResult.author);
            console.log('   - hasResourceIntelligence:', mappedResult.hasResourceIntelligence);
            console.log('   - intelligenceType:', mappedResult.intelligenceType);
            console.log('');
            
        } catch (error) {
            console.error('❌ Error analyzing:', fileName, error.message);
        }
    }
}

function getIntelligenceType(result) {
    if (result.fileExtension === '.ts4script' && result.intelligence?.resourceIntelligence) {
        return 'Script Intelligence';
    }
    if (result.fileExtension === '.package' && result.intelligence?.resourceIntelligence) {
        return 'Resource Intelligence';
    }
    if (result.intelligence) {
        return 'Basic Intelligence';
    }
    return 'No Intelligence';
}

testUIDataStructure().catch(console.error);
