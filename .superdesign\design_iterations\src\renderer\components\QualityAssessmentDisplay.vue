<template>
  <div class="quality-assessment">
    <!-- Overall Score -->
    <div class="quality-overview">
      <div class="score-circle">
        <div class="score-value">{{ assessment.overallScore }}</div>
        <div class="score-label">Overall</div>
      </div>
      
      <div class="score-breakdown">
        <div class="score-item">
          <div class="score-item__label">Completeness</div>
          <div class="score-item__bar">
            <div 
              class="score-item__fill" 
              :style="{ width: `${assessment.completeness}%` }"
            ></div>
          </div>
          <div class="score-item__value">{{ assessment.completeness }}</div>
        </div>
        
        <div class="score-item">
          <div class="score-item__label">Compatibility</div>
          <div class="score-item__bar">
            <div 
              class="score-item__fill" 
              :style="{ width: `${assessment.compatibility}%` }"
            ></div>
          </div>
          <div class="score-item__value">{{ assessment.compatibility }}</div>
        </div>
        
        <div class="score-item">
          <div class="score-item__label">Performance</div>
          <div class="score-item__bar">
            <div 
              class="score-item__fill" 
              :style="{ width: `${assessment.performance}%` }"
            ></div>
          </div>
          <div class="score-item__value">{{ assessment.performance }}</div>
        </div>
      </div>
    </div>
    
    <!-- Quality Factors -->
    <div v-if="assessment.qualityFactors?.length > 0" class="quality-factors">
      <h6 class="factors-title">Quality Factors</h6>
      <div class="factors-list">
        <span 
          v-for="factor in assessment.qualityFactors" 
          :key="factor"
          class="factor-tag factor-tag--positive"
        >
          {{ factor }}
        </span>
      </div>
    </div>
    
    <!-- Issues -->
    <div v-if="assessment.issues?.length > 0" class="quality-issues">
      <h6 class="issues-title">Issues Found</h6>
      <div class="issues-list">
        <div 
          v-for="issue in assessment.issues" 
          :key="issue"
          class="issue-item"
        >
          <ExclamationTriangleIcon class="issue-icon" />
          <span class="issue-text">{{ issue }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline';

interface QualityAssessment {
  overallScore: number;
  completeness: number;
  compatibility: number;
  performance: number;
  qualityFactors?: string[];
  issues?: string[];
}

defineProps<{
  assessment: QualityAssessment;
}>();
</script>

<style scoped>
.quality-assessment {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Overview */
.quality-overview {
  display: flex;
  gap: var(--space-6);
  align-items: center;
}

.score-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--bg-secondary);
  border: 3px solid var(--plumbob-green);
  flex-shrink: 0;
}

.score-value {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
}

.score-label {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.score-breakdown {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.score-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.score-item__label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  min-width: 100px;
}

.score-item__bar {
  flex: 1;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.score-item__fill {
  height: 100%;
  background: linear-gradient(90deg, var(--sims-blue), var(--plumbob-green));
  border-radius: var(--radius-full);
  transition: width var(--duration-300) var(--ease-out);
}

.score-item__value {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
  min-width: 30px;
  text-align: right;
}

/* Quality Factors */
.quality-factors {
  background: var(--success-bg);
  border: 1px solid var(--success-border);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.factors-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--success);
  margin: 0 0 var(--space-3) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.factors-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.factor-tag {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.factor-tag--positive {
  background: var(--success);
  color: var(--text-inverse);
}

/* Issues */
.quality-issues {
  background: var(--warning-bg);
  border: 1px solid var(--warning-border);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.issues-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--warning);
  margin: 0 0 var(--space-3) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.issue-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.issue-icon {
  width: 16px;
  height: 16px;
  color: var(--warning);
  flex-shrink: 0;
}

.issue-text {
  font-size: var(--text-sm);
  color: var(--text-primary);
}
</style>
