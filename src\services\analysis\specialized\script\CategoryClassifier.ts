/**
 * Script Category Classifier
 * 
 * Specialized component for classifying script mods into categories and subcategories
 * based on filename patterns, content analysis, and gameplay areas.
 * 
 * Extracted from ScriptAnalyzer.ts as part of Phase 2 refactoring.
 */

import { GameplayArea, ScriptModCategory, type ScriptModInfo } from './types';
import { FrameworkDetector } from './FrameworkDetector';
import { GameplayAreaDetector } from './GameplayAreaDetector';

/**
 * <PERSON>les classification of script mods into categories and subcategories
 */
export class CategoryClassifier {
    
    /**
     * Classifies a script mod based on filename and detected gameplay areas
     * 
     * @param fileName - Script file name
     * @param scriptInfo - Script info object to populate
     */
    public static classifyScript(fileName: string, scriptInfo: ScriptModInfo): void {
        const lowerFileName = fileName.toLowerCase();
        
        // First, let framework detector handle framework/library/cheat/utility detection
        FrameworkDetector.determineCategory(lowerFileName, scriptInfo);
        
        // If it's a gameplay mod, determine the specific subcategory
        if (scriptInfo.category === ScriptModCategory.GAMEPLAY) {
            scriptInfo.subcategory = this.detectGameplaySubcategory(lowerFileName, scriptInfo.gameplayAreas);
        }
        
        // Validate and refine classification
        this.refineClassification(scriptInfo);
    }
    
    /**
     * Detects gameplay subcategory based on filename and gameplay areas
     * 
     * @param fileName - Script file name (lowercase)
     * @param gameplayAreas - Detected gameplay areas
     * @returns Subcategory identifier
     */
    private static detectGameplaySubcategory(fileName: string, gameplayAreas: GameplayArea[]): string {
        // Filename-based detection (highest priority)
        const fileNameMappings: Record<string, string> = {
            'career': 'career_mods',
            'job': 'career_mods',
            'work': 'career_mods',
            'relationship': 'relationship_mods',
            'romance': 'relationship_mods',
            'dating': 'relationship_mods',
            'pregnancy': 'family_mods',
            'pregnant': 'family_mods',
            'family': 'family_mods',
            'household': 'family_mods',
            'skill': 'skill_mods',
            'trait': 'trait_mods',
            'personality': 'trait_mods',
            'emotion': 'emotion_mods',
            'mood': 'emotion_mods',
            'buff': 'emotion_mods',
            'interaction': 'interaction_mods',
            'social': 'interaction_mods',
            'autonomy': 'autonomy_mods',
            'behavior': 'autonomy_mods',
            'event': 'event_mods',
            'party': 'event_mods',
            'holiday': 'event_mods'
        };
        
        for (const [pattern, subcategory] of Object.entries(fileNameMappings)) {
            if (fileName.includes(pattern)) {
                return subcategory;
            }
        }
        
        // Gameplay area-based detection (secondary)
        const primaryArea = GameplayAreaDetector.getPrimaryArea(gameplayAreas);
        return this.mapGameplayAreaToSubcategory(primaryArea);
    }
    
    /**
     * Maps gameplay area to subcategory
     * 
     * @param gameplayArea - Primary gameplay area
     * @returns Subcategory identifier
     */
    private static mapGameplayAreaToSubcategory(gameplayArea: GameplayArea): string {
        const areaToSubcategory: Record<GameplayArea, string> = {
            [GameplayArea.CAREERS]: 'career_mods',
            [GameplayArea.RELATIONSHIPS]: 'relationship_mods',
            [GameplayArea.FAMILY]: 'family_mods',
            [GameplayArea.PREGNANCY]: 'family_mods',
            [GameplayArea.SKILLS]: 'skill_mods',
            [GameplayArea.TRAITS]: 'trait_mods',
            [GameplayArea.ASPIRATIONS]: 'aspiration_mods',
            [GameplayArea.EMOTIONS]: 'emotion_mods',
            [GameplayArea.INTERACTIONS]: 'interaction_mods',
            [GameplayArea.EVENTS]: 'event_mods',
            [GameplayArea.AUTONOMY]: 'autonomy_mods',
            [GameplayArea.UI]: 'ui_mods',
            [GameplayArea.OBJECTS]: 'object_mods',
            [GameplayArea.UNKNOWN]: 'gameplay_mods'
        };
        
        return areaToSubcategory[gameplayArea] || 'gameplay_mods';
    }
    
    /**
     * Refines classification based on additional analysis
     * 
     * @param scriptInfo - Script info to refine
     */
    private static refineClassification(scriptInfo: ScriptModInfo): void {
        // Check for misclassified framework mods
        if (scriptInfo.category === ScriptModCategory.GAMEPLAY && this.appearsToBeFramework(scriptInfo)) {
            scriptInfo.category = ScriptModCategory.FRAMEWORK;
            scriptInfo.isFramework = true;
            scriptInfo.subcategory = 'framework';
        }
        
        // Check for misclassified utility mods
        if (scriptInfo.category === ScriptModCategory.GAMEPLAY && this.appearsToBeUtility(scriptInfo)) {
            scriptInfo.category = ScriptModCategory.UTILITY;
            scriptInfo.subcategory = 'utility_mod';
        }
        
        // Validate subcategory
        if (!scriptInfo.subcategory || scriptInfo.subcategory === 'unknown') {
            scriptInfo.subcategory = this.getDefaultSubcategory(scriptInfo.category);
        }
    }
    
    /**
     * Checks if a script appears to be a framework based on additional indicators
     * 
     * @param scriptInfo - Script info to check
     * @returns true if appears to be a framework
     */
    private static appearsToBeFramework(scriptInfo: ScriptModInfo): boolean {
        // Check dependencies for framework indicators
        const frameworkDependencies = ['sims4', 'services', 'event_testing', 'interactions'];
        const hasFrameworkDeps = frameworkDependencies.some(dep => 
            scriptInfo.dependencies.includes(dep)
        );
        
        // Check for multiple gameplay areas (frameworks often affect many areas)
        const affectsMultipleAreas = scriptInfo.gameplayAreas.length >= 3;
        
        // Check for framework-like naming patterns in dependencies
        const hasFrameworkNaming = scriptInfo.dependencies.some(dep => 
            dep.includes('core') || dep.includes('base') || dep.includes('framework')
        );
        
        return hasFrameworkDeps && (affectsMultipleAreas || hasFrameworkNaming);
    }
    
    /**
     * Checks if a script appears to be a utility based on additional indicators
     * 
     * @param scriptInfo - Script info to check
     * @returns true if appears to be a utility
     */
    private static appearsToBeUtility(scriptInfo: ScriptModInfo): boolean {
        // Check for utility-like gameplay areas
        const utilityAreas = [GameplayArea.UI, GameplayArea.UNKNOWN];
        const hasUtilityAreas = scriptInfo.gameplayAreas.some(area => utilityAreas.includes(area));
        
        // Check for utility-like dependencies
        const utilityDependencies = ['os', 'sys', 'json', 'xml'];
        const hasUtilityDeps = utilityDependencies.some(dep => 
            scriptInfo.dependencies.includes(dep)
        );
        
        return hasUtilityAreas && hasUtilityDeps;
    }
    
    /**
     * Gets default subcategory for a given category
     * 
     * @param category - Script mod category
     * @returns Default subcategory
     */
    private static getDefaultSubcategory(category: ScriptModCategory): string {
        const defaults: Record<ScriptModCategory, string> = {
            [ScriptModCategory.GAMEPLAY]: 'gameplay_mods',
            [ScriptModCategory.FRAMEWORK]: 'framework',
            [ScriptModCategory.LIBRARY]: 'script_library',
            [ScriptModCategory.UTILITY]: 'utility_mod',
            [ScriptModCategory.CHEAT]: 'cheat_mod',
            [ScriptModCategory.UNKNOWN]: 'unknown'
        };
        
        return defaults[category] || 'unknown';
    }
    
    /**
     * Gets human-readable subcategory name
     * 
     * @param subcategory - Subcategory identifier
     * @returns Human-readable name
     */
    public static getSubcategoryDisplayName(subcategory: string): string {
        const displayNames: Record<string, string> = {
            'career_mods': 'Career Mods',
            'relationship_mods': 'Relationship Mods',
            'family_mods': 'Family Mods',
            'skill_mods': 'Skill Mods',
            'trait_mods': 'Trait Mods',
            'aspiration_mods': 'Aspiration Mods',
            'emotion_mods': 'Emotion Mods',
            'interaction_mods': 'Interaction Mods',
            'event_mods': 'Event Mods',
            'autonomy_mods': 'Autonomy Mods',
            'ui_mods': 'UI Mods',
            'object_mods': 'Object Mods',
            'gameplay_mods': 'Gameplay Mods',
            'framework': 'Framework',
            'script_library': 'Script Library',
            'utility_mod': 'Utility Mod',
            'cheat_mod': 'Cheat Mod',
            'mc_command_center': 'MC Command Center',
            'xml_injector': 'XML Injector',
            'ui_cheats': 'UI Cheats Extension',
            'unknown': 'Unknown'
        };
        
        return displayNames[subcategory] || subcategory
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }
    
    /**
     * Validates classification results
     * 
     * @param scriptInfo - Script info to validate
     * @returns Validation result
     */
    public static validateClassification(scriptInfo: ScriptModInfo): {
        isValid: boolean;
        issues: string[];
        confidence: number;
    } {
        const issues: string[] = [];
        let confidence = 100;
        
        // Check category consistency
        if (scriptInfo.isFramework && scriptInfo.category !== ScriptModCategory.FRAMEWORK) {
            issues.push('Framework flag inconsistent with category');
            confidence -= 30;
        }
        
        if (scriptInfo.isLibrary && scriptInfo.category !== ScriptModCategory.LIBRARY) {
            issues.push('Library flag inconsistent with category');
            confidence -= 30;
        }
        
        // Check subcategory validity
        if (!scriptInfo.subcategory || scriptInfo.subcategory === 'unknown') {
            issues.push('Subcategory not determined');
            confidence -= 20;
        }
        
        // Check gameplay areas consistency
        if (scriptInfo.category === ScriptModCategory.GAMEPLAY && 
            scriptInfo.gameplayAreas.length === 1 && 
            scriptInfo.gameplayAreas[0] === GameplayArea.UNKNOWN) {
            issues.push('Gameplay mod with unknown gameplay area');
            confidence -= 15;
        }
        
        return {
            isValid: issues.length === 0,
            issues,
            confidence: Math.max(0, confidence)
        };
    }
}
