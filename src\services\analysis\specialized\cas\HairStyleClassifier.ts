/**
 * Hair Style Classifier
 * 
 * Enhanced component for classifying hair styles from filenames and resource data.
 * Implements detailed hair length, style, and texture detection to improve
 * subcategory accuracy from 40-60% to 70-80%.
 * 
 * Part of Enhanced Subcategory Detection phase.
 */

import { HairLength, HairStyle, HairTexture, HairClassificationInfo } from './types';

/**
 * <PERSON><PERSON> detailed hair style classification using filename analysis and resource patterns
 */
export class HairStyleClassifier {
    
    /**
     * Hair length detection patterns
     */
    private static readonly LENGTH_PATTERNS = {
        [HairLength.VERY_SHORT]: [
            'pixie', 'buzz', 'crew', 'short', 'crop', 'bob'
        ],
        [HairLength.SHORT]: [
            'bob', 'lob', 'chin', 'shoulder', 'short'
        ],
        [HairLength.MEDIUM]: [
            'medium', 'mid', 'shoulder', 'collarbone', 'lob'
        ],
        [HairLength.LONG]: [
            'long', 'waist', 'hip', 'flowing'
        ],
        [HairLength.VERY_LONG]: [
            'floor', 'ankle', 'very_long', 'extra_long', 'rapunzel'
        ]
    };

    /**
     * Hair style detection patterns
     */
    private static readonly STYLE_PATTERNS = {
        [HairStyle.PONYTAIL]: [
            'ponytail', 'pony', 'tail', 'high_pony', 'low_pony'
        ],
        [HairStyle.BUN]: [
            'bun', 'chignon', 'topknot', 'messy_bun', 'high_bun', 'low_bun'
        ],
        [HairStyle.BRAIDS]: [
            'braid', 'braided', 'plait', 'french_braid', 'dutch_braid', 'fishtail'
        ],
        [HairStyle.PIGTAILS]: [
            'pigtails', 'twin_tails', 'twintails', 'pig_tails'
        ],
        [HairStyle.UPDO]: [
            'updo', 'up_do', 'formal', 'elegant', 'wedding'
        ],
        [HairStyle.HALF_UP]: [
            'half_up', 'halfup', 'half_down', 'halfdown'
        ],
        [HairStyle.BANGS]: [
            'bangs', 'fringe', 'bang', 'side_bang'
        ],
        [HairStyle.SIDE_SWEPT]: [
            'side_swept', 'swept', 'side_part', 'asymmetric'
        ],
        [HairStyle.LOOSE]: [
            'loose', 'down', 'flowing', 'free'
        ]
    };

    /**
     * Hair texture detection patterns
     */
    private static readonly TEXTURE_PATTERNS = {
        [HairTexture.STRAIGHT]: [
            'straight', 'sleek', 'smooth', 'pin_straight'
        ],
        [HairTexture.WAVY]: [
            'wavy', 'wave', 'beach', 'tousled', 'loose_wave'
        ],
        [HairTexture.CURLY]: [
            'curly', 'curl', 'ringlet', 'spiral', 'bouncy'
        ],
        [HairTexture.COILY]: [
            'coily', 'kinky', 'afro', 'natural', 'textured'
        ]
    };

    /**
     * Classifies hair style from filename and available data
     * 
     * @param filename - The mod filename
     * @param resourceData - Optional resource data for additional analysis
     * @returns Hair classification information
     */
    public static classifyHairStyle(filename: string, resourceData?: any): HairClassificationInfo {
        const lowerFilename = filename.toLowerCase();
        const keywords = this.extractKeywords(lowerFilename);
        
        // Detect length
        const length = this.detectHairLength(lowerFilename, keywords);
        
        // Detect styles (can have multiple)
        const styles = this.detectHairStyles(lowerFilename, keywords);
        
        // Detect texture
        const texture = this.detectHairTexture(lowerFilename, keywords);
        
        // Check for accessories
        const hasAccessories = this.detectHairAccessories(lowerFilename);
        
        // Calculate confidence based on detection quality
        const confidence = this.calculateConfidence(length, styles, texture, keywords);
        
        return {
            length,
            style: styles,
            texture,
            hasAccessories,
            confidence,
            detectionMethod: 'filename',
            keywords
        };
    }

    /**
     * Extracts relevant keywords from filename
     */
    private static extractKeywords(filename: string): string[] {
        // Remove common prefixes/suffixes and split into words
        const cleaned = filename
            .replace(/\.(package|ts4script)$/i, '')
            .replace(/^[^_]*_/, '') // Remove creator prefix
            .replace(/_/g, ' ')
            .toLowerCase();
        
        return cleaned.split(/[\s\-_]+/).filter(word => word.length > 2);
    }

    /**
     * Detects hair length from filename patterns
     */
    private static detectHairLength(filename: string, keywords: string[]): HairLength {
        const allText = [filename, ...keywords].join(' ');
        
        for (const [length, patterns] of Object.entries(this.LENGTH_PATTERNS)) {
            if (patterns.some(pattern => allText.includes(pattern))) {
                return length as HairLength;
            }
        }
        
        return HairLength.UNKNOWN;
    }

    /**
     * Detects hair styles from filename patterns (can detect multiple)
     */
    private static detectHairStyles(filename: string, keywords: string[]): HairStyle[] {
        const allText = [filename, ...keywords].join(' ');
        const detectedStyles: HairStyle[] = [];
        
        for (const [style, patterns] of Object.entries(this.STYLE_PATTERNS)) {
            if (patterns.some(pattern => allText.includes(pattern))) {
                detectedStyles.push(style as HairStyle);
            }
        }
        
        // If no specific style detected, default to loose
        if (detectedStyles.length === 0) {
            detectedStyles.push(HairStyle.LOOSE);
        }
        
        return detectedStyles;
    }

    /**
     * Detects hair texture from filename patterns
     */
    private static detectHairTexture(filename: string, keywords: string[]): HairTexture {
        const allText = [filename, ...keywords].join(' ');
        
        for (const [texture, patterns] of Object.entries(this.TEXTURE_PATTERNS)) {
            if (patterns.some(pattern => allText.includes(pattern))) {
                return texture as HairTexture;
            }
        }
        
        return HairTexture.UNKNOWN;
    }

    /**
     * Detects if hair has accessories
     */
    private static detectHairAccessories(filename: string): boolean {
        const accessoryPatterns = [
            'bow', 'ribbon', 'headband', 'clip', 'pin', 'flower',
            'accessory', 'decorated', 'ornament', 'jewel'
        ];
        
        return accessoryPatterns.some(pattern => filename.includes(pattern));
    }

    /**
     * Calculates confidence score based on detection quality
     */
    private static calculateConfidence(
        length: HairLength,
        styles: HairStyle[],
        texture: HairTexture,
        keywords: string[]
    ): number {
        let confidence = 0.3; // Base confidence for filename analysis
        
        // Increase confidence based on successful detections
        if (length !== HairLength.UNKNOWN) confidence += 0.2;
        if (styles.length > 0 && !styles.includes(HairStyle.UNKNOWN)) confidence += 0.2;
        if (texture !== HairTexture.UNKNOWN) confidence += 0.1;
        
        // Increase confidence based on keyword quality
        if (keywords.length >= 3) confidence += 0.1;
        if (keywords.some(k => k.includes('hair'))) confidence += 0.1;
        
        return Math.min(confidence, 1.0);
    }

    /**
     * Gets human-readable description of hair classification
     */
    public static getHairDescription(hairInfo: HairClassificationInfo): string {
        const parts: string[] = [];
        
        if (hairInfo.length !== HairLength.UNKNOWN) {
            parts.push(hairInfo.length.replace('_', ' '));
        }
        
        if (hairInfo.texture !== HairTexture.UNKNOWN) {
            parts.push(hairInfo.texture);
        }
        
        if (hairInfo.style.length > 0 && !hairInfo.style.includes(HairStyle.UNKNOWN)) {
            const styleNames = hairInfo.style.map(s => s.replace('_', ' '));
            parts.push(styleNames.join(', '));
        }
        
        if (hairInfo.hasAccessories) {
            parts.push('with accessories');
        }
        
        return parts.length > 0 ? parts.join(' ') + ' hair' : 'hair';
    }
}
