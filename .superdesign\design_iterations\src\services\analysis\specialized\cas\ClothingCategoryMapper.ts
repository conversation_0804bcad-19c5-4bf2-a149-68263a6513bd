/**
 * CAS Clothing Category Mapper
 * 
 * Specialized component for mapping clothing category values to outfit categories.
 * Handles the conversion from numeric clothing categories to structured outfit types.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring.
 */

import { ClothingCategory, type CASPartInfo } from './types';

/**
 * Handles mapping of clothing category values to outfit categories
 */
export class ClothingCategoryMapper {
    
    /**
     * Maps clothing category values to outfit categories
     * 
     * @param clothingCategoryValue - The numeric clothing category value from SimData
     * @param casPartInfo - CAS part info object to populate
     */
    public static mapClothingCategory(clothingCategoryValue: any, casPartInfo: CASPartInfo): void {
        const numericValue = this.parseClothingCategoryValue(clothingCategoryValue);
        
        if (numericValue === null) {
            console.warn('Invalid clothing category value:', clothingCategoryValue);
            this.setDefaultClothingCategory(casPartInfo);
            return;
        }
        
        this.mapClothingCategoryToOutfits(numericValue, casPartInfo);
    }
    
    /**
     * Parses various input formats to numeric clothing category value
     * 
     * @param value - Input value in various formats
     * @returns Numeric value or null if parsing fails
     */
    private static parseClothingCategoryValue(value: any): number | null {
        if (typeof value === 'number') {
            return value;
        }
        
        if (typeof value === 'string') {
            const parsed = parseInt(value, 10);
            return isNaN(parsed) ? null : parsed;
        }
        
        // Handle object with value property
        if (value && typeof value.value !== 'undefined') {
            return this.parseClothingCategoryValue(value.value);
        }
        
        return null;
    }
    
    /**
     * Maps numeric clothing category to outfit categories
     * 
     * @param clothingCategory - Numeric clothing category value
     * @param casPartInfo - CAS part info to populate
     */
    private static mapClothingCategoryToOutfits(clothingCategory: number, casPartInfo: CASPartInfo): void {
        // Clear existing clothing categories to avoid duplicates
        casPartInfo.clothingCategories = [];
        
        // Map based on common clothing category values
        // Note: These mappings may need adjustment based on actual game data
        switch (clothingCategory) {
            case 1: // Everyday
                casPartInfo.clothingCategories.push(ClothingCategory.EVERYDAY);
                break;
                
            case 2: // Formal
                casPartInfo.clothingCategories.push(ClothingCategory.FORMAL);
                break;
                
            case 3: // Athletic
                casPartInfo.clothingCategories.push(ClothingCategory.ATHLETIC);
                break;
                
            case 4: // Sleep
                casPartInfo.clothingCategories.push(ClothingCategory.SLEEP);
                break;
                
            case 5: // Party
                casPartInfo.clothingCategories.push(ClothingCategory.PARTY);
                break;
                
            case 6: // Swimwear
                casPartInfo.clothingCategories.push(ClothingCategory.SWIMWEAR);
                break;
                
            case 7: // Hot Weather
                casPartInfo.clothingCategories.push(ClothingCategory.HOT_WEATHER);
                break;
                
            case 8: // Cold Weather
                casPartInfo.clothingCategories.push(ClothingCategory.COLD_WEATHER);
                break;
                
            case 9: // Bathing
                casPartInfo.clothingCategories.push(ClothingCategory.BATHING);
                break;
                
            default:
                // For unknown categories, default to everyday
                this.setDefaultClothingCategory(casPartInfo);
                break;
        }
    }
    
    /**
     * Sets default clothing category when parsing fails or value is unknown
     * 
     * @param casPartInfo - CAS part info to update
     */
    private static setDefaultClothingCategory(casPartInfo: CASPartInfo): void {
        casPartInfo.clothingCategories = [ClothingCategory.EVERYDAY];
    }
    
    /**
     * Adds multiple clothing categories from bitwise flags
     * 
     * @param clothingCategoryFlags - Bitwise flags for multiple categories
     * @param casPartInfo - CAS part info to populate
     */
    public static mapClothingCategoryFlags(clothingCategoryFlags: number, casPartInfo: CASPartInfo): void {
        casPartInfo.clothingCategories = [];
        
        // Check each clothing category flag
        if (clothingCategoryFlags & 0x01) {
            casPartInfo.clothingCategories.push(ClothingCategory.EVERYDAY);
        }
        if (clothingCategoryFlags & 0x02) {
            casPartInfo.clothingCategories.push(ClothingCategory.FORMAL);
        }
        if (clothingCategoryFlags & 0x04) {
            casPartInfo.clothingCategories.push(ClothingCategory.ATHLETIC);
        }
        if (clothingCategoryFlags & 0x08) {
            casPartInfo.clothingCategories.push(ClothingCategory.SLEEP);
        }
        if (clothingCategoryFlags & 0x10) {
            casPartInfo.clothingCategories.push(ClothingCategory.PARTY);
        }
        if (clothingCategoryFlags & 0x20) {
            casPartInfo.clothingCategories.push(ClothingCategory.SWIMWEAR);
        }
        if (clothingCategoryFlags & 0x40) {
            casPartInfo.clothingCategories.push(ClothingCategory.HOT_WEATHER);
        }
        if (clothingCategoryFlags & 0x80) {
            casPartInfo.clothingCategories.push(ClothingCategory.COLD_WEATHER);
        }
        if (clothingCategoryFlags & 0x100) {
            casPartInfo.clothingCategories.push(ClothingCategory.BATHING);
        }
        
        // If no categories found, set default
        if (casPartInfo.clothingCategories.length === 0) {
            this.setDefaultClothingCategory(casPartInfo);
        }
    }
    
    /**
     * Gets human-readable description for clothing category
     * 
     * @param clothingCategory - Clothing category enum value
     * @returns Human-readable description
     */
    public static getClothingCategoryDescription(clothingCategory: ClothingCategory): string {
        const descriptions: Record<ClothingCategory, string> = {
            [ClothingCategory.EVERYDAY]: 'Everyday',
            [ClothingCategory.FORMAL]: 'Formal',
            [ClothingCategory.ATHLETIC]: 'Athletic',
            [ClothingCategory.SLEEP]: 'Sleep',
            [ClothingCategory.PARTY]: 'Party',
            [ClothingCategory.SWIMWEAR]: 'Swimwear',
            [ClothingCategory.HOT_WEATHER]: 'Hot Weather',
            [ClothingCategory.COLD_WEATHER]: 'Cold Weather',
            [ClothingCategory.BATHING]: 'Bathing'
        };
        
        return descriptions[clothingCategory] || 'Unknown Category';
    }
    
    /**
     * Validates clothing category assignments
     * 
     * @param clothingCategories - Array of clothing categories to validate
     * @returns true if clothing categories are valid
     */
    public static validateClothingCategories(clothingCategories: ClothingCategory[]): boolean {
        return clothingCategories.length > 0 && clothingCategories.every(category => 
            Object.values(ClothingCategory).includes(category)
        );
    }
    
    /**
     * Determines if clothing categories are appropriate for the CAS part type
     * 
     * @param clothingCategories - Array of clothing categories
     * @param isClothing - Whether the CAS part is clothing
     * @returns true if categories are appropriate
     */
    public static areClothingCategoriesAppropriate(clothingCategories: ClothingCategory[], isClothing: boolean): boolean {
        // Non-clothing items shouldn't have clothing categories
        if (!isClothing && clothingCategories.length > 0) {
            return false;
        }
        
        // Clothing items should have at least one category
        if (isClothing && clothingCategories.length === 0) {
            return false;
        }
        
        return true;
    }
}
