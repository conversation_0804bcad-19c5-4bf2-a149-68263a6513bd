import { app, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron';
import * as path from 'path';
import * as fs from 'fs/promises';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

/**
 * Creates a serializable version of analysis results by removing non-serializable objects
 */
function createSerializableResult(result: any): any {
    if (!result) return result;

    // Create a deep copy and remove problematic properties
    const serializable = JSON.parse(JSON.stringify(result, (key, value) => {
        // Skip functions, symbols, and other non-serializable types
        if (typeof value === 'function' || typeof value === 'symbol') {
            return undefined;
        }

        // Skip Buffer objects and replace with size info
        if (value && value.type === 'Buffer' && Array.isArray(value.data)) {
            return { type: 'Buffer', size: value.data.length };
        }

        // Skip complex S4TK objects but keep basic properties
        if (value && typeof value === 'object' && value.constructor &&
            value.constructor.name && value.constructor.name.includes('Resource')) {
            return { type: value.constructor.name, size: value.size || 0 };
        }

        return value;
    }));

    return serializable;
}

function createWindow() {
    const mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        webPreferences: {
            preload: path.join(__dirname, '../preload/index.js'),
            nodeIntegration: false,
            contextIsolation: true,
        },
        titleBarStyle: 'hiddenInset',
        show: false, // Don't show until ready
    });

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // electron-vite will handle this automatically
    if (process.env.VITE_DEV_SERVER_URL) {
        mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL);
        // Open DevTools in development
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    } else {
        mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    return mainWindow;
}

app.whenReady().then(() => {
    const analysisService = new PackageAnalysisService();
    let mainWindow: BrowserWindow;

    // IPC Handlers

    // Single file analysis
    ipcMain.handle('analyze-package', async (event, filePath: string) => {
        console.log(`Analyzing file: ${filePath}`);
        try {
            const buffer = await fs.readFile(filePath);
            const result = await analysisService.detailedAnalyzeAsync(buffer, filePath);

            // Create a serializable version of the result
            const serializableResult = createSerializableResult(result);

            return { success: true, data: serializableResult };
        } catch (error) {
            console.error('Analysis error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'An unknown error occurred.'
            };
        }
    });

    // Batch analysis for entire mod folder
    ipcMain.handle('analyze-mods-folder', async (event, folderPath: string) => {
        console.log(`Analyzing mods folder: ${folderPath}`);
        try {
            const results = await analyzeModsFolder(folderPath, analysisService);

            // Create serializable versions of all results
            const serializableResults = results.map(result => createSerializableResult(result));

            return { success: true, data: serializableResults };
        } catch (error) {
            console.error('Folder analysis error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to analyze mods folder.'
            };
        }
    });

    // Open folder dialog
    ipcMain.handle('select-mods-folder', async () => {
        try {
            const result = await dialog.showOpenDialog(mainWindow, {
                properties: ['openDirectory'],
                title: 'Select Sims 4 Mods Folder',
                defaultPath: path.join(require('os').homedir(), 'Documents', 'Electronic Arts', 'The Sims 4', 'Mods')
            });

            if (!result.canceled && result.filePaths.length > 0) {
                return { success: true, path: result.filePaths[0] };
            } else {
                return { success: false, error: 'No folder selected' };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to open folder dialog'
            };
        }
    });

    // Export analysis results
    ipcMain.handle('export-results', async (event, data: any, format: 'json' | 'csv') => {
        try {
            const result = await dialog.showSaveDialog(mainWindow, {
                title: 'Export Analysis Results',
                defaultPath: `simonitor-analysis-${new Date().toISOString().split('T')[0]}.${format}`,
                filters: [
                    { name: format.toUpperCase(), extensions: [format] }
                ]
            });

            if (!result.canceled && result.filePath) {
                let content: string;
                if (format === 'json') {
                    content = JSON.stringify(data, null, 2);
                } else {
                    content = convertToCSV(data);
                }

                await fs.writeFile(result.filePath, content, 'utf8');
                return { success: true, path: result.filePath };
            } else {
                return { success: false, error: 'Export cancelled' };
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to export results'
            };
        }
    });

    mainWindow = createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            mainWindow = createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Helper Functions

/**
 * Recursively analyzes all mod files in a folder
 */
async function analyzeModsFolder(folderPath: string, analysisService: PackageAnalysisService): Promise<any[]> {
    const results: any[] = [];

    async function scanDirectory(dirPath: string): Promise<void> {
        try {
            const items = await fs.readdir(dirPath, { withFileTypes: true });

            for (const item of items) {
                const fullPath = path.join(dirPath, item.name);

                if (item.isDirectory()) {
                    // Recursively scan subdirectories
                    await scanDirectory(fullPath);
                } else if (item.isFile()) {
                    const ext = path.extname(item.name).toLowerCase();
                    if (ext === '.package' || ext === '.ts4script') {
                        try {
                            console.log(`Analyzing: ${fullPath}`);
                            const buffer = await fs.readFile(fullPath);
                            const result = await analysisService.detailedAnalyzeAsync(buffer, fullPath);

                            // Add file metadata
                            const stats = await fs.stat(fullPath);
                            result.filePath = fullPath;
                            result.fileSize = stats.size;
                            result.lastModified = stats.mtime;

                            results.push(result);
                        } catch (error) {
                            console.error(`Error analyzing ${fullPath}:`, error);
                            // Add error entry
                            results.push({
                                fileName: item.name,
                                filePath: fullPath,
                                error: error instanceof Error ? error.message : 'Analysis failed',
                                hasError: true
                            });
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`Error scanning directory ${dirPath}:`, error);
        }
    }

    await scanDirectory(folderPath);
    return results;
}

/**
 * Converts analysis results to CSV format
 */
function convertToCSV(data: any[]): string {
    if (data.length === 0) return '';

    // Define CSV headers
    const headers = [
        'File Name',
        'File Path',
        'File Size',
        'File Type',
        'Author',
        'Version',
        'Resource Count',
        'Has Intelligence',
        'Intelligence Type',
        'Quality Score',
        'Risk Level',
        'Category',
        'Content Type',
        'Performance Impact',
        'Custom Content',
        'Processing Time',
        'Error'
    ];

    // Convert data to CSV rows
    const rows = data.map(item => [
        item.fileName || '',
        item.filePath || '',
        item.fileSize || 0,
        item.fileExtension || '',
        item.author || '',
        item.version || '',
        item.resourceCount || 0,
        item.hasResourceIntelligence ? 'Yes' : 'No',
        item.intelligenceType || '',
        item.qualityScore || '',
        item.riskLevel || '',
        item.resourceIntelligenceData?.category || '',
        item.resourceIntelligenceData?.contentType || '',
        item.resourceIntelligenceData?.performance?.estimatedImpact || '',
        item.resourceIntelligenceData?.customContent?.isCustomContent ? 'Yes' : 'No',
        item.processingTime || 0,
        item.error || ''
    ]);

    // Escape CSV values
    const escapeCSV = (value: any): string => {
        const str = String(value);
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
            return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
    };

    // Build CSV content
    const csvContent = [
        headers.map(escapeCSV).join(','),
        ...rows.map(row => row.map(escapeCSV).join(','))
    ].join('\n');

    return csvContent;
}