#!/usr/bin/env node

/**
 * Real-World Mods Test
 * 
 * Tests the enhanced system against the user's actual Sims 4 mods folder
 * to validate performance with real-world mod collections.
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface RealWorldTestResult {
    filePath: string;
    fileType: string;
    fileExtension: string;
    fileSize: number;
    
    // Phase 3A: Metadata
    author?: string;
    version?: string;
    modName?: string;
    metadataSource?: string;
    metadataConfidence?: number;
    
    // Phase 4A: Intelligence
    hasIntelligence: boolean;
    qualityScore?: number;
    riskLevel?: string;
    dependencyCount?: number;
    hasResourceIntelligence: boolean;
    
    // Performance
    processingTime: number;
    error?: string;
}

class RealWorldModTester {
    private analysisService: PackageAnalysisService;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
    }
    
    /**
     * Tests all mods in the user's real Sims 4 mods folder
     */
    public async testRealWorldMods(): Promise<void> {
        console.log('🌍 REAL-WORLD MODS ANALYSIS');
        console.log('Testing enhanced system against actual Sims 4 mods collection\n');
        
        const modsDir = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
        
        if (!fs.existsSync(modsDir)) {
            console.error('❌ Mods directory not found:', modsDir);
            console.log('Please ensure the path is correct and the folder exists.');
            return;
        }
        
        console.log(`📁 Scanning mods directory: ${modsDir}\n`);
        
        // Recursively find all mod files
        const modFiles = this.findModFiles(modsDir);
        
        if (modFiles.length === 0) {
            console.log('❌ No mod files found in the directory.');
            return;
        }
        
        console.log(`📦 Found ${modFiles.length} mod files to analyze\n`);
        console.log('🚀 FULL COLLECTION ANALYSIS - Testing production readiness');
        console.log('   This will test ALL files to validate large-scale performance\n');
        
        const results: RealWorldTestResult[] = [];
        let processedCount = 0;
        let totalProcessingTime = 0;
        let errorCount = 0;
        const startTime = Date.now();

        // Progress tracking
        const progressInterval = Math.max(1, Math.floor(modFiles.length / 20)); // Show progress every 5%

        for (const filePath of modFiles) {
            processedCount++;
            const fileName = path.basename(filePath);

            // Show progress for large collections
            if (processedCount % progressInterval === 0 || processedCount === modFiles.length) {
                const progress = Math.round((processedCount / modFiles.length) * 100);
                const elapsed = Math.round((Date.now() - startTime) / 1000);
                const avgTime = Math.round(totalProcessingTime / processedCount);
                console.log(`📊 Progress: ${progress}% (${processedCount}/${modFiles.length}) | ⏱️ ${elapsed}s elapsed | 📈 ${avgTime}ms avg`);
            }

            const result = await this.testFile(filePath);
            results.push(result);
            totalProcessingTime += result.processingTime;

            if (result.error) {
                errorCount++;
                console.log(`   ❌ [${processedCount}] ${fileName}: ${result.error}`);
            } else {
                // Only show details for interesting files or errors
                if (result.processingTime > 1000 || !result.author || processedCount <= 10) {
                    const authorStatus = result.author ? '✅' : '❌';
                    const intelligenceStatus = result.hasIntelligence ? '✅' : '❌';
                    const qualityScore = result.qualityScore ? `${result.qualityScore}/100` : 'N/A';
                    const sizeKB = Math.round(result.fileSize / 1024);

                    console.log(`   [${processedCount}] ${fileName}`);
                    console.log(`      📝 Author: ${authorStatus} | 🧠 Intelligence: ${intelligenceStatus} | ⭐ Quality: ${qualityScore} | 📊 ${sizeKB}KB | ⏱️ ${result.processingTime}ms`);
                }
            }
        }

        const totalElapsed = Math.round((Date.now() - startTime) / 1000);
        console.log(`\n🏁 ANALYSIS COMPLETE: ${modFiles.length} files in ${totalElapsed}s (${errorCount} errors)`);
        console.log(`   Average: ${Math.round(totalProcessingTime / modFiles.length)}ms per file`);
        
        console.log('\n' + '='.repeat(80));
        this.generateRealWorldReport(results, modFiles.length, totalElapsed, totalProcessingTime);
    }
    
    /**
     * Recursively finds all mod files in the directory
     */
    private findModFiles(dir: string): string[] {
        const modFiles: string[] = [];
        
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    // Recursively search subdirectories
                    modFiles.push(...this.findModFiles(fullPath));
                } else if (stat.isFile()) {
                    // Check if it's a mod file
                    if (item.endsWith('.package') || item.endsWith('.ts4script')) {
                        modFiles.push(fullPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not read directory ${dir}:`, error);
        }
        
        return modFiles;
    }
    
    /**
     * Tests a single mod file with timeout protection
     */
    private async testFile(filePath: string): Promise<RealWorldTestResult> {
        const fileName = path.basename(filePath);
        const fileExtension = path.extname(fileName);

        const startTime = Date.now();

        try {
            const stats = fs.statSync(filePath);

            // Skip extremely large files that might cause memory issues
            const maxFileSize = 500 * 1024 * 1024; // 500MB limit
            if (stats.size > maxFileSize) {
                return {
                    filePath: fileName,
                    fileType: 'skipped',
                    fileExtension,
                    fileSize: stats.size,
                    hasIntelligence: false,
                    hasResourceIntelligence: false,
                    processingTime: Date.now() - startTime,
                    error: `File too large (${Math.round(stats.size / 1024 / 1024)}MB > 500MB limit)`
                };
            }

            const buffer = fs.readFileSync(filePath);

            // Create a timeout promise for very slow files
            const timeoutMs = 30000; // 30 second timeout
            const analysisPromise = this.analysisService.detailedAnalyzeAsync(buffer, filePath);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error(`Analysis timeout after ${timeoutMs}ms`)), timeoutMs);
            });

            const analysis = await Promise.race([analysisPromise, timeoutPromise]) as any;
            const processingTime = Date.now() - startTime;
            
            return {
                filePath: fileName,
                fileType: analysis.fileType,
                fileExtension,
                fileSize: stats.size,
                
                // Phase 3A: Metadata
                author: analysis.metadata.author,
                version: analysis.metadata.version,
                modName: analysis.metadata.modName,
                metadataSource: analysis.metadata.metadataSource,
                metadataConfidence: analysis.metadata.metadataConfidence,
                
                // Phase 4A: Intelligence
                hasIntelligence: !!analysis.intelligence,
                qualityScore: analysis.intelligence?.qualityAssessment?.overallScore,
                riskLevel: analysis.intelligence?.dependencies?.riskLevel,
                dependencyCount: analysis.intelligence?.dependencies?.dependencies?.length || 0,
                hasResourceIntelligence: !!analysis.intelligence?.resourceIntelligence,
                
                processingTime
            };
            
        } catch (error) {
            const processingTime = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            console.log(`   ❌ Error: ${errorMessage}`);
            
            return {
                filePath: fileName,
                fileType: 'unknown',
                fileExtension,
                fileSize: 0,
                hasIntelligence: false,
                hasResourceIntelligence: false,
                processingTime,
                error: errorMessage
            };
        }
    }
    
    /**
     * Generates comprehensive real-world analysis report
     */
    private generateRealWorldReport(results: RealWorldTestResult[], totalFilesFound: number, totalElapsedSeconds: number, totalProcessingTime: number): void {
        console.log('🌍 REAL-WORLD MODS ANALYSIS REPORT');
        console.log('==================================================\n');

        // Large-scale performance metrics
        const throughputPerSecond = Math.round(results.length / totalElapsedSeconds);
        const avgProcessingTime = Math.round(totalProcessingTime / results.length);
        const processedSizeMB = Math.round(results.reduce((sum, r) => sum + r.fileSize, 0) / 1024 / 1024);

        console.log('🚀 LARGE-SCALE PERFORMANCE:');
        console.log(`   Total Processing Time: ${totalElapsedSeconds}s (${Math.round(totalElapsedSeconds/60)}m ${totalElapsedSeconds%60}s)`);
        console.log(`   Throughput: ${throughputPerSecond} files/second`);
        console.log(`   Average Processing: ${avgProcessingTime}ms per file`);
        console.log(`   Total Data Processed: ${processedSizeMB} MB`);
        console.log(`   Data Throughput: ${Math.round(processedSizeMB / totalElapsedSeconds)} MB/second\n`);
        
        // Basic statistics
        const totalTested = results.length;
        const successfulFiles = results.filter(r => !r.error).length;
        const scriptFiles = results.filter(r => r.fileExtension === '.ts4script');
        const packageFiles = results.filter(r => r.fileExtension === '.package');
        
        console.log('📊 COLLECTION OVERVIEW:');
        console.log(`   Total Files Found: ${totalFilesFound}`);
        console.log(`   Files Tested: ${totalTested}`);
        console.log(`   Successful Analysis: ${successfulFiles}/${totalTested} (${Math.round((successfulFiles/totalTested)*100)}%)`);
        console.log(`   Script Files (.ts4script): ${scriptFiles.length}`);
        console.log(`   Package Files (.package): ${packageFiles.length}\n`);
        
        // File size analysis
        const totalSize = results.reduce((sum, r) => sum + r.fileSize, 0);
        const avgSize = Math.round(totalSize / results.length / 1024); // KB
        const totalSizeMB = Math.round(totalSize / 1024 / 1024);
        
        console.log('📊 COLLECTION SIZE:');
        console.log(`   Total Collection Size: ${totalSizeMB} MB`);
        console.log(`   Average File Size: ${avgSize} KB`);
        console.log(`   Largest File: ${Math.round(Math.max(...results.map(r => r.fileSize)) / 1024)} KB`);
        console.log(`   Smallest File: ${Math.round(Math.min(...results.map(r => r.fileSize)) / 1024)} KB\n`);
        
        // Phase 3A: Metadata Analysis
        this.analyzeRealWorldMetadata(results, scriptFiles, packageFiles);
        
        // Phase 4A: Intelligence Analysis
        this.analyzeRealWorldIntelligence(results, scriptFiles, packageFiles);
        
        // Performance Analysis
        this.analyzeRealWorldPerformance(results);
        
        // Quality Assessment
        this.analyzeRealWorldQuality(results);
        
        // Creator Analysis
        this.analyzeCreators(results);
        
        // Error Analysis
        this.analyzeRealWorldErrors(results);
        
        // Overall Assessment
        this.generateRealWorldAssessment(results, totalFilesFound);
    }
    
    private analyzeRealWorldMetadata(results: RealWorldTestResult[], scriptFiles: RealWorldTestResult[], packageFiles: RealWorldTestResult[]): void {
        console.log('📝 METADATA EXTRACTION PERFORMANCE:');
        
        const filesWithAuthor = results.filter(r => r.author).length;
        const filesWithVersion = results.filter(r => r.version).length;
        const filesWithModName = results.filter(r => r.modName).length;
        
        const authorPercentage = Math.round((filesWithAuthor / results.length) * 100);
        const versionPercentage = Math.round((filesWithVersion / results.length) * 100);
        const modNamePercentage = Math.round((filesWithModName / results.length) * 100);
        
        console.log(`   Overall Author Detection: ${filesWithAuthor}/${results.length} (${authorPercentage}%)`);
        console.log(`   Overall Version Detection: ${filesWithVersion}/${results.length} (${versionPercentage}%)`);
        console.log(`   Overall Mod Name Detection: ${filesWithModName}/${results.length} (${modNamePercentage}%)`);
        
        if (scriptFiles.length > 0) {
            const scriptWithAuthor = scriptFiles.filter(r => r.author).length;
            console.log(`   Script Author Detection: ${scriptWithAuthor}/${scriptFiles.length} (${Math.round((scriptWithAuthor/scriptFiles.length)*100)}%)`);
        }
        
        if (packageFiles.length > 0) {
            const packageWithAuthor = packageFiles.filter(r => r.author).length;
            console.log(`   Package Author Detection: ${packageWithAuthor}/${packageFiles.length} (${Math.round((packageWithAuthor/packageFiles.length)*100)}%)`);
        }
        console.log();
    }
    
    private analyzeRealWorldIntelligence(results: RealWorldTestResult[], scriptFiles: RealWorldTestResult[], packageFiles: RealWorldTestResult[]): void {
        console.log('🧠 INTELLIGENCE INTEGRATION PERFORMANCE:');
        
        const filesWithIntelligence = results.filter(r => r.hasIntelligence).length;
        const filesWithResourceIntelligence = results.filter(r => r.hasResourceIntelligence).length;
        const filesWithQuality = results.filter(r => r.qualityScore !== undefined).length;
        
        const intelligencePercentage = Math.round((filesWithIntelligence / results.length) * 100);
        const resourceIntelligencePercentage = Math.round((filesWithResourceIntelligence / results.length) * 100);
        const qualityPercentage = Math.round((filesWithQuality / results.length) * 100);
        
        console.log(`   Intelligence Data: ${filesWithIntelligence}/${results.length} (${intelligencePercentage}%)`);
        console.log(`   Resource Intelligence: ${filesWithResourceIntelligence}/${results.length} (${resourceIntelligencePercentage}%)`);
        console.log(`   Quality Assessment: ${filesWithQuality}/${results.length} (${qualityPercentage}%)\n`);
    }
    
    private analyzeRealWorldPerformance(results: RealWorldTestResult[]): void {
        console.log('⏱️ PERFORMANCE ANALYSIS:');
        
        const processingTimes = results.map(r => r.processingTime);
        const avgTime = Math.round(processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length);
        const maxTime = Math.max(...processingTimes);
        const minTime = Math.min(...processingTimes);
        
        console.log(`   Average Processing Time: ${avgTime}ms`);
        console.log(`   Maximum Processing Time: ${maxTime}ms`);
        console.log(`   Minimum Processing Time: ${minTime}ms`);
        console.log(`   Performance Target: <10ms average ✅ ${avgTime <= 10 ? 'ACHIEVED' : 'NEEDS OPTIMIZATION'}\n`);
    }
    
    private analyzeRealWorldQuality(results: RealWorldTestResult[]): void {
        const qualityScores = results.filter(r => r.qualityScore !== undefined).map(r => r.qualityScore!);
        
        if (qualityScores.length > 0) {
            console.log('⭐ QUALITY ASSESSMENT:');
            
            const avgQuality = Math.round(qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length);
            const maxQuality = Math.max(...qualityScores);
            const minQuality = Math.min(...qualityScores);
            
            console.log(`   Average Quality Score: ${avgQuality}/100`);
            console.log(`   Quality Range: ${minQuality} - ${maxQuality}`);
            console.log(`   Files with Quality Data: ${qualityScores.length}/${results.length}\n`);
        }
    }
    
    private analyzeCreators(results: RealWorldTestResult[]): void {
        const creators = new Map<string, number>();
        
        results.forEach(result => {
            if (result.author) {
                creators.set(result.author, (creators.get(result.author) || 0) + 1);
            }
        });
        
        if (creators.size > 0) {
            console.log('👥 DETECTED CREATORS:');
            
            const sortedCreators = Array.from(creators.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10); // Top 10 creators
            
            sortedCreators.forEach(([creator, count]) => {
                console.log(`   ${creator}: ${count} mod${count > 1 ? 's' : ''}`);
            });
            
            console.log(`   Total Unique Creators: ${creators.size}\n`);
        }
    }
    
    private analyzeRealWorldErrors(results: RealWorldTestResult[]): void {
        const errors = results.filter(r => r.error);
        
        if (errors.length > 0) {
            console.log('❌ ERROR ANALYSIS:');
            console.log(`   Files with Errors: ${errors.length}/${results.length} (${Math.round((errors.length/results.length)*100)}%)`);
            
            // Group errors by type
            const errorTypes = new Map<string, number>();
            errors.forEach(error => {
                const errorType = error.error?.split(':')[0] || 'Unknown';
                errorTypes.set(errorType, (errorTypes.get(errorType) || 0) + 1);
            });
            
            console.log('   Error Types:');
            errorTypes.forEach((count, type) => {
                console.log(`     ${type}: ${count} file${count > 1 ? 's' : ''}`);
            });
            console.log();
        }
    }
    
    private generateRealWorldAssessment(results: RealWorldTestResult[], totalFilesFound: number): void {
        console.log('🎯 PRODUCTION READINESS ASSESSMENT:');

        const successRate = Math.round((results.filter(r => !r.error).length / results.length) * 100);
        const authorRate = Math.round((results.filter(r => r.author).length / results.length) * 100);
        const intelligenceRate = Math.round((results.filter(r => r.hasIntelligence).length / results.length) * 100);
        const avgTime = Math.round(results.reduce((sum, r) => sum + r.processingTime, 0) / results.length);
        const errorRate = Math.round((results.filter(r => r.error).length / results.length) * 100);

        console.log(`   System Reliability: ${successRate}% (${100-errorRate}% success rate)`);
        console.log(`   Metadata Accuracy: ${authorRate}% author detection`);
        console.log(`   Intelligence Coverage: ${intelligenceRate}%`);
        console.log(`   Performance: ${avgTime}ms average per file`);
        console.log(`   Error Rate: ${errorRate}%`);

        // Production readiness criteria
        const reliabilityPass = successRate >= 95;
        const accuracyPass = authorRate >= 80;
        const intelligencePass = intelligenceRate >= 90;
        const performancePass = avgTime <= 1000; // 1 second average for large collections
        const errorRatePass = errorRate <= 5;

        console.log('\n📋 PRODUCTION CRITERIA:');
        console.log(`   ✅ Reliability (≥95%): ${reliabilityPass ? '✅ PASS' : '❌ FAIL'} (${successRate}%)`);
        console.log(`   ✅ Accuracy (≥80%): ${accuracyPass ? '✅ PASS' : '❌ FAIL'} (${authorRate}%)`);
        console.log(`   ✅ Intelligence (≥90%): ${intelligencePass ? '✅ PASS' : '❌ FAIL'} (${intelligenceRate}%)`);
        console.log(`   ✅ Performance (≤1000ms): ${performancePass ? '✅ PASS' : '❌ FAIL'} (${avgTime}ms)`);
        console.log(`   ✅ Error Rate (≤5%): ${errorRatePass ? '✅ PASS' : '❌ FAIL'} (${errorRate}%)`);

        const allCriteriaMet = reliabilityPass && accuracyPass && intelligencePass && performancePass && errorRatePass;

        if (allCriteriaMet) {
            console.log('\n🏆 PRODUCTION STATUS: READY FOR DEPLOYMENT');
            console.log('   ✅ All production criteria met');
            console.log('   ✅ Handles large-scale mod collections');
            console.log('   ✅ Reliable performance with real-world data');
            console.log('   🚀 SYSTEM IS PRODUCTION READY');
        } else {
            console.log('\n⚠️ PRODUCTION STATUS: NEEDS OPTIMIZATION');
            console.log('   📋 Address failing criteria before production deployment');

            if (!reliabilityPass) console.log('   🔧 Improve error handling and system stability');
            if (!accuracyPass) console.log('   🔧 Enhance metadata extraction patterns');
            if (!intelligencePass) console.log('   🔧 Improve intelligence analysis coverage');
            if (!performancePass) console.log('   🔧 Optimize processing speed for large files');
            if (!errorRatePass) console.log('   🔧 Reduce error rate through better validation');
        }

        // Scalability assessment
        console.log(`\n📊 SCALABILITY VALIDATION:`);
        console.log(`   Collection Size: ${totalFilesFound} files`);
        console.log(`   Processing Capability: ${totalFilesFound >= 1000 ? '✅ Large-scale proven' : '⚠️ Needs large-scale testing'}`);
        console.log(`   Memory Efficiency: ${avgTime < 2000 ? '✅ Efficient' : '⚠️ Needs optimization'}`);
    }
}

// Run the real-world test
async function main() {
    const tester = new RealWorldModTester();
    await tester.testRealWorldMods();
}

if (require.main === module) {
    main().catch(console.error);
}
