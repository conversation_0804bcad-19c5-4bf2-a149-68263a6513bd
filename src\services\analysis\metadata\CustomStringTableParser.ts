/**
 * Custom StringTable Parser (LlamaLogic-style)
 * 
 * Handles multiple STBL versions and format variations that S4TK cannot parse.
 * Based on LlamaLogic.Packages StringTableModel implementation.
 */

import { DEFAULT_METADATA_CONFIG, type MetadataExtractionConfig } from '../../../config/metadata-extraction.config';

export interface StringTableEntry {
    readonly keyHash: number;
    readonly value: string;
    readonly flags?: number;
}

export interface StringTableMetadata {
    readonly modName?: string;
    readonly description?: string;
    readonly author?: string;
    readonly confidence: number;
    readonly source: 'stringtable';
    readonly version: number;
    readonly entryCount: number;
    readonly locale?: string;
    readonly processingTime: number;
}

export interface ParsedStringTable {
    readonly version: number;
    readonly compressed: boolean;
    readonly entries: Map<number, string>;
    readonly locale?: string;
    readonly stringLength: number;
}

/**
 * Custom StringTable parser that handles multiple versions
 */
export class CustomStringTableParser {
    private readonly config: MetadataExtractionConfig;
    private static readonly EXPECTED_IDENTIFIER = new Uint8Array([0x53, 0x54, 0x42, 0x4C]); // "STBL"
    
    constructor(config: Partial<MetadataExtractionConfig> = {}) {
        this.config = {
            ...DEFAULT_METADATA_CONFIG,
            ...config
        };
    }
    
    /**
     * Parses StringTable buffer and extracts metadata
     */
    async parseStringTable(buffer: Buffer): Promise<StringTableMetadata | null> {
        const startTime = performance.now();
        
        try {
            // Parse the StringTable structure
            const parsedTable = this.parseStringTableStructure(buffer);
            if (!parsedTable) {
                return null;
            }
            
            // Extract metadata from entries
            const metadata = this.extractMetadataFromEntries(parsedTable);
            
            const processingTime = performance.now() - startTime;
            
            return {
                ...metadata,
                source: 'stringtable',
                version: parsedTable.version,
                entryCount: parsedTable.entries.size,
                locale: parsedTable.locale,
                processingTime
            };
            
        } catch (error) {
            console.warn('[CustomStringTableParser] Parsing failed:', error);
            return null;
        }
    }
    
    /**
     * Parses the binary StringTable structure
     */
    private parseStringTableStructure(buffer: Buffer): ParsedStringTable | null {
        if (buffer.length < 21) {
            return null; // Too small to be a valid STBL
        }
        
        let readPosition = 0;
        
        // Check file identifier "STBL"
        const identifier = buffer.subarray(0, 4);
        if (!this.arraysEqual(identifier, CustomStringTableParser.EXPECTED_IDENTIFIER)) {
            return null;
        }
        readPosition += 4;
        
        // Read version
        const version = buffer.readUInt16LE(readPosition);
        readPosition += 2;
        
        // Check if version is supported
        if (!this.config.stringTable.supportedVersions.includes(version)) {
            console.warn(`[CustomStringTableParser] Unsupported STBL version: ${version}`);
            return null;
        }
        
        // Read compression flag
        const compressed = buffer.readUInt8(readPosition) !== 0;
        readPosition += 1;
        
        // Read number of entries
        const numEntries = this.readUInt64LE(buffer, readPosition);
        readPosition += 8;
        
        // Skip reserved bytes (2 bytes)
        readPosition += 2;
        
        // Read string length
        const stringLength = buffer.readUInt32LE(readPosition);
        readPosition += 4;
        
        // Parse entries
        const entries = new Map<number, string>();
        
        try {
            for (let i = 0; i < numEntries; i++) {
                if (readPosition >= buffer.length) {
                    break; // Prevent reading beyond buffer
                }
                
                // Read key hash
                const keyHash = buffer.readUInt32LE(readPosition);
                readPosition += 4;
                
                // Read flags (1 byte)
                const flags = buffer.readUInt8(readPosition);
                readPosition += 1;
                
                // Read string length
                const strLength = buffer.readUInt16LE(readPosition);
                readPosition += 2;
                
                // Validate string length
                if (strLength > this.config.stringTable.maxStringLength || 
                    readPosition + strLength > buffer.length) {
                    console.warn(`[CustomStringTableParser] Invalid string length: ${strLength}`);
                    continue;
                }
                
                // Read string
                let value = '';
                if (strLength > 0) {
                    const stringBuffer = buffer.subarray(readPosition, readPosition + strLength);
                    value = stringBuffer.toString(this.config.stringTable.encoding);
                    readPosition += strLength;
                }
                
                // Only add if we don't already have this key (handle duplicates)
                if (!entries.has(keyHash)) {
                    entries.set(keyHash, value);
                }
            }
        } catch (error) {
            console.warn('[CustomStringTableParser] Error parsing entries:', error);
            // Return partial results if we got some entries
            if (entries.size === 0) {
                return null;
            }
        }
        
        return {
            version,
            compressed,
            entries,
            stringLength
        };
    }
    
    /**
     * Extracts metadata from StringTable entries
     */
    private extractMetadataFromEntries(parsedTable: ParsedStringTable): Omit<StringTableMetadata, 'source' | 'version' | 'entryCount' | 'locale' | 'processingTime'> {
        const metadata: {
            modName?: string;
            description?: string;
            author?: string;
            confidence: number;
        } = {
            confidence: 0
        };
        
        // Convert entries to key-value pairs for pattern matching
        const stringEntries: Array<{ key: string; value: string }> = [];
        
        for (const [keyHash, value] of parsedTable.entries) {
            // Try to reverse-engineer meaningful keys from common patterns
            // This is a limitation since we only have hashes, not original keys
            stringEntries.push({
                key: keyHash.toString(16).toUpperCase(), // Use hex representation
                value: value
            });
        }
        
        // Extract mod name
        metadata.modName = this.extractByPatterns(
            stringEntries,
            this.config.stringTable.keyPatterns.modName,
            'modName'
        );
        
        // Extract description
        metadata.description = this.extractByPatterns(
            stringEntries,
            this.config.stringTable.keyPatterns.description,
            'description'
        );
        
        // Extract author
        metadata.author = this.extractByPatterns(
            stringEntries,
            this.config.stringTable.keyPatterns.author,
            'author'
        );
        
        // Since we're working with hashes, also try content-based detection
        const contentBasedMetadata = this.extractByContent(stringEntries);
        
        // Merge content-based results
        if (!metadata.modName && contentBasedMetadata.modName) {
            metadata.modName = contentBasedMetadata.modName;
        }
        if (!metadata.description && contentBasedMetadata.description) {
            metadata.description = contentBasedMetadata.description;
        }
        if (!metadata.author && contentBasedMetadata.author) {
            metadata.author = contentBasedMetadata.author;
        }
        
        // Calculate confidence
        metadata.confidence = this.calculateStringTableConfidence(metadata, parsedTable.entries.size);
        
        return metadata;
    }
    
    /**
     * Extracts metadata by pattern matching (limited effectiveness with hashes)
     */
    private extractByPatterns(
        entries: Array<{ key: string; value: string }>,
        patterns: readonly RegExp[],
        type: string
    ): string | undefined {
        for (const entry of entries) {
            for (const pattern of patterns) {
                if (pattern.test(entry.key) && this.isValidMetadataValue(entry.value, type)) {
                    return this.sanitizeMetadataValue(entry.value);
                }
            }
        }
        return undefined;
    }
    
    /**
     * Extracts metadata by analyzing content patterns
     */
    private extractByContent(entries: Array<{ key: string; value: string }>): {
        modName?: string;
        description?: string;
        author?: string;
    } {
        const result: {
            modName?: string;
            description?: string;
            author?: string;
        } = {};
        
        for (const entry of entries) {
            const value = entry.value.trim();
            
            // Skip very short or very long values
            if (value.length < 3 || value.length > 200) {
                continue;
            }
            
            // Skip values that look like IDs or technical strings
            if (/^[A-F0-9]{8,}$/i.test(value) || /^\d+$/.test(value)) {
                continue;
            }
            
            // Look for potential mod names (shorter, title-case strings)
            if (!result.modName && this.looksLikeModName(value)) {
                result.modName = this.sanitizeMetadataValue(value);
            }
            
            // Look for potential descriptions (longer, sentence-like strings)
            if (!result.description && this.looksLikeDescription(value)) {
                result.description = this.sanitizeMetadataValue(value);
            }
            
            // Look for potential author names
            if (!result.author && this.looksLikeAuthor(value)) {
                result.author = this.sanitizeMetadataValue(value);
            }
        }
        
        return result;
    }
    
    /**
     * Checks if a value looks like a mod name
     */
    private looksLikeModName(value: string): boolean {
        return value.length >= 5 && 
               value.length <= 50 &&
               /[a-zA-Z]/.test(value) &&
               !value.includes('\n') &&
               !/^[a-z\s]+$/.test(value); // Not all lowercase
    }
    
    /**
     * Checks if a value looks like a description
     */
    private looksLikeDescription(value: string): boolean {
        return value.length >= 20 && 
               value.length <= 500 &&
               /[a-zA-Z]/.test(value) &&
               (value.includes(' ') || value.includes('.'));
    }
    
    /**
     * Checks if a value looks like an author name
     */
    private looksLikeAuthor(value: string): boolean {
        return value.length >= 2 && 
               value.length <= 30 &&
               /[a-zA-Z]/.test(value) &&
               !value.includes('\n') &&
               !/\d{4,}/.test(value); // No long numbers
    }
    
    /**
     * Validates metadata value
     */
    private isValidMetadataValue(value: string, type: string): boolean {
        if (!value || typeof value !== 'string') {
            return false;
        }
        
        const trimmed = value.trim();
        return trimmed.length > 0 && trimmed.length <= this.config.stringTable.maxStringLength;
    }
    
    /**
     * Sanitizes metadata value
     */
    private sanitizeMetadataValue(value: string): string {
        return value.trim()
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control chars
            .substring(0, this.config.stringTable.maxStringLength);
    }
    
    /**
     * Calculates confidence for StringTable metadata
     */
    private calculateStringTableConfidence(
        metadata: { modName?: string; description?: string; author?: string },
        entryCount: number
    ): number {
        let confidence = 20; // Base confidence for successful parsing
        
        if (metadata.modName) confidence += 30;
        if (metadata.description) confidence += 25;
        if (metadata.author) confidence += 20;
        
        // Boost for having multiple entries (more likely to contain metadata)
        if (entryCount > 10) confidence += 10;
        if (entryCount > 50) confidence += 10;
        
        return Math.min(confidence, 100);
    }
    
    /**
     * Reads 64-bit little-endian integer
     */
    private readUInt64LE(buffer: Buffer, offset: number): number {
        // JavaScript can't handle full 64-bit integers safely
        // Read as two 32-bit integers and combine (assuming high part is 0)
        const low = buffer.readUInt32LE(offset);
        const high = buffer.readUInt32LE(offset + 4);
        
        if (high !== 0) {
            throw new Error('64-bit integer too large for JavaScript');
        }
        
        return low;
    }
    
    /**
     * Compares two Uint8Arrays for equality
     */
    private arraysEqual(a: Uint8Array, b: Uint8Array): boolean {
        if (a.length !== b.length) return false;
        for (let i = 0; i < a.length; i++) {
            if (a[i] !== b[i]) return false;
        }
        return true;
    }
}
