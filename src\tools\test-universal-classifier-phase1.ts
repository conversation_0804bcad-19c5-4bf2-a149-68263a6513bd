/**
 * Test Universal Classifier - Phase 1
 * 
 * Tests the refactored universal classifier to ensure hair detection
 * continues working with zero regression after refactoring.
 */

import { UniversalSubcategoryClassifier } from '../services/analysis/specialized/cas/UniversalSubcategoryClassifier';
import { HairStyleClassifier } from '../services/analysis/specialized/cas/HairStyleClassifier';

/**
 * Test hair classification backward compatibility
 */
function testHairClassificationCompatibility() {
    console.log('🧪 Testing Hair Classification Backward Compatibility...\n');
    
    const testFiles = [
        'creator_long_wavy_ponytail_hair.package',
        'short_curly_bob_hair.package',
        'medium_straight_bangs_hair.package',
        'very_long_braided_hair_with_bow.package',
        'pixie_cut_hair.package'
    ];
    
    for (const filename of testFiles) {
        console.log(`📁 Testing: ${filename}`);
        
        // Test original classifier
        const originalResult = HairStyleClassifier.classifyHairStyle(filename);
        
        // Test universal classifier (backward compatibility)
        const universalResult = UniversalSubcategoryClassifier.classifyHairStyle(filename);
        
        // Compare results
        console.log(`   Original - Length: ${originalResult.length}, Style: ${originalResult.style.join(', ')}, Texture: ${originalResult.texture}`);
        console.log(`   Universal - Length: ${universalResult.length}, Style: ${universalResult.style.join(', ')}, Texture: ${universalResult.texture}`);
        console.log(`   Confidence: Original ${originalResult.confidence.toFixed(2)}, Universal ${universalResult.confidence.toFixed(2)}`);
        
        // Check for regressions
        const hasRegression = 
            originalResult.length !== universalResult.length ||
            originalResult.texture !== universalResult.texture ||
            Math.abs(originalResult.confidence - universalResult.confidence) > 0.2;
        
        if (hasRegression) {
            console.log(`   ❌ REGRESSION DETECTED!`);
        } else {
            console.log(`   ✅ No regression`);
        }
        
        console.log('');
    }
}

/**
 * Test universal classification functionality
 */
function testUniversalClassification() {
    console.log('🔬 Testing Universal Classification Functionality...\n');
    
    const testFiles = [
        { filename: 'creator_long_wavy_ponytail_hair.package', category: 'hair' },
        { filename: 'formal_dress_clothing.package', category: 'clothing' },
        { filename: 'red_lipstick_makeup.package', category: 'makeup' }
    ];
    
    for (const test of testFiles) {
        console.log(`📁 Testing: ${test.filename} (${test.category})`);
        
        const result = UniversalSubcategoryClassifier.classifySubcategories(
            test.filename, 
            test.category
        );
        
        console.log(`   Category: ${result.category}`);
        console.log(`   Subcategories: ${result.subcategories.join(', ')}`);
        console.log(`   Confidence: ${result.confidence.toFixed(2)}`);
        console.log(`   Tags: ${result.tags.join(', ')}`);
        console.log(`   Keywords: ${result.keywords.join(', ')}`);
        console.log(`   Description: ${UniversalSubcategoryClassifier.getDescription(result)}`);
        console.log('');
    }
}

/**
 * Test pattern configuration
 */
function testPatternConfiguration() {
    console.log('⚙️ Testing Pattern Configuration...\n');
    
    const { SubcategoryPatternConfig } = require('../services/analysis/specialized/cas/SubcategoryPatternConfig');
    
    // Test pattern retrieval
    const hairPatterns = SubcategoryPatternConfig.getPatternsByCategory('hair');
    console.log(`📊 Hair patterns loaded: ${hairPatterns.length}`);
    
    const lengthPatterns = SubcategoryPatternConfig.getPatternsByPriority(1);
    console.log(`📊 Priority 1 patterns: ${lengthPatterns.length}`);
    
    const allPatterns = SubcategoryPatternConfig.getAllHairPatterns();
    console.log(`📊 Total hair patterns: ${allPatterns.length}`);
    
    // Test pattern structure
    if (allPatterns.length > 0) {
        const samplePattern = allPatterns[0];
        console.log(`📋 Sample pattern structure:`);
        console.log(`   Category: ${samplePattern.category}`);
        console.log(`   Subcategory: ${samplePattern.subcategory}`);
        console.log(`   Patterns: ${samplePattern.patterns.join(', ')}`);
        console.log(`   Confidence: ${samplePattern.confidence}`);
        console.log(`   Priority: ${samplePattern.priority}`);
    }
    
    console.log('');
}

/**
 * Main test execution
 */
function main() {
    console.log('🚀 Universal Subcategory Classifier - Phase 1 Testing\n');
    console.log('Testing refactored hair classification for zero regression...\n');
    
    try {
        testPatternConfiguration();
        testHairClassificationCompatibility();
        testUniversalClassification();
        
        console.log('✅ Phase 1 testing completed successfully!');
        console.log('🎯 Hair classification refactored to universal system with zero regression');
        
    } catch (error) {
        console.error('❌ Testing failed:', error);
        process.exit(1);
    }
}

// Run tests
main();
