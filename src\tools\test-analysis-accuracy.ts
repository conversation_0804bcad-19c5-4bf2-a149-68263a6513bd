/**
 * Analysis Accuracy Validation Tool
 * 
 * Tests our package and script analysis against real mod files
 * to ensure we're extracting data accurately before implementing conflict detection.
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';
import { FileType, ModCategory } from '../types/analysis';

interface TestResult {
    filePath: string;
    success: boolean;
    fileType: FileType;
    category: ModCategory;
    resourceCount: number;
    extractedData: any;
    issues: string[];
    analysisTime: number;
}

class AnalysisAccuracyTester {
    private analysisService: PackageAnalysisService;
    private testResults: TestResult[] = [];

    constructor() {
        this.analysisService = new PackageAnalysisService();
    }

    /**
     * Run comprehensive analysis accuracy tests
     */
    public async runTests(): Promise<void> {
        console.log('🔍 Starting Analysis Accuracy Validation...\n');

        const assetsDir = path.join(process.cwd(), 'assets');
        
        if (!fs.existsSync(assetsDir)) {
            console.error('❌ Assets directory not found!');
            return;
        }

        const allFiles = fs.readdirSync(assetsDir);
        const packageFiles = allFiles.filter(file => file.endsWith('.package')).slice(0, 5);
        const scriptFiles = allFiles.filter(file => file.endsWith('.ts4script')).slice(0, 5);
        const testFiles = [...packageFiles, ...scriptFiles];

        console.log(`📁 Found ${testFiles.length} test files\n`);

        for (const fileName of testFiles) {
            await this.testFile(path.join(assetsDir, fileName));
        }

        this.generateReport();
    }

    /**
     * Test analysis of a single file
     */
    private async testFile(filePath: string): Promise<void> {
        const fileName = path.basename(filePath);
        console.log(`🔍 Testing: ${fileName}`);

        const startTime = Date.now();
        let result: TestResult;

        try {
            const buffer = fs.readFileSync(filePath);
            const analysis = this.analysisService.analyzePackage(buffer, filePath);

            result = {
                filePath: fileName,
                success: true,
                fileType: analysis.fileType,
                category: analysis.category,
                resourceCount: analysis.resourceCount,
                extractedData: this.extractKeyData(analysis),
                issues: this.validateAnalysis(analysis, fileName),
                analysisTime: Date.now() - startTime
            };

            console.log(`   ✅ Success: ${analysis.fileType} | ${analysis.category} | ${analysis.resourceCount} resources | ${result.analysisTime}ms`);

            // Show script metadata if available
            if (analysis.metadata && analysis.metadata.scriptInfo) {
                const scriptInfo = analysis.metadata.scriptInfo;
                console.log(`   📝 Script Info: ${scriptInfo.author || 'No author'} | ${scriptInfo.version || 'No version'} | ${scriptInfo.description || 'No description'}`);
            }

            if (result.issues.length > 0) {
                console.log(`   ⚠️  Issues: ${result.issues.join(', ')}`);
            }

        } catch (error) {
            result = {
                filePath: fileName,
                success: false,
                fileType: FileType.UNKNOWN,
                category: ModCategory.UNKNOWN,
                resourceCount: 0,
                extractedData: null,
                issues: [`Analysis failed: ${error instanceof Error ? error.message : String(error)}`],
                analysisTime: Date.now() - startTime
            };

            console.log(`   ❌ Failed: ${result.issues[0]}`);
        }

        this.testResults.push(result);
        console.log(''); // Empty line for readability
    }

    /**
     * Extract key data points for validation
     */
    private extractKeyData(analysis: any): any {
        return {
            hasResources: analysis.resources && analysis.resources.length > 0,
            hasMetadata: analysis.metadata && Object.keys(analysis.metadata).length > 0,
            hasDependencies: analysis.dependencies && analysis.dependencies.length > 0,
            suggestedPath: analysis.suggestedPath,
            subcategory: analysis.subcategory,
            isOverride: analysis.isOverride
        };
    }

    /**
     * Validate analysis results for common issues
     */
    private validateAnalysis(analysis: any, fileName: string): string[] {
        const issues: string[] = [];

        // Check file type detection
        if (analysis.fileType === FileType.UNKNOWN) {
            issues.push('File type not detected');
        }

        // Check category detection
        if (analysis.category === ModCategory.UNKNOWN) {
            issues.push('Category not detected');
        }

        // Check resource extraction for .package files
        if (fileName.endsWith('.package')) {
            if (analysis.resourceCount === 0) {
                issues.push('No resources extracted from package file');
            }
            if (!analysis.resources || analysis.resources.length === 0) {
                issues.push('Resources array is empty');
            }
        }

        // Check script analysis for .ts4script files
        if (fileName.endsWith('.ts4script')) {
            if (!analysis.metadata || !analysis.metadata.scriptInfo) {
                issues.push('Script metadata not extracted');
                console.log(`   🔍 Debug: metadata keys = ${analysis.metadata ? Object.keys(analysis.metadata).join(', ') : 'none'}`);
            } else {
                const scriptInfo = analysis.metadata.scriptInfo;
                console.log(`   📝 Script Info: ${scriptInfo.author || 'No author'} | ${scriptInfo.version || 'No version'} | ${scriptInfo.subcategory || 'No subcategory'}`);
            }
        }

        // Check for missing metadata
        if (!analysis.metadata || Object.keys(analysis.metadata).length === 0) {
            issues.push('No metadata extracted');
        }

        // Check analysis time (should be reasonable)
        if (analysis.analysisTime && analysis.analysisTime > 5000) {
            issues.push('Analysis took too long (>5s)');
        }

        return issues;
    }

    /**
     * Generate comprehensive test report
     */
    private generateReport(): void {
        console.log('📊 ANALYSIS ACCURACY REPORT');
        console.log('=' .repeat(50));

        const totalTests = this.testResults.length;
        const successfulTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - successfulTests;

        console.log(`\n📈 SUMMARY:`);
        console.log(`   Total Tests: ${totalTests}`);
        console.log(`   Successful: ${successfulTests} (${Math.round(successfulTests/totalTests*100)}%)`);
        console.log(`   Failed: ${failedTests} (${Math.round(failedTests/totalTests*100)}%)`);

        // Performance metrics
        const avgTime = this.testResults.reduce((sum, r) => sum + r.analysisTime, 0) / totalTests;
        const maxTime = Math.max(...this.testResults.map(r => r.analysisTime));
        console.log(`\n⏱️  PERFORMANCE:`);
        console.log(`   Average Time: ${Math.round(avgTime)}ms`);
        console.log(`   Max Time: ${maxTime}ms`);

        // File type detection
        const packageFiles = this.testResults.filter(r => r.filePath.endsWith('.package'));
        const scriptFiles = this.testResults.filter(r => r.filePath.endsWith('.ts4script'));
        
        console.log(`\n📦 PACKAGE FILES (${packageFiles.length}):`);
        packageFiles.forEach(r => {
            const status = r.success ? '✅' : '❌';
            console.log(`   ${status} ${r.filePath}: ${r.category} | ${r.resourceCount} resources`);
        });

        console.log(`\n🐍 SCRIPT FILES (${scriptFiles.length}):`);
        scriptFiles.forEach(r => {
            const status = r.success ? '✅' : '❌';
            console.log(`   ${status} ${r.filePath}: ${r.category}`);
        });

        // Issues summary
        const allIssues = this.testResults.flatMap(r => r.issues);
        const uniqueIssues = [...new Set(allIssues)];
        
        if (uniqueIssues.length > 0) {
            console.log(`\n⚠️  COMMON ISSUES:`);
            uniqueIssues.forEach(issue => {
                const count = allIssues.filter(i => i === issue).length;
                console.log(`   • ${issue} (${count} files)`);
            });
        }

        // Recommendations
        console.log(`\n💡 RECOMMENDATIONS:`);
        if (failedTests > 0) {
            console.log(`   • Fix ${failedTests} failed analyses before proceeding to conflict detection`);
        }
        if (avgTime > 1000) {
            console.log(`   • Optimize performance - average analysis time is ${Math.round(avgTime)}ms`);
        }
        if (uniqueIssues.some(i => i.includes('resources'))) {
            console.log(`   • Improve resource extraction accuracy`);
        }
        if (uniqueIssues.some(i => i.includes('metadata'))) {
            console.log(`   • Enhance metadata extraction`);
        }

        console.log(`\n🎯 NEXT STEPS:`);
        if (successfulTests / totalTests >= 0.9) {
            console.log(`   ✅ Analysis accuracy is good (${Math.round(successfulTests/totalTests*100)}%) - ready for conflict detection`);
        } else {
            console.log(`   ⚠️  Analysis accuracy needs improvement (${Math.round(successfulTests/totalTests*100)}%) - fix issues before proceeding`);
        }
    }
}

// Run the tests
async function main() {
    const tester = new AnalysisAccuracyTester();
    await tester.runTests();
}

if (require.main === module) {
    main().catch(console.error);
}

export { AnalysisAccuracyTester };
