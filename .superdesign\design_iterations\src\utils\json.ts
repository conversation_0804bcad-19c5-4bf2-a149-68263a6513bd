/**
 * A custom JSON replacer function that handles BigInt values by converting
 * them to strings.
 * @param key The key of the value being stringified.
 * @param value The value to stringify.
 * @returns The original value, or the BigInt as a string.
 */
export function jsonReplacer(key: string, value: any): any {
    if (typeof value === 'bigint') {
        return value.toString();
    }
    return value;
}