import { FileType, ModCategory } from '../../../types/analysis';

/**
 * File metadata result
 */
interface FileMetadata {
    type: string;
    category: string;
    subcategory: string;
    confidence: number;
}

/**
 * Service for detecting file types and basic categorization
 * Focused on file extension and basic pattern analysis
 */
export class FileTypeDetector {
    
    /**
     * Analyzes file type and provides basic categorization
     */
    public static analyzeFile(filePath: string, fileSize: number): FileMetadata {
        const fileName = filePath.toLowerCase();
        const extension = this.getFileExtension(fileName);
        
        // Determine file type
        const fileType = this.determineFileType(extension, fileName);
        
        // Determine category based on file type and name patterns
        const category = this.determineCategory(fileType, fileName, fileSize);
        
        // Determine subcategory for more specific classification
        const subcategory = this.determineSubcategory(fileType, fileName);
        
        // Calculate confidence based on various factors
        const confidence = this.calculateConfidence(fileType, fileName, fileSize);
        
        return {
            type: fileType,
            category,
            subcategory,
            confidence
        };
    }
    
    /**
     * Extracts file extension from file path
     */
    private static getFileExtension(fileName: string): string {
        const lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1) : '';
    }
    
    /**
     * Determines file type based on extension
     */
    private static determineFileType(extension: string, fileName: string): string {
        switch (extension) {
            case 'package':
                return 'package';
            case 'ts4script':
                return 'script';
            case 'zip':
            case 'rar':
            case '7z':
                return 'archive';
            case 'txt':
            case 'md':
            case 'readme':
                return 'documentation';
            case 'png':
            case 'jpg':
            case 'jpeg':
            case 'gif':
                return 'image';
            default:
                return 'unknown';
        }
    }
    
    /**
     * Determines category based on file type and name patterns
     */
    private static determineCategory(fileType: string, fileName: string, fileSize: number): string {
        if (fileType === 'script') {
            return ModCategory.SCRIPT_MOD;
        }
        
        if (fileType !== 'package') {
            return ModCategory.UNKNOWN;
        }
        
        // Analyze filename patterns for package files
        return this.analyzePatternsForCategory(fileName, fileSize);
    }
    
    /**
     * Analyzes filename patterns to determine category
     */
    private static analyzePatternsForCategory(fileName: string, fileSize: number): string {
        // CAS (Create-A-Sim) patterns
        const casPatterns = [
            'cas', 'hair', 'clothing', 'accessory', 'makeup', 'skin',
            'eyes', 'eyebrows', 'facial', 'body', 'outfit', 'shoes'
        ];
        
        // Build/Buy patterns
        const buildBuyPatterns = [
            'furniture', 'decor', 'build', 'buy', 'object', 'clutter',
            'lighting', 'appliance', 'bathroom', 'bedroom', 'kitchen'
        ];
        
        // Override patterns
        const overridePatterns = [
            'override', 'replacement', 'replace', 'vanilla', 'basegame',
            'fix', 'patch', 'update', 'correction'
        ];
        
        // Tuning patterns
        const tuningPatterns = [
            'tuning', 'mod', 'gameplay', 'trait', 'career', 'skill',
            'interaction', 'buff', 'motive', 'relationship'
        ];
        
        // Check patterns in order of specificity
        if (this.containsAnyPattern(fileName, overridePatterns)) {
            return ModCategory.OVERRIDE;
        }
        
        if (this.containsAnyPattern(fileName, casPatterns)) {
            return ModCategory.CAS_CC;
        }
        
        if (this.containsAnyPattern(fileName, buildBuyPatterns)) {
            return ModCategory.BUILD_BUY_CC;
        }
        
        if (this.containsAnyPattern(fileName, tuningPatterns)) {
            return ModCategory.TUNING_MOD;
        }
        
        // Size-based heuristics
        if (fileSize > 50 * 1024 * 1024) { // > 50MB likely CC
            return ModCategory.BUILD_BUY_CC;
        }
        
        if (fileSize < 100 * 1024) { // < 100KB likely tuning
            return ModCategory.TUNING_MOD;
        }
        
        return ModCategory.UNKNOWN;
    }
    
    /**
     * Determines subcategory for more specific classification
     */
    private static determineSubcategory(fileType: string, fileName: string): string {
        if (fileType === 'script') {
            if (fileName.includes('ui_cheat') || fileName.includes('uicheat')) {
                return 'ui_cheat';
            }
            if (fileName.includes('mccc') || fileName.includes('mc_command')) {
                return 'mccc';
            }
            return 'general_script';
        }
        
        if (fileType === 'package') {
            // More specific subcategories based on patterns
            if (fileName.includes('hair')) return 'hair';
            if (fileName.includes('clothing')) return 'clothing';
            if (fileName.includes('furniture')) return 'furniture';
            if (fileName.includes('override')) return 'override';
        }
        
        return 'general';
    }
    
    /**
     * Calculates confidence score based on various factors
     */
    private static calculateConfidence(fileType: string, fileName: string, fileSize: number): number {
        let confidence = 0.5; // Base confidence
        
        // File extension confidence
        if (fileType === 'package' || fileType === 'script') {
            confidence += 0.3;
        }
        
        // Filename pattern confidence
        const hasDescriptivePattern = this.hasDescriptivePattern(fileName);
        if (hasDescriptivePattern) {
            confidence += 0.2;
        }
        
        // File size reasonableness
        if (this.isReasonableFileSize(fileType, fileSize)) {
            confidence += 0.1;
        }
        
        return Math.min(confidence, 1.0);
    }
    
    /**
     * Checks if filename contains any of the given patterns
     */
    private static containsAnyPattern(fileName: string, patterns: string[]): boolean {
        return patterns.some(pattern => fileName.includes(pattern));
    }
    
    /**
     * Checks if filename has descriptive patterns
     */
    private static hasDescriptivePattern(fileName: string): boolean {
        const descriptivePatterns = [
            'cas', 'hair', 'clothing', 'furniture', 'override', 'tuning',
            'mod', 'script', 'fix', 'patch', 'update', 'replacement'
        ];
        
        return this.containsAnyPattern(fileName, descriptivePatterns);
    }
    
    /**
     * Checks if file size is reasonable for the file type
     */
    private static isReasonableFileSize(fileType: string, fileSize: number): boolean {
        switch (fileType) {
            case 'script':
                return fileSize > 1024 && fileSize < 10 * 1024 * 1024; // 1KB - 10MB
            case 'package':
                return fileSize > 1024 && fileSize < 500 * 1024 * 1024; // 1KB - 500MB
            default:
                return true;
        }
    }
}