<template>
  <div class="mod-list-item">
    <div class="mod-list-item__main">
      <div class="mod-list-item__info">
        <h4 class="mod-list-item__name">{{ modData.fileName }}</h4>
        <div class="mod-list-item__meta">
          <span v-if="modData.author" class="author">{{ modData.author }}</span>
          <span class="file-size">{{ formatFileSize(modData.fileSize) }}</span>
          <span class="file-type" :class="`type-${modData.fileExtension.slice(1)}`">
            {{ modData.fileExtension }}
          </span>
        </div>
      </div>
      
      <div class="mod-list-item__indicators">
        <IntelligenceIndicator
          v-if="modData.hasResourceIntelligence"
          :type="modData.intelligenceType"
          :quality-score="modData.qualityScore"
          :risk-level="modData.riskLevel"
          size="sm"
        />
        <QualityBadge
          v-if="modData.qualityScore"
          :score="modData.qualityScore"
          size="sm"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import IntelligenceIndicator from './IntelligenceIndicator.vue';
import QualityBadge from './QualityBadge.vue';

defineProps<{
  modData: any;
}>();

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
</script>

<style scoped>
.mod-list-item {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  transition: all var(--duration-150) var(--ease-out);
}

.mod-list-item:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.mod-list-item__main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mod-list-item__info {
  flex: 1;
  min-width: 0;
}

.mod-list-item__name {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mod-list-item__meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.author {
  color: var(--text-accent);
  font-weight: var(--font-medium);
}

.file-size {
  font-family: var(--font-family-mono);
}

.file-type {
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
}

.file-type.type-package {
  background: var(--sims-blue-bg);
  color: var(--sims-blue);
}

.file-type.type-ts4script {
  background: var(--sims-purple-bg);
  color: var(--sims-purple);
}

.mod-list-item__indicators {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}
</style>
