/**
 * EA Manifest File Analyzer
 * 
 * Specialized component for detecting and parsing XML manifest files within 
 * .package files using S4TK resources. Supports EA's standard manifest format
 * for official mod metadata extraction.
 * 
 * Based on research of EA's Sims 4 manifest system and community practices.
 */

import { Package, StringTableResource, CombinedTuningResource, SimDataResource } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';

export interface ManifestMetadata {
    author?: string;
    version?: string;
    modName?: string;
    description?: string;
    dependencies?: string[];
    confidence: number; // 0-100 confidence score
    source?: string; // Which resource type provided the metadata
}

/**
 * Analyzes package files for EA manifest files and metadata
 */
export class ManifestAnalyzer {
    
    /**
     * Extract metadata from Sims 4 manifest XML files within packages
     *
     * @param s4tkPackage - S4TK Package instance
     * @returns Extracted metadata with confidence score
     */
    public static extractFromPackage(s4tkPackage: Package): ManifestMetadata {
        const metadata: ManifestMetadata = { confidence: 0 };

        try {
            // Try different resource types in order of reliability
            // PRIORITY 1: Llama-Logic manifest system (highest confidence)
            const extractors = [
                () => this.extractFromLlamaLogicManifest(s4tkPackage),
                () => this.extractFromStringTables(s4tkPackage),
                () => this.extractFromCombinedTuning(s4tkPackage),
                () => this.extractFromSimData(s4tkPackage),
                () => this.extractFromXMLComments(s4tkPackage)
            ];
            
            for (const extractor of extractors) {
                const result = extractor();
                if (result.confidence > metadata.confidence) {
                    Object.assign(metadata, result);
                    
                    // If we found high-confidence metadata, we can stop
                    if (result.confidence >= 80) break;
                }
            }
            
        } catch (error) {
            console.warn('Error extracting manifest metadata from package:', error);
        }
        
        return metadata;
    }

    /**
     * Extract metadata from Llama-Logic manifest system
     * Looks for SnippetTuning resources containing mod file manifests
     * This is the highest confidence source as it's a standardized format
     */
    private static extractFromLlamaLogicManifest(s4tkPackage: Package): ManifestMetadata {
        const metadata: ManifestMetadata = { confidence: 0, source: 'LlamaLogic_Manifest' };

        try {
            for (const entry of s4tkPackage.entries.values()) {
                if (entry.key.type === BinaryResourceType.SnippetTuning) {
                    try {
                        // Try to parse as XML to look for manifest structure
                        const content = entry.value.toString();

                        // Look for Llama-Logic manifest XML patterns
                        if (this.isLlamaLogicManifest(content)) {
                            const manifestData = this.parseLlamaLogicXML(content);
                            if (manifestData) {
                                Object.assign(metadata, manifestData);
                                metadata.confidence = 95; // Highest confidence for standardized format
                                return metadata;
                            }
                        }
                    } catch (error) {
                        // Skip invalid XML resources
                        continue;
                    }
                }
            }
        } catch (error) {
            console.warn('Error extracting from Llama-Logic manifest:', error);
        }

        return metadata;
    }

    /**
     * Determines if XML content is a Llama-Logic manifest
     */
    private static isLlamaLogicManifest(content: string): boolean {
        // Look for characteristic Llama-Logic manifest patterns
        const manifestIndicators = [
            'ModFileManifest',
            'TuningName',
            'TuningFullInstance',
            'Creators',
            'llamalogic'
        ];

        const lowerContent = content.toLowerCase();
        return manifestIndicators.some(indicator =>
            lowerContent.includes(indicator.toLowerCase())
        );
    }

    /**
     * Parse Llama-Logic manifest XML content
     */
    private static parseLlamaLogicXML(content: string): Partial<ManifestMetadata> | null {
        try {
            const metadata: Partial<ManifestMetadata> = {};

            // Extract Name
            const nameMatch = content.match(/<Name[^>]*>([^<]+)<\/Name>/i);
            if (nameMatch) metadata.modName = this.cleanMetadataValue(nameMatch[1]);

            // Extract Creators (can be multiple)
            const creatorsMatches = content.match(/<Creators[^>]*>([\s\S]*?)<\/Creators>/i);
            if (creatorsMatches) {
                const creatorItems = creatorsMatches[1].match(/<li[^>]*>([^<]+)<\/li>/gi);
                if (creatorItems && creatorItems.length > 0) {
                    // Take the first creator as primary author
                    const firstCreator = creatorItems[0].replace(/<[^>]+>/g, '');
                    metadata.author = this.cleanMetadataValue(firstCreator);
                }
            }

            // Extract Version
            const versionMatch = content.match(/<Version[^>]*>([^<]+)<\/Version>/i);
            if (versionMatch) metadata.version = this.cleanMetadataValue(versionMatch[1]);

            // Extract Description
            const descMatch = content.match(/<Description[^>]*>([^<]+)<\/Description>/i);
            if (descMatch) metadata.description = this.cleanMetadataValue(descMatch[1]);

            // Extract URL
            const urlMatch = content.match(/<Url[^>]*>([^<]+)<\/Url>/i);
            if (urlMatch) {
                // Store URL info but don't use as primary metadata
                metadata.description = metadata.description ?
                    `${metadata.description} (${urlMatch[1]})` :
                    `Available at: ${urlMatch[1]}`;
            }

            return Object.keys(metadata).length > 0 ? metadata : null;

        } catch (error) {
            console.warn('Error parsing Llama-Logic XML:', error);
            return null;
        }
    }

    /**
     * Extract metadata from String Table resources
     * String tables often contain mod names, descriptions, and author info
     */
    private static extractFromStringTables(s4tkPackage: Package): ManifestMetadata {
        const metadata: ManifestMetadata = { confidence: 0, source: 'StringTable' };
        
        try {
            for (const entry of s4tkPackage.entries.values()) {
                if (entry.key.type === BinaryResourceType.StringTable) {
                    const stbl = StringTableResource.from(entry.value);
                    
                    // Look for common metadata string patterns
                    for (const [key, value] of stbl.entries()) {
                        const lowerValue = value.toLowerCase();
                        const keyStr = key.toString();
                        
                        // Author patterns in string values
                        if (lowerValue.includes('created by') || lowerValue.includes('author') || 
                            lowerValue.includes('made by') || keyStr.toLowerCase().includes('author')) {
                            const authorMatch = value.match(/(?:created by|author|made by):?\s*(.+?)(?:\n|$|\.)/i);
                            if (authorMatch && !metadata.author) {
                                metadata.author = this.cleanMetadataValue(authorMatch[1]);
                                metadata.confidence = Math.max(metadata.confidence, 70);
                            }
                        }
                        
                        // Version patterns
                        if (lowerValue.includes('version') || keyStr.toLowerCase().includes('version')) {
                            const versionMatch = value.match(/version:?\s*(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/i);
                            if (versionMatch && !metadata.version) {
                                metadata.version = versionMatch[1];
                                metadata.confidence = Math.max(metadata.confidence, 70);
                            }
                        }
                        
                        // Mod name patterns (look for title-like strings)
                        if (keyStr.toLowerCase().includes('title') || keyStr.toLowerCase().includes('name') ||
                            (value.length > 5 && value.length < 100 && /^[A-Z]/.test(value) && !lowerValue.includes('the sims'))) {
                            if (!metadata.modName && this.isLikelyModName(value)) {
                                metadata.modName = this.cleanMetadataValue(value);
                                metadata.confidence = Math.max(metadata.confidence, 60);
                            }
                        }
                        
                        // Description patterns (longer descriptive text)
                        if (value.length > 20 && value.length < 500 && !metadata.description) {
                            if (this.isLikelyDescription(value)) {
                                metadata.description = this.cleanMetadataValue(value);
                                metadata.confidence = Math.max(metadata.confidence, 50);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('Error extracting from String Tables:', error);
        }
        
        return metadata;
    }
    
    /**
     * Extract metadata from Combined Tuning XML resources
     * XML comments and attributes often contain metadata
     */
    private static extractFromCombinedTuning(s4tkPackage: Package): ManifestMetadata {
        const metadata: ManifestMetadata = { confidence: 0, source: 'CombinedTuning' };
        
        try {
            for (const entry of s4tkPackage.entries.values()) {
                if (entry.key.type === BinaryResourceType.CombinedTuning) {
                    const tuning = CombinedTuningResource.from(entry.value);
                    const xmlContent = tuning.dom.toString();
                    
                    // Look for XML comments with metadata
                    const commentMatches = xmlContent.match(/<!--[\s\S]*?-->/g);
                    if (commentMatches) {
                        for (const comment of commentMatches) {
                            // Author in comments
                            const authorMatch = comment.match(/(?:author|created by|made by):?\s*(.+?)(?:\n|-->)/i);
                            if (authorMatch && !metadata.author) {
                                metadata.author = this.cleanMetadataValue(authorMatch[1]);
                                metadata.confidence = Math.max(metadata.confidence, 80);
                            }
                            
                            // Version in comments
                            const versionMatch = comment.match(/version:?\s*(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/i);
                            if (versionMatch && !metadata.version) {
                                metadata.version = versionMatch[1];
                                metadata.confidence = Math.max(metadata.confidence, 80);
                            }
                            
                            // Description in comments
                            const descMatch = comment.match(/(?:description|desc):?\s*(.+?)(?:\n|-->)/i);
                            if (descMatch && !metadata.description) {
                                metadata.description = this.cleanMetadataValue(descMatch[1]);
                                metadata.confidence = Math.max(metadata.confidence, 70);
                            }
                        }
                    }
                    
                    // Look for custom attributes or elements
                    const root = tuning.dom.root;
                    if (root) {
                        // Check for custom metadata attributes
                        if (root.attributes.author && !metadata.author) {
                            metadata.author = root.attributes.author;
                            metadata.confidence = Math.max(metadata.confidence, 90);
                        }
                        if (root.attributes.version && !metadata.version) {
                            metadata.version = root.attributes.version;
                            metadata.confidence = Math.max(metadata.confidence, 90);
                        }
                        if (root.attributes.mod_name && !metadata.modName) {
                            metadata.modName = root.attributes.mod_name;
                            metadata.confidence = Math.max(metadata.confidence, 90);
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('Error extracting from Combined Tuning:', error);
        }
        
        return metadata;
    }
    
    /**
     * Extract metadata from SimData resources
     * SimData can contain structured metadata
     */
    private static extractFromSimData(s4tkPackage: Package): ManifestMetadata {
        const metadata: ManifestMetadata = { confidence: 0, source: 'SimData' };
        
        try {
            for (const entry of s4tkPackage.entries.values()) {
                if (entry.key.type === BinaryResourceType.SimData) {
                    const simData = SimDataResource.from(entry.value);
                    
                    // Check instance properties for metadata
                    const instance = simData.instance;
                    if (instance && instance.row) {
                        // Look for common metadata property names
                        for (const [key, cell] of Object.entries(instance.row)) {
                            const keyLower = key.toLowerCase();
                            const value = cell.value?.toString();
                            
                            if (!value) continue;
                            
                            if ((keyLower.includes('author') || keyLower.includes('creator')) && !metadata.author) {
                                metadata.author = this.cleanMetadataValue(value);
                                metadata.confidence = Math.max(metadata.confidence, 75);
                            }
                            
                            if (keyLower.includes('version') && !metadata.version) {
                                const versionMatch = value.match(/(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/);
                                if (versionMatch) {
                                    metadata.version = versionMatch[1];
                                    metadata.confidence = Math.max(metadata.confidence, 75);
                                }
                            }
                            
                            if ((keyLower.includes('mod_name') || keyLower.includes('title')) && !metadata.modName) {
                                metadata.modName = this.cleanMetadataValue(value);
                                metadata.confidence = Math.max(metadata.confidence, 75);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('Error extracting from SimData:', error);
        }
        
        return metadata;
    }
    
    /**
     * Extract metadata from XML comments across all XML resources
     * Fallback method for any XML content with comments
     */
    private static extractFromXMLComments(s4tkPackage: Package): ManifestMetadata {
        const metadata: ManifestMetadata = { confidence: 0, source: 'XMLComments' };
        
        try {
            // This is a broader search across all resources that might contain XML
            for (const entry of s4tkPackage.entries.values()) {
                try {
                    // Try to convert resource to string and look for XML patterns
                    const content = entry.value.toString();
                    
                    if (content.includes('<!--') && content.includes('-->')) {
                        // Found XML comments, extract metadata
                        const commentMatches = content.match(/<!--[\s\S]*?-->/g);
                        if (commentMatches) {
                            for (const comment of commentMatches) {
                                // Look for manifest-like patterns
                                if (comment.toLowerCase().includes('manifest') || 
                                    comment.toLowerCase().includes('metadata')) {
                                    
                                    const authorMatch = comment.match(/author[:\s]*([^<>\n]+)/i);
                                    if (authorMatch && !metadata.author) {
                                        metadata.author = this.cleanMetadataValue(authorMatch[1]);
                                        metadata.confidence = Math.max(metadata.confidence, 60);
                                    }
                                    
                                    const versionMatch = comment.match(/version[:\s]*(\d+\.\d+(?:\.\d+)?(?:[a-z]?)?)/i);
                                    if (versionMatch && !metadata.version) {
                                        metadata.version = versionMatch[1];
                                        metadata.confidence = Math.max(metadata.confidence, 60);
                                    }
                                }
                            }
                        }
                    }
                } catch {
                    // Skip resources that can't be converted to string
                    continue;
                }
            }
        } catch (error) {
            console.warn('Error extracting from XML comments:', error);
        }
        
        return metadata;
    }
    
    /**
     * Cleans and validates metadata values
     */
    private static cleanMetadataValue(value: string): string {
        return value
            .trim()
            .replace(/['"]/g, '') // Remove quotes
            .replace(/\s+/g, ' ') // Normalize whitespace
            .replace(/^[-\s]+|[-\s]+$/g, '') // Remove leading/trailing dashes and spaces
            .substring(0, 100); // Limit length
    }
    
    /**
     * Determines if a string looks like a mod name
     */
    private static isLikelyModName(value: string): boolean {
        // Mod names typically:
        // - Are 5-80 characters long
        // - Start with a capital letter or number
        // - Don't contain common UI text
        // - Aren't generic game terms
        
        if (value.length < 5 || value.length > 80) return false;
        
        const lowerValue = value.toLowerCase();
        const genericTerms = [
            'the sims', 'sims 4', 'ea games', 'maxis', 'loading', 'error', 'warning',
            'click here', 'continue', 'cancel', 'ok', 'yes', 'no', 'true', 'false'
        ];
        
        if (genericTerms.some(term => lowerValue.includes(term))) return false;
        
        // Should start with letter or number
        if (!/^[a-zA-Z0-9]/.test(value)) return false;
        
        return true;
    }
    
    /**
     * Determines if a string looks like a description
     */
    private static isLikelyDescription(value: string): boolean {
        // Descriptions typically:
        // - Are 20-500 characters long
        // - Contain multiple words
        // - Have proper sentence structure
        // - Aren't just file paths or technical strings
        
        if (value.length < 20 || value.length > 500) return false;
        
        const wordCount = value.split(/\s+/).length;
        if (wordCount < 4) return false;
        
        // Shouldn't be mostly technical characters
        const technicalChars = /[{}[\]()=<>|\\\/]/g;
        const technicalCount = (value.match(technicalChars) || []).length;
        if (technicalCount > value.length * 0.1) return false;
        
        return true;
    }
}
