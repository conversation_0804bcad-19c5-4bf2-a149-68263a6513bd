/**
 * CAS Body Location Mapper
 * 
 * Specialized component for mapping body type values to body locations.
 * Handles the conversion from numeric body types to structured location categories.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring.
 */

import { BodyLocation, type CASPartInfo } from './types';

/**
 * Handles mapping of body type values to body location categories
 */
export class BodyLocationMapper {
    
    /**
     * Maps body type values to body location categories
     * 
     * @param bodyTypeValue - The numeric body type value from SimData
     * @param casPartInfo - CAS part info object to populate
     */
    public static mapBodyTypeToLocation(bodyTypeValue: any, casPartInfo: CASPartInfo): void {
        const numericValue = this.parseBodyTypeValue(bodyTypeValue);
        
        if (numericValue === null) {
            console.warn('Invalid body type value:', bodyTypeValue);
            return;
        }
        
        this.mapBodyTypeToLocations(numericValue, casPartInfo);
    }
    
    /**
     * Parses various input formats to numeric body type value
     * 
     * @param value - Input value in various formats
     * @returns Numeric value or null if parsing fails
     */
    private static parseBodyTypeValue(value: any): number | null {
        if (typeof value === 'number') {
            return value;
        }
        
        if (typeof value === 'string') {
            const parsed = parseInt(value, 10);
            return isNaN(parsed) ? null : parsed;
        }
        
        // Handle object with value property
        if (value && typeof value.value !== 'undefined') {
            return this.parseBodyTypeValue(value.value);
        }
        
        return null;
    }
    
    /**
     * Maps numeric body type to body location categories
     * 
     * @param bodyType - Numeric body type value
     * @param casPartInfo - CAS part info to populate
     */
    private static mapBodyTypeToLocations(bodyType: number, casPartInfo: CASPartInfo): void {
        // Clear existing body locations to avoid duplicates
        casPartInfo.bodyLocation = [];
        
        // Map based on common body type values
        // Note: These mappings may need adjustment based on actual game data
        switch (bodyType) {
            case 1: // Head/Face
                casPartInfo.bodyLocation.push(BodyLocation.HEAD, BodyLocation.FACE);
                break;
                
            case 2: // Hair
                casPartInfo.bodyLocation.push(BodyLocation.HAIR);
                break;
                
            case 3: // Upper body
                casPartInfo.bodyLocation.push(BodyLocation.UPPER_BODY);
                break;
                
            case 4: // Lower body
                casPartInfo.bodyLocation.push(BodyLocation.LOWER_BODY);
                break;
                
            case 5: // Full body
                casPartInfo.bodyLocation.push(BodyLocation.FULL_BODY);
                break;
                
            case 6: // Feet
                casPartInfo.bodyLocation.push(BodyLocation.FEET);
                break;
                
            case 7: // Hands
                casPartInfo.bodyLocation.push(BodyLocation.HANDS);
                break;
                
            case 8: // Neck
                casPartInfo.bodyLocation.push(BodyLocation.NECK);
                break;
                
            case 9: // Ears
                casPartInfo.bodyLocation.push(BodyLocation.EARS);
                break;
                
            case 10: // Wrists
                casPartInfo.bodyLocation.push(BodyLocation.WRISTS);
                break;
                
            case 11: // Rings/Fingers
                casPartInfo.bodyLocation.push(BodyLocation.RINGS);
                break;
                
            default:
                // For unknown body types, try to infer from existing category
                this.inferBodyLocationFromCategory(casPartInfo);
                break;
        }
    }
    
    /**
     * Infers body location from existing CAS category when body type is unknown
     * 
     * @param casPartInfo - CAS part info to update
     */
    private static inferBodyLocationFromCategory(casPartInfo: CASPartInfo): void {
        // Only infer if no body locations are set
        if (casPartInfo.bodyLocation.length > 0) {
            return;
        }
        
        // Infer based on category and subcategory
        if (casPartInfo.isHair) {
            casPartInfo.bodyLocation.push(BodyLocation.HAIR);
        } else if (casPartInfo.isMakeup) {
            casPartInfo.bodyLocation.push(BodyLocation.FACE);
        } else if (casPartInfo.isAccessory) {
            // Default accessory location - could be refined further
            casPartInfo.bodyLocation.push(BodyLocation.NECK);
        } else if (casPartInfo.isClothing) {
            // Infer from subcategory
            switch (casPartInfo.subcategory) {
                case 'tops':
                    casPartInfo.bodyLocation.push(BodyLocation.UPPER_BODY);
                    break;
                case 'bottoms':
                    casPartInfo.bodyLocation.push(BodyLocation.LOWER_BODY);
                    break;
                case 'shoes':
                    casPartInfo.bodyLocation.push(BodyLocation.FEET);
                    break;
                case 'full_body':
                    casPartInfo.bodyLocation.push(BodyLocation.FULL_BODY);
                    break;
                default:
                    casPartInfo.bodyLocation.push(BodyLocation.FULL_BODY);
                    break;
            }
        }
    }
    
    /**
     * Gets human-readable description for body location
     * 
     * @param bodyLocation - Body location enum value
     * @returns Human-readable description
     */
    public static getBodyLocationDescription(bodyLocation: BodyLocation): string {
        const descriptions: Record<BodyLocation, string> = {
            [BodyLocation.HEAD]: 'Head',
            [BodyLocation.HAIR]: 'Hair',
            [BodyLocation.FACE]: 'Face',
            [BodyLocation.UPPER_BODY]: 'Upper Body',
            [BodyLocation.LOWER_BODY]: 'Lower Body',
            [BodyLocation.FULL_BODY]: 'Full Body',
            [BodyLocation.FEET]: 'Feet',
            [BodyLocation.HANDS]: 'Hands',
            [BodyLocation.NECK]: 'Neck',
            [BodyLocation.EARS]: 'Ears',
            [BodyLocation.WRISTS]: 'Wrists',
            [BodyLocation.RINGS]: 'Rings/Fingers'
        };
        
        return descriptions[bodyLocation] || 'Unknown Location';
    }
    
    /**
     * Validates body location assignments
     * 
     * @param bodyLocations - Array of body locations to validate
     * @returns true if body locations are valid
     */
    public static validateBodyLocations(bodyLocations: BodyLocation[]): boolean {
        return bodyLocations.every(location => 
            Object.values(BodyLocation).includes(location)
        );
    }
}
