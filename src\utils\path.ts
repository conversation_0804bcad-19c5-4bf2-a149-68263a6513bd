import * as path from 'path';

/**
 * Sanitizes a file path to prevent directory traversal attacks.
 *
 * @param basePath - The base directory that the path should be within.
 * @param userPath - The path provided by the user.
 * @returns A sanitized, absolute path.
 * @throws An error if the path is invalid or attempts to traverse directories.
 */
export function sanitizePath(basePath: string, userPath: string): string {
    const absoluteBasePath = path.resolve(basePath);
    const absoluteUserPath = path.resolve(absoluteBasePath, userPath);

    if (!absoluteUserPath.startsWith(absoluteBasePath)) {
        throw new Error('Invalid path: Directory traversal is not allowed.');
    }

    return absoluteUserPath;
}