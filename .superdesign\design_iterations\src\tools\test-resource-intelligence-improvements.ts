#!/usr/bin/env node

/**
 * Test Resource Intelligence Improvements
 * 
 * Tests the enhanced Resource Intelligence system to validate improvements
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface ImprovementTestResult {
    fileName: string;
    fileExtension: string;
    resourceCount: number | string;
    
    // Before/After comparison
    hadResourceIntelligence: boolean;
    hasResourceIntelligenceNow: boolean;
    improvement: boolean;
    
    // Intelligence details
    intelligenceType: string;
    qualityScore?: number;
    
    processingTime: number;
}

class ResourceIntelligenceImprovementTester {
    private analysisService: PackageAnalysisService;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
    }
    
    /**
     * Tests Resource Intelligence improvements
     */
    public async testImprovements(): Promise<void> {
        console.log('🔧 TESTING RESOURCE INTELLIGENCE IMPROVEMENTS');
        console.log('Validating enhanced coverage and Script Intelligence\n');
        
        const modsDir = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
        
        if (!fs.existsSync(modsDir)) {
            console.error('❌ Mods directory not found:', modsDir);
            return;
        }
        
        // Find all mod files
        const modFiles = this.findModFiles(modsDir);
        console.log(`📦 Found ${modFiles.length} mod files\n`);
        
        // Test a representative sample
        const sampleSize = 100;
        const sampleFiles = this.selectRepresentativeSample(modFiles, sampleSize);
        
        console.log(`🧪 Testing ${sampleFiles.length} representative files:\n`);
        
        const results: ImprovementTestResult[] = [];
        
        for (let i = 0; i < sampleFiles.length; i++) {
            const filePath = sampleFiles[i];
            const fileName = path.basename(filePath);
            
            if (i % 20 === 0) {
                console.log(`Progress: ${i + 1}/${sampleFiles.length} files tested`);
            }
            
            const result = await this.testFile(filePath);
            results.push(result);
            
            // Show interesting results immediately
            if (result.improvement || result.fileExtension === '.ts4script') {
                const status = result.improvement ? '✅ IMPROVED' : '📊 ANALYZED';
                const intelligence = result.hasResourceIntelligenceNow ? '🧠' : '❌';
                console.log(`   ${status} ${fileName} ${intelligence} ${result.intelligenceType}`);
            }
        }
        
        console.log(`\n📊 Testing complete!\n`);
        this.generateImprovementReport(results);
    }
    
    /**
     * Recursively finds all mod files
     */
    private findModFiles(dir: string): string[] {
        const modFiles: string[] = [];
        
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    modFiles.push(...this.findModFiles(fullPath));
                } else if (stat.isFile()) {
                    if (item.endsWith('.package') || item.endsWith('.ts4script')) {
                        modFiles.push(fullPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not read directory ${dir}:`, error);
        }
        
        return modFiles;
    }
    
    /**
     * Select a representative sample of files for testing
     */
    private selectRepresentativeSample(modFiles: string[], sampleSize: number): string[] {
        const scriptFiles = modFiles.filter(f => f.endsWith('.ts4script'));
        const packageFiles = modFiles.filter(f => f.endsWith('.package'));
        
        // Ensure we get both types
        const scriptSample = scriptFiles.slice(0, Math.min(20, scriptFiles.length));
        const packageSample = packageFiles.slice(0, sampleSize - scriptSample.length);
        
        return [...scriptSample, ...packageSample];
    }
    
    /**
     * Tests a single file for Resource Intelligence improvements
     */
    private async testFile(filePath: string): Promise<ImprovementTestResult> {
        const fileName = path.basename(filePath);
        const fileExtension = path.extname(fileName);
        const startTime = Date.now();
        
        try {
            const buffer = fs.readFileSync(filePath);
            const analysis = await this.analysisService.detailedAnalyzeAsync(buffer, filePath);
            
            const hasResourceIntelligenceNow = !!analysis.intelligence?.resourceIntelligence;
            const resourceCount = analysis.resourceCount || 'N/A';
            const qualityScore = analysis.intelligence?.qualityAssessment?.overallScore;
            
            // Determine intelligence type
            let intelligenceType = 'None';
            if (fileExtension === '.ts4script' && hasResourceIntelligenceNow) {
                intelligenceType = 'Script Intelligence';
            } else if (fileExtension === '.package' && hasResourceIntelligenceNow) {
                intelligenceType = 'Resource Intelligence';
            } else if (analysis.intelligence) {
                intelligenceType = 'Basic Intelligence';
            }
            
            // For this test, we assume improvement if we now have intelligence
            // (since we can't compare to "before" state)
            const improvement = hasResourceIntelligenceNow;
            
            return {
                fileName,
                fileExtension,
                resourceCount,
                hadResourceIntelligence: false, // We can't know the before state
                hasResourceIntelligenceNow,
                improvement,
                intelligenceType,
                qualityScore,
                processingTime: Date.now() - startTime
            };
            
        } catch (error) {
            return {
                fileName,
                fileExtension,
                resourceCount: 'Error',
                hadResourceIntelligence: false,
                hasResourceIntelligenceNow: false,
                improvement: false,
                intelligenceType: 'Error',
                processingTime: Date.now() - startTime
            };
        }
    }
    
    /**
     * Generates improvement report
     */
    private generateImprovementReport(results: ImprovementTestResult[]): void {
        console.log('🚀 RESOURCE INTELLIGENCE IMPROVEMENT REPORT');
        console.log('============================================\n');
        
        const totalFiles = results.length;
        const scriptFiles = results.filter(r => r.fileExtension === '.ts4script');
        const packageFiles = results.filter(r => r.fileExtension === '.package');
        
        console.log('📊 SAMPLE BREAKDOWN:');
        console.log(`   Total Files Tested: ${totalFiles}`);
        console.log(`   Script Files (.ts4script): ${scriptFiles.length}`);
        console.log(`   Package Files (.package): ${packageFiles.length}\n`);
        
        // Overall Resource Intelligence coverage
        const filesWithResourceIntelligence = results.filter(r => r.hasResourceIntelligenceNow).length;
        const resourceIntelligenceCoverage = Math.round((filesWithResourceIntelligence / totalFiles) * 100);
        
        console.log('🧠 RESOURCE INTELLIGENCE COVERAGE:');
        console.log(`   Files with Resource Intelligence: ${filesWithResourceIntelligence}/${totalFiles} (${resourceIntelligenceCoverage}%)`);
        
        // Script Intelligence analysis
        const scriptsWithIntelligence = scriptFiles.filter(r => r.hasResourceIntelligenceNow).length;
        const scriptIntelligenceCoverage = scriptFiles.length > 0 ? 
            Math.round((scriptsWithIntelligence / scriptFiles.length) * 100) : 0;
        
        console.log(`   Script Intelligence Coverage: ${scriptsWithIntelligence}/${scriptFiles.length} (${scriptIntelligenceCoverage}%)`);
        
        // Package Intelligence analysis
        const packagesWithIntelligence = packageFiles.filter(r => r.hasResourceIntelligenceNow).length;
        const packageIntelligenceCoverage = packageFiles.length > 0 ? 
            Math.round((packagesWithIntelligence / packageFiles.length) * 100) : 0;
        
        console.log(`   Package Intelligence Coverage: ${packagesWithIntelligence}/${packageFiles.length} (${packageIntelligenceCoverage}%)\n`);
        
        // Intelligence type breakdown
        console.log('🔍 INTELLIGENCE TYPE BREAKDOWN:');
        const intelligenceTypes = new Map<string, number>();
        results.forEach(result => {
            const type = result.intelligenceType;
            intelligenceTypes.set(type, (intelligenceTypes.get(type) || 0) + 1);
        });
        
        const sortedTypes = Array.from(intelligenceTypes.entries())
            .sort((a, b) => b[1] - a[1]);
        
        sortedTypes.forEach(([type, count]) => {
            const percentage = Math.round((count / totalFiles) * 100);
            console.log(`   ${type}: ${count} files (${percentage}%)`);
        });
        
        // Quality score analysis
        const filesWithQuality = results.filter(r => r.qualityScore !== undefined);
        if (filesWithQuality.length > 0) {
            const avgQuality = Math.round(
                filesWithQuality.reduce((sum, r) => sum + (r.qualityScore || 0), 0) / filesWithQuality.length
            );
            console.log(`\n⭐ QUALITY ASSESSMENT:`);
            console.log(`   Files with Quality Scores: ${filesWithQuality.length}/${totalFiles}`);
            console.log(`   Average Quality Score: ${avgQuality}/100`);
        }
        
        // Performance analysis
        const avgProcessingTime = Math.round(
            results.reduce((sum, r) => sum + r.processingTime, 0) / totalFiles
        );
        console.log(`\n⏱️ PERFORMANCE:`);
        console.log(`   Average Processing Time: ${avgProcessingTime}ms per file`);
        
        // Success assessment
        console.log(`\n🎯 IMPROVEMENT ASSESSMENT:`);
        
        const targetResourceIntelligence = 85; // Target 85%+ coverage
        const targetScriptIntelligence = 90;   // Target 90%+ script coverage
        
        const resourceIntelligenceSuccess = resourceIntelligenceCoverage >= targetResourceIntelligence;
        const scriptIntelligenceSuccess = scriptIntelligenceCoverage >= targetScriptIntelligence;
        
        console.log(`   Resource Intelligence Target (${targetResourceIntelligence}%): ${resourceIntelligenceSuccess ? '✅ ACHIEVED' : '❌ NEEDS WORK'} (${resourceIntelligenceCoverage}%)`);
        console.log(`   Script Intelligence Target (${targetScriptIntelligence}%): ${scriptIntelligenceSuccess ? '✅ ACHIEVED' : '❌ NEEDS WORK'} (${scriptIntelligenceCoverage}%)`);
        
        if (resourceIntelligenceSuccess && scriptIntelligenceSuccess) {
            console.log(`\n🏆 IMPROVEMENT STATUS: EXCELLENT SUCCESS`);
            console.log(`   ✅ Resource Intelligence coverage significantly improved`);
            console.log(`   ✅ Script Intelligence successfully implemented`);
            console.log(`   ✅ Ready for production deployment`);
        } else {
            console.log(`\n📈 IMPROVEMENT STATUS: GOOD PROGRESS`);
            if (!resourceIntelligenceSuccess) {
                console.log(`   📋 Continue optimizing Resource Intelligence thresholds`);
            }
            if (!scriptIntelligenceSuccess) {
                console.log(`   📋 Enhance Script Intelligence detection`);
            }
        }
        
        // Projected full collection impact
        const projectedImprovement = Math.round((resourceIntelligenceCoverage - 77) * 100) / 100;
        console.log(`\n📊 PROJECTED FULL COLLECTION IMPACT:`);
        console.log(`   Current Coverage (from report): 77%`);
        console.log(`   Sample Coverage: ${resourceIntelligenceCoverage}%`);
        console.log(`   Projected Improvement: +${projectedImprovement}% points`);
        console.log(`   Projected New Coverage: ${Math.min(77 + projectedImprovement, 95)}%`);
    }
}

// Run the improvement test
async function main() {
    const tester = new ResourceIntelligenceImprovementTester();
    await tester.testImprovements();
}

if (require.main === module) {
    main().catch(console.error);
}
