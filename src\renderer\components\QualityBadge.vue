<template>
  <div class="quality-badge" :class="badgeClass">
    <StarIcon class="quality-badge__icon" />
    <span class="quality-badge__score">{{ score }}</span>
    <span class="quality-badge__label">{{ qualityLabel }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { StarIcon } from '@heroicons/vue/24/solid';

const props = defineProps<{
  score: number;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}>();

const qualityLevel = computed(() => {
  if (props.score >= 90) return 'excellent';
  if (props.score >= 80) return 'very-good';
  if (props.score >= 70) return 'good';
  if (props.score >= 60) return 'fair';
  if (props.score >= 40) return 'poor';
  return 'very-poor';
});

const qualityLabel = computed(() => {
  const labels: Record<string, string> = {
    'excellent': 'Excellent',
    'very-good': 'Very Good',
    'good': 'Good',
    'fair': 'Fair',
    'poor': 'Poor',
    'very-poor': 'Very Poor'
  };
  return labels[qualityLevel.value];
});

const badgeClass = computed(() => {
  const classes = [
    'quality-badge',
    `quality-badge--${qualityLevel.value}`
  ];
  
  if (props.size) {
    classes.push(`quality-badge--${props.size}`);
  }
  
  return classes;
});
</script>

<style scoped>
.quality-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  transition: all var(--duration-150) var(--ease-out);
}

.quality-badge--sm {
  padding: 2px var(--space-1);
  font-size: 10px;
  gap: 2px;
}

.quality-badge--lg {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  gap: var(--space-2);
}

.quality-badge__icon {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.quality-badge--sm .quality-badge__icon {
  width: 10px;
  height: 10px;
}

.quality-badge--lg .quality-badge__icon {
  width: 16px;
  height: 16px;
}

.quality-badge__score {
  font-family: var(--font-family-mono);
  font-weight: var(--font-bold);
}

.quality-badge__label {
  font-weight: var(--font-medium);
}

/* Quality levels */
.quality-badge--excellent {
  background: var(--success-bg);
  color: var(--success);
  border: 1px solid var(--success-border);
}

.quality-badge--very-good {
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green-dark);
  border: 1px solid var(--plumbob-green-light);
}

.quality-badge--good {
  background: var(--info-bg);
  color: var(--info);
  border: 1px solid var(--info-border);
}

.quality-badge--fair {
  background: var(--sims-orange-bg);
  color: var(--sims-orange);
  border: 1px solid var(--sims-orange-soft);
}

.quality-badge--poor {
  background: var(--warning-bg);
  color: var(--warning);
  border: 1px solid var(--warning-border);
}

.quality-badge--very-poor {
  background: var(--error-bg);
  color: var(--error);
  border: 1px solid var(--error-border);
}

.quality-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}
</style>
