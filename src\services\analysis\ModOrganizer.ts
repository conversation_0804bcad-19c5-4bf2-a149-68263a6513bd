import type { AnalyzedPackage } from '../../types/analysis';
import { ModCategory } from '../../types/analysis';
import { 
    OrganizationPreferences, 
    OrganizationPlan, 
    CategoryData,
    DEFAULT_PREFERENCES 
} from './organization/types';
import { PathGenerator } from './organization/PathGenerator';
import { ConflictDetector } from './organization/ConflictDetector';
import { AuthorExtractor } from './organization/AuthorExtractor';

/**
 * ModOrganizer provides automatic mod organization suggestions and folder structure generation
 * 
 * Refactored to use specialized components for better maintainability:
 * - PathGenerator: Handles path generation logic
 * - ConflictDetector: Detects organization conflicts
 * - AuthorExtractor: Extracts author information
 */
export class ModOrganizer {
    private preferences: OrganizationPreferences;

    constructor(preferences: Partial<OrganizationPreferences> = {}) {
        this.preferences = { ...DEFAULT_PREFERENCES, ...preferences };
    }

    /**
     * Generates a suggested folder path for a mod based on its analysis
     */
    public generateSuggestedPath(analysis: AnalyzedPackage): string {
        return PathGenerator.generateSuggestedPath(analysis, this.preferences);
    }

    /**
     * Generates organization suggestions for a batch of mods
     */
    public generateBatchOrganization(analyses: AnalyzedPackage[]): OrganizationPlan {
        const plan: OrganizationPlan = {
            totalFiles: analyses.length,
            categories: new Map(),
            conflicts: [],
            suggestions: []
        };

        // Group by suggested paths
        const pathGroups = new Map<string, AnalyzedPackage[]>();
        
        analyses.forEach(analysis => {
            const suggestedPath = this.generateSuggestedPath(analysis);
            if (!pathGroups.has(suggestedPath)) {
                pathGroups.set(suggestedPath, []);
            }
            pathGroups.get(suggestedPath)!.push(analysis);
        });

        // Build category statistics
        analyses.forEach(analysis => {
            const category = analysis.category;
            if (!plan.categories.has(category)) {
                plan.categories.set(category, {
                    count: 0,
                    files: [],
                    suggestedFolder: this.preferences.customFolderNames[category] || 
                                   PathGenerator.getCategoryDisplayName(category)
                });
            }
            
            const categoryData = plan.categories.get(category)!;
            categoryData.count++;
            categoryData.files.push(analysis);
        });

        // Generate organization suggestions
        pathGroups.forEach((files, suggestedPath) => {
            plan.suggestions.push({
                targetPath: suggestedPath,
                files: files.map(f => f.filePath),
                fileCount: files.length,
                category: files[0].category,
                description: PathGenerator.generatePathDescription(suggestedPath, files)
            });
        });

        // Detect potential organization conflicts using specialized detector
        ConflictDetector.detectOrganizationConflicts(plan, analyses, this.preferences);

        return plan;
    }

    /**
     * Gets author information for a mod (delegates to AuthorExtractor)
     */
    public getAuthor(analysis: AnalyzedPackage): string | null {
        return AuthorExtractor.extractAuthor(analysis);
    }

    /**
     * Gets multiple authors for collaborative mods
     */
    public getMultipleAuthors(analysis: AnalyzedPackage): string[] {
        return AuthorExtractor.extractMultipleAuthors(analysis);
    }

    /**
     * Validates an organization plan for common issues
     */
    public validateOrganizationPlan(plan: OrganizationPlan): {
        isValid: boolean;
        issues: string[];
        warnings: string[];
    } {
        return ConflictDetector.validateOrganizationPlan(plan);
    }

    /**
     * Updates organization preferences
     */
    public updatePreferences(newPreferences: Partial<OrganizationPreferences>): void {
        this.preferences = { ...this.preferences, ...newPreferences };
    }

    /**
     * Gets current organization preferences
     */
    public getPreferences(): OrganizationPreferences {
        return { ...this.preferences };
    }
}

// Re-export types for convenience
export type { 
    OrganizationPreferences, 
    OrganizationPlan, 
    CategoryData,
    OrganizationSuggestion,
    OrganizationConflict
} from './organization/types';