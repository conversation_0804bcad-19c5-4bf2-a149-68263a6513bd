<template>
  <div class="hair-classification" v-if="hairDetails">
    <div class="hair-classification__header">
      <div class="hair-classification__icon">
        ✂️
      </div>
      <h4 class="hair-classification__title">Hair Style Details</h4>
      <div class="hair-classification__confidence">
        <span class="confidence-badge" :class="getConfidenceClass()">
          {{ Math.round(hairDetails.confidence * 100) }}% confidence
        </span>
      </div>
    </div>

    <div class="hair-classification__content">
      <!-- Hair Length -->
      <div class="hair-attribute" v-if="hairDetails.length !== 'unknown'">
        <div class="hair-attribute__label">
          <span class="hair-attribute__icon">📏</span>
          Length
        </div>
        <div class="hair-attribute__value">
          <span class="hair-length-badge" :class="`hair-length--${hairDetails.length}`">
            {{ formatHairLength(hairDetails.length) }}
          </span>
        </div>
      </div>

      <!-- Hair Style -->
      <div class="hair-attribute" v-if="hairDetails.style && hairDetails.style.length > 0 && !hairDetails.style.includes('unknown')">
        <div class="hair-attribute__label">
          <span class="hair-attribute__icon">💇</span>
          Style
        </div>
        <div class="hair-attribute__value">
          <div class="hair-styles">
            <span 
              v-for="style in hairDetails.style" 
              :key="style"
              class="hair-style-badge"
              :class="`hair-style--${style}`"
            >
              {{ formatHairStyle(style) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Hair Texture -->
      <div class="hair-attribute" v-if="hairDetails.texture !== 'unknown'">
        <div class="hair-attribute__label">
          <span class="hair-attribute__icon">🌊</span>
          Texture
        </div>
        <div class="hair-attribute__value">
          <span class="hair-texture-badge" :class="`hair-texture--${hairDetails.texture}`">
            {{ formatHairTexture(hairDetails.texture) }}
          </span>
        </div>
      </div>

      <!-- Accessories -->
      <div class="hair-attribute" v-if="hairDetails.hasAccessories">
        <div class="hair-attribute__label">
          <span class="hair-attribute__icon">✨</span>
          Features
        </div>
        <div class="hair-attribute__value">
          <span class="hair-accessory-badge">
            With Accessories
          </span>
        </div>
      </div>

      <!-- Detection Method -->
      <div class="hair-classification__meta">
        <span class="detection-method">
          <span class="detection-method__icon">🔍</span>
          Detected via {{ formatDetectionMethod(hairDetails.detectionMethod) }}
        </span>
        <div class="hair-keywords" v-if="hairDetails.keywords && hairDetails.keywords.length > 0">
          <span class="keywords-label">Keywords:</span>
          <span class="keyword-tag" v-for="keyword in hairDetails.keywords.slice(0, 3)" :key="keyword">
            {{ keyword }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface HairClassificationInfo {
  length: string;
  style: string[];
  texture: string;
  hasAccessories: boolean;
  confidence: number;
  detectionMethod: string;
  keywords: string[];
}

interface Props {
  hairDetails: HairClassificationInfo | null;
}

const props = defineProps<Props>();

const getConfidenceClass = () => {
  if (!props.hairDetails) return 'confidence--low';
  const confidence = props.hairDetails.confidence;
  if (confidence >= 0.8) return 'confidence--high';
  if (confidence >= 0.6) return 'confidence--medium';
  return 'confidence--low';
};

const formatHairLength = (length: string): string => {
  return length.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatHairStyle = (style: string): string => {
  return style.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatHairTexture = (texture: string): string => {
  return texture.replace(/\b\w/g, l => l.toUpperCase());
};

const formatDetectionMethod = (method: string): string => {
  switch (method) {
    case 'filename': return 'filename analysis';
    case 'resource_analysis': return 'resource analysis';
    case 'hybrid': return 'hybrid detection';
    default: return method;
  }
};
</script>

<style scoped>
.hair-classification {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: 1rem;
  margin-top: 0.75rem;
}

.hair-classification__header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--gray-200);
}

.hair-classification__icon {
  font-size: 1.25rem;
}

.hair-classification__title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700);
  margin: 0;
  flex: 1;
}

.confidence-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.confidence--high {
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green-dark);
  border: 1px solid var(--plumbob-green-light);
}

.confidence--medium {
  background: var(--sims-orange-bg);
  color: var(--sims-orange);
  border: 1px solid var(--sims-orange-light);
}

.confidence--low {
  background: var(--gray-100);
  color: var(--gray-600);
  border: 1px solid var(--gray-300);
}

.hair-classification__content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.hair-attribute {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.hair-attribute__label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 5rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.hair-attribute__icon {
  font-size: 0.875rem;
}

.hair-attribute__value {
  flex: 1;
}

.hair-length-badge,
.hair-style-badge,
.hair-texture-badge,
.hair-accessory-badge {
  display: inline-block;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  text-transform: capitalize;
}

/* Hair Length Styles */
.hair-length--very_short { background: #fef3c7; color: #d97706; }
.hair-length--short { background: #fde68a; color: #d97706; }
.hair-length--medium { background: #fed7aa; color: #ea580c; }
.hair-length--long { background: #fecaca; color: #dc2626; }
.hair-length--very_long { background: #fda4af; color: #be185d; }

/* Hair Style Styles */
.hair-style--ponytail { background: var(--sims-blue-soft); color: var(--sims-blue-dark); }
.hair-style--bun { background: var(--sims-purple-soft); color: var(--sims-purple); }
.hair-style--braids { background: var(--sims-pink-soft); color: var(--sims-pink); }
.hair-style--loose { background: var(--plumbob-green-bg); color: var(--plumbob-green-dark); }
.hair-style--bangs { background: var(--sims-orange-soft); color: var(--sims-orange); }
.hair-style--side_swept { background: #e0e7ff; color: #6366f1; }
.hair-style--pigtails { background: #fce7f3; color: #ec4899; }
.hair-style--updo { background: #f3e8ff; color: #9333ea; }
.hair-style--half_up { background: #ecfdf5; color: #059669; }

/* Hair Texture Styles */
.hair-texture--straight { background: #f0f9ff; color: #0284c7; }
.hair-texture--wavy { background: #f0fdf4; color: #16a34a; }
.hair-texture--curly { background: #fefce8; color: #ca8a04; }
.hair-texture--coily { background: #fdf2f8; color: #be185d; }

.hair-accessory-badge {
  background: var(--plumbob-green-bg);
  color: var(--plumbob-green-dark);
}

.hair-styles {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.hair-classification__meta {
  margin-top: 0.5rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detection-method {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--gray-500);
}

.detection-method__icon {
  font-size: 0.75rem;
}

.hair-keywords {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.keywords-label {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.keyword-tag {
  font-size: 0.625rem;
  background: var(--gray-100);
  color: var(--gray-600);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
}
</style>
