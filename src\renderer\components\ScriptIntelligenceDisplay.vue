<template>
  <div class="script-intelligence">
    <!-- Overview Cards -->
    <div class="script-intelligence__overview">
      <div class="overview-card">
        <div class="overview-card__icon">
          <CodeBracketIcon />
        </div>
        <div class="overview-card__content">
          <div class="overview-card__label">Script Type</div>
          <div class="overview-card__value">{{ formatScriptType(intelligence.scriptType) }}</div>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="overview-card__icon framework-icon">
          <CubeIcon />
        </div>
        <div class="overview-card__content">
          <div class="overview-card__label">Framework</div>
          <div class="overview-card__value">{{ formatFramework(intelligence.framework) }}</div>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="overview-card__icon complexity-icon">
          <ChartBarIcon />
        </div>
        <div class="overview-card__content">
          <div class="overview-card__label">Complexity</div>
          <div class="overview-card__value">
            {{ formatComplexity(intelligence.complexity.level) }}
            <span class="complexity-score">({{ intelligence.complexity.score }})</span>
          </div>
        </div>
      </div>
      
      <div class="overview-card">
        <div class="overview-card__icon risk-icon" :class="`risk-${intelligence.riskLevel}`">
          <ShieldCheckIcon v-if="intelligence.riskLevel === 'low'" />
          <ShieldExclamationIcon v-else-if="intelligence.riskLevel === 'medium'" />
          <ExclamationTriangleIcon v-else />
        </div>
        <div class="overview-card__content">
          <div class="overview-card__label">Risk Level</div>
          <div class="overview-card__value" :class="`risk-${intelligence.riskLevel}`">
            {{ formatRiskLevel(intelligence.riskLevel) }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Complexity Analysis -->
    <div v-if="intelligence.complexity.factors.length > 0" class="script-intelligence__section">
      <h5 class="section-title">Complexity Factors</h5>
      <div class="complexity-factors">
        <span 
          v-for="factor in intelligence.complexity.factors" 
          :key="factor"
          class="complexity-factor"
        >
          {{ factor }}
        </span>
      </div>
    </div>
    
    <!-- Features Detection -->
    <div v-if="intelligence.features.length > 0" class="script-intelligence__section">
      <h5 class="section-title">Detected Features</h5>
      <div class="features-grid">
        <div 
          v-for="feature in intelligence.features" 
          :key="feature"
          class="feature-item"
        >
          <component :is="getFeatureIcon(feature)" class="feature-icon" />
          <span class="feature-name">{{ formatFeature(feature) }}</span>
        </div>
      </div>
    </div>
    
    <!-- Dependencies -->
    <div v-if="intelligence.dependencies.length > 0" class="script-intelligence__section">
      <h5 class="section-title">Dependencies</h5>
      <div class="dependencies-list">
        <div 
          v-for="dependency in intelligence.dependencies" 
          :key="dependency"
          class="dependency-item"
        >
          <LinkIcon class="dependency-icon" />
          <code class="dependency-name">{{ dependency }}</code>
        </div>
      </div>
    </div>
    
    <!-- Performance Impact -->
    <div class="script-intelligence__section">
      <h5 class="section-title">Performance Impact</h5>
      <div class="performance-grid">
        <div class="performance-metric">
          <div class="metric-label">Overall Impact</div>
          <div class="metric-value" :class="`impact-${intelligence.performance.estimatedImpact}`">
            {{ formatImpact(intelligence.performance.estimatedImpact) }}
          </div>
        </div>
        
        <div class="performance-metric">
          <div class="metric-label">Memory Usage</div>
          <div class="metric-value" :class="`usage-${intelligence.performance.memoryUsage}`">
            {{ formatUsage(intelligence.performance.memoryUsage) }}
          </div>
        </div>
        
        <div class="performance-metric">
          <div class="metric-label">CPU Usage</div>
          <div class="metric-value" :class="`usage-${intelligence.performance.cpuUsage}`">
            {{ formatUsage(intelligence.performance.cpuUsage) }}
          </div>
        </div>
        
        <div class="performance-metric">
          <div class="metric-label">Load Time</div>
          <div class="metric-value" :class="`load-${intelligence.performance.loadTime}`">
            {{ formatLoadTime(intelligence.performance.loadTime) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  CodeBracketIcon,
  CubeIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  ShieldExclamationIcon,
  ExclamationTriangleIcon,
  LinkIcon,
  CommandLineIcon,
  PaintBrushIcon,
  CogIcon,
  ClockIcon,
  DocumentTextIcon,
  BellIcon,
  FolderIcon,
  GlobeAltIcon
} from '@heroicons/vue/24/outline';

interface ScriptIntelligence {
  scriptType: string;
  framework: string;
  complexity: {
    level: string;
    score: number;
    factors: string[];
  };
  features: string[];
  dependencies: string[];
  performance: {
    estimatedImpact: string;
    memoryUsage: string;
    cpuUsage: string;
    loadTime: string;
  };
  qualityScore: number;
  riskLevel: string;
}

defineProps<{
  intelligence: ScriptIntelligence;
}>();

// Formatting functions
const formatScriptType = (type: string): string => {
  const types: Record<string, string> = {
    'python_mod': 'Python Mod',
    'javascript_mod': 'JavaScript Mod',
    'compiled_library': 'Compiled Library',
    'framework_extension': 'Framework Extension',
    'utility_script': 'Utility Script',
    'unknown': 'Unknown'
  };
  return types[type] || type;
};

const formatFramework = (framework: string): string => {
  const frameworks: Record<string, string> = {
    'mc_command_center': 'MC Command Center',
    'basemental_drugs': 'Basemental Drugs',
    'wicked_whims': 'Wicked Whims',
    'slice_of_life': 'Slice of Life',
    'ui_cheats': 'UI Cheats',
    'custom_framework': 'Custom Framework',
    'standalone': 'Standalone',
    'unknown': 'Unknown'
  };
  return frameworks[framework] || framework;
};

const formatComplexity = (level: string): string => {
  const levels: Record<string, string> = {
    'simple': 'Simple',
    'moderate': 'Moderate',
    'complex': 'Complex',
    'advanced': 'Advanced'
  };
  return levels[level] || level;
};

const formatRiskLevel = (level: string): string => {
  const levels: Record<string, string> = {
    'low': 'Low Risk',
    'medium': 'Medium Risk',
    'high': 'High Risk'
  };
  return levels[level] || level;
};

const formatFeature = (feature: string): string => {
  const features: Record<string, string> = {
    'ui_modification': 'UI Modification',
    'gameplay_override': 'Gameplay Override',
    'cheat_commands': 'Cheat Commands',
    'automation': 'Automation',
    'data_tracking': 'Data Tracking',
    'event_handling': 'Event Handling',
    'menu_injection': 'Menu Injection',
    'notification_system': 'Notification System',
    'save_data_management': 'Save Data Management',
    'network_communication': 'Network Communication'
  };
  return features[feature] || feature;
};

const formatImpact = (impact: string): string => {
  const impacts: Record<string, string> = {
    'minimal': 'Minimal',
    'low': 'Low',
    'medium': 'Medium',
    'high': 'High'
  };
  return impacts[impact] || impact;
};

const formatUsage = (usage: string): string => {
  const usages: Record<string, string> = {
    'low': 'Low',
    'medium': 'Medium',
    'high': 'High'
  };
  return usages[usage] || usage;
};

const formatLoadTime = (loadTime: string): string => {
  const times: Record<string, string> = {
    'fast': 'Fast',
    'medium': 'Medium',
    'slow': 'Slow'
  };
  return times[loadTime] || loadTime;
};

const getFeatureIcon = (feature: string) => {
  const icons: Record<string, any> = {
    'ui_modification': PaintBrushIcon,
    'gameplay_override': CogIcon,
    'cheat_commands': CommandLineIcon,
    'automation': ClockIcon,
    'data_tracking': DocumentTextIcon,
    'event_handling': CogIcon,
    'menu_injection': PaintBrushIcon,
    'notification_system': BellIcon,
    'save_data_management': FolderIcon,
    'network_communication': GlobeAltIcon
  };
  return icons[feature] || CogIcon;
};
</script>

<style scoped>
.script-intelligence {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

/* Overview Cards */
.script-intelligence__overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.overview-card {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: all var(--duration-150) var(--ease-out);
}

.overview-card:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.overview-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background: var(--sims-purple-bg);
  color: var(--sims-purple);
}

.overview-card__icon.framework-icon {
  background: var(--sims-blue-bg);
  color: var(--sims-blue);
}

.overview-card__icon.complexity-icon {
  background: var(--sims-orange-bg);
  color: var(--sims-orange);
}

.overview-card__icon.risk-icon.risk-low {
  background: var(--success-bg);
  color: var(--success);
}

.overview-card__icon.risk-icon.risk-medium {
  background: var(--warning-bg);
  color: var(--warning);
}

.overview-card__icon.risk-icon.risk-high {
  background: var(--error-bg);
  color: var(--error);
}

.overview-card__icon svg {
  width: 20px;
  height: 20px;
}

.overview-card__content {
  flex: 1;
  min-width: 0;
}

.overview-card__label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-1);
}

.overview-card__value {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.overview-card__value.risk-low {
  color: var(--success);
}

.overview-card__value.risk-medium {
  color: var(--warning);
}

.overview-card__value.risk-high {
  color: var(--error);
}

.complexity-score {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-normal);
}

/* Sections */
.script-intelligence__section {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.section-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Complexity Factors */
.complexity-factors {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.complexity-factor {
  padding: var(--space-2) var(--space-3);
  background: var(--sims-orange-bg);
  color: var(--sims-orange);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

/* Features */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.feature-icon {
  width: 16px;
  height: 16px;
  color: var(--sims-purple);
}

.feature-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

/* Dependencies */
.dependencies-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.dependency-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.dependency-icon {
  width: 16px;
  height: 16px;
  color: var(--text-tertiary);
}

.dependency-name {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  color: var(--text-primary);
  background: none;
  padding: 0;
}

/* Performance */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-4);
}

.performance-metric {
  text-align: center;
}

.metric-label {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-2);
}

.metric-value {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
}

.metric-value.impact-minimal,
.metric-value.usage-low,
.metric-value.load-fast {
  background: var(--success-bg);
  color: var(--success);
}

.metric-value.impact-low,
.metric-value.usage-medium,
.metric-value.load-medium {
  background: var(--info-bg);
  color: var(--info);
}

.metric-value.impact-medium {
  background: var(--warning-bg);
  color: var(--warning);
}

.metric-value.impact-high,
.metric-value.usage-high,
.metric-value.load-slow {
  background: var(--error-bg);
  color: var(--error);
}
</style>
