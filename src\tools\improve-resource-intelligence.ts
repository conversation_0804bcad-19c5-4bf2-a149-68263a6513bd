#!/usr/bin/env node

/**
 * Resource Intelligence Improvement Tool
 * 
 * Analyzes current Resource Intelligence gaps and implements improvements
 * to increase coverage from 77% to 90%+
 */

import * as fs from 'fs';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';

interface ResourceIntelligenceGap {
    fileName: string;
    fileExtension: string;
    resourceCount: number | string;
    currentIntelligence: boolean;
    gapReason: string;
    improvementOpportunity: string;
}

class ResourceIntelligenceImprover {
    private analysisService: PackageAnalysisService;
    
    constructor() {
        this.analysisService = new PackageAnalysisService();
    }
    
    /**
     * Analyzes Resource Intelligence gaps and proposes improvements
     */
    public async analyzeAndImprove(): Promise<void> {
        console.log('🔍 RESOURCE INTELLIGENCE IMPROVEMENT ANALYSIS');
        console.log('Identifying gaps and implementing enhancements\n');
        
        const modsDir = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
        
        if (!fs.existsSync(modsDir)) {
            console.error('❌ Mods directory not found:', modsDir);
            return;
        }
        
        // Find all mod files
        const modFiles = this.findModFiles(modsDir);
        console.log(`📦 Found ${modFiles.length} mod files to analyze\n`);
        
        // Sample analysis (first 100 files for detailed gap analysis)
        const sampleFiles = modFiles.slice(0, 100);
        const gaps: ResourceIntelligenceGap[] = [];
        
        console.log('🔍 Analyzing Resource Intelligence gaps...\n');
        
        for (let i = 0; i < sampleFiles.length; i++) {
            const filePath = sampleFiles[i];
            const fileName = path.basename(filePath);
            
            if (i % 20 === 0) {
                console.log(`Progress: ${i + 1}/${sampleFiles.length} files analyzed`);
            }
            
            const gap = await this.analyzeFile(filePath);
            if (gap) {
                gaps.push(gap);
            }
        }
        
        console.log(`\n📊 Analysis complete: ${gaps.length} gaps identified\n`);
        
        this.generateImprovementReport(gaps);
        this.proposeImplementationPlan(gaps);
    }
    
    /**
     * Recursively finds all mod files
     */
    private findModFiles(dir: string): string[] {
        const modFiles: string[] = [];
        
        try {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    modFiles.push(...this.findModFiles(fullPath));
                } else if (stat.isFile()) {
                    if (item.endsWith('.package') || item.endsWith('.ts4script')) {
                        modFiles.push(fullPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not read directory ${dir}:`, error);
        }
        
        return modFiles;
    }
    
    /**
     * Analyzes a single file for Resource Intelligence gaps
     */
    private async analyzeFile(filePath: string): Promise<ResourceIntelligenceGap | null> {
        const fileName = path.basename(filePath);
        const fileExtension = path.extname(fileName);
        
        try {
            const buffer = fs.readFileSync(filePath);
            const analysis = await this.analysisService.detailedAnalyzeAsync(buffer, filePath);
            
            const hasResourceIntelligence = !!analysis.intelligence?.resourceIntelligence;
            const resourceCount = analysis.resourceCount || 'N/A';
            
            // Only analyze files missing Resource Intelligence
            if (!hasResourceIntelligence) {
                const gap: ResourceIntelligenceGap = {
                    fileName,
                    fileExtension,
                    resourceCount,
                    currentIntelligence: false,
                    gapReason: this.identifyGapReason(fileExtension, resourceCount),
                    improvementOpportunity: this.identifyImprovementOpportunity(fileExtension, resourceCount)
                };
                
                return gap;
            }
            
            return null;
            
        } catch (error) {
            return {
                fileName,
                fileExtension,
                resourceCount: 'Error',
                currentIntelligence: false,
                gapReason: 'Analysis failed',
                improvementOpportunity: 'Fix analysis pipeline'
            };
        }
    }
    
    /**
     * Identifies the reason for Resource Intelligence gap
     */
    private identifyGapReason(fileExtension: string, resourceCount: number | string): string {
        if (fileExtension === '.ts4script') {
            return 'Script files not supported by Resource Intelligence';
        }
        
        if (resourceCount === 'N/A' || resourceCount === 'Error') {
            return 'Resource count unavailable - possible parsing issue';
        }
        
        if (resourceCount === 1) {
            return 'Single resource file - may be skipped by current logic';
        }
        
        if (typeof resourceCount === 'number' && resourceCount <= 3) {
            return 'Low resource count - may not trigger detailed analysis';
        }
        
        return 'Unknown reason - needs investigation';
    }
    
    /**
     * Identifies improvement opportunities
     */
    private identifyImprovementOpportunity(fileExtension: string, resourceCount: number | string): string {
        if (fileExtension === '.ts4script') {
            return 'Create Script Intelligence Analyzer for .ts4script files';
        }
        
        if (resourceCount === 'N/A' || resourceCount === 'Error') {
            return 'Improve resource parsing and error handling';
        }
        
        if (resourceCount === 1) {
            return 'Enable Resource Intelligence for single-resource files';
        }
        
        if (typeof resourceCount === 'number' && resourceCount <= 3) {
            return 'Lower threshold for Resource Intelligence activation';
        }
        
        return 'Investigate and fix underlying issue';
    }
    
    /**
     * Generates improvement report
     */
    private generateImprovementReport(gaps: ResourceIntelligenceGap[]): void {
        console.log('📊 RESOURCE INTELLIGENCE GAP ANALYSIS');
        console.log('=====================================\n');
        
        // Group gaps by reason
        const gapsByReason = new Map<string, ResourceIntelligenceGap[]>();
        gaps.forEach(gap => {
            const reason = gap.gapReason;
            if (!gapsByReason.has(reason)) {
                gapsByReason.set(reason, []);
            }
            gapsByReason.get(reason)!.push(gap);
        });
        
        console.log('🔍 GAP BREAKDOWN:');
        gapsByReason.forEach((gapsForReason, reason) => {
            const count = gapsForReason.length;
            const percentage = Math.round((count / gaps.length) * 100);
            console.log(`   ${reason}: ${count} files (${percentage}%)`);
        });
        
        console.log('\n📈 IMPROVEMENT OPPORTUNITIES:');
        
        // Group by improvement opportunity
        const improvementsByType = new Map<string, number>();
        gaps.forEach(gap => {
            const improvement = gap.improvementOpportunity;
            improvementsByType.set(improvement, (improvementsByType.get(improvement) || 0) + 1);
        });
        
        const sortedImprovements = Array.from(improvementsByType.entries())
            .sort((a, b) => b[1] - a[1]);
        
        sortedImprovements.forEach(([improvement, count]) => {
            const percentage = Math.round((count / gaps.length) * 100);
            console.log(`   ${improvement}: ${count} files (${percentage}%)`);
        });
        
        // File type breakdown
        console.log('\n📁 FILE TYPE BREAKDOWN:');
        const scriptGaps = gaps.filter(g => g.fileExtension === '.ts4script').length;
        const packageGaps = gaps.filter(g => g.fileExtension === '.package').length;
        
        console.log(`   Script files (.ts4script): ${scriptGaps} gaps`);
        console.log(`   Package files (.package): ${packageGaps} gaps`);
        
        // Resource count analysis
        console.log('\n📊 RESOURCE COUNT ANALYSIS:');
        const resourceCounts = new Map<string, number>();
        gaps.forEach(gap => {
            const count = gap.resourceCount.toString();
            resourceCounts.set(count, (resourceCounts.get(count) || 0) + 1);
        });
        
        const sortedCounts = Array.from(resourceCounts.entries())
            .sort((a, b) => {
                if (a[0] === 'N/A') return 1;
                if (b[0] === 'N/A') return -1;
                return parseInt(a[0]) - parseInt(b[0]);
            });
        
        sortedCounts.forEach(([count, files]) => {
            console.log(`   Resource Count ${count}: ${files} files`);
        });
    }
    
    /**
     * Proposes implementation plan
     */
    private proposeImplementationPlan(gaps: ResourceIntelligenceGap[]): void {
        console.log('\n🚀 IMPLEMENTATION PLAN');
        console.log('======================\n');
        
        const scriptGaps = gaps.filter(g => g.fileExtension === '.ts4script').length;
        const singleResourceGaps = gaps.filter(g => g.resourceCount === 1).length;
        const lowResourceGaps = gaps.filter(g => typeof g.resourceCount === 'number' && g.resourceCount <= 3).length;
        const errorGaps = gaps.filter(g => g.resourceCount === 'N/A' || g.resourceCount === 'Error').length;
        
        console.log('📋 PRIORITY 1: Script Intelligence (High Impact)');
        console.log(`   Files affected: ${scriptGaps}`);
        console.log('   Implementation:');
        console.log('   - Create ScriptIntelligenceAnalyzer.ts');
        console.log('   - Analyze Python/JavaScript code structure');
        console.log('   - Detect mod frameworks (MC Command Center, etc.)');
        console.log('   - Estimate complexity and performance impact');
        console.log(`   Expected improvement: +${scriptGaps} files (${Math.round((scriptGaps/gaps.length)*100)}%)\n`);
        
        console.log('📋 PRIORITY 2: Single Resource Enhancement (Medium Impact)');
        console.log(`   Files affected: ${singleResourceGaps}`);
        console.log('   Implementation:');
        console.log('   - Lower Resource Intelligence threshold');
        console.log('   - Add single-resource analysis logic');
        console.log('   - Enhance tuning file intelligence');
        console.log(`   Expected improvement: +${singleResourceGaps} files (${Math.round((singleResourceGaps/gaps.length)*100)}%)\n`);
        
        console.log('📋 PRIORITY 3: Low Resource Count (Medium Impact)');
        console.log(`   Files affected: ${lowResourceGaps}`);
        console.log('   Implementation:');
        console.log('   - Adjust ResourceIntelligenceAnalyzer thresholds');
        console.log('   - Enable analysis for 2-3 resource files');
        console.log('   - Add lightweight analysis mode');
        console.log(`   Expected improvement: +${lowResourceGaps} files (${Math.round((lowResourceGaps/gaps.length)*100)}%)\n`);
        
        console.log('📋 PRIORITY 4: Error Handling (Low Impact)');
        console.log(`   Files affected: ${errorGaps}`);
        console.log('   Implementation:');
        console.log('   - Improve resource parsing robustness');
        console.log('   - Add fallback analysis methods');
        console.log('   - Better error recovery');
        console.log(`   Expected improvement: +${errorGaps} files (${Math.round((errorGaps/gaps.length)*100)}%)\n`);
        
        const totalPotentialImprovement = scriptGaps + singleResourceGaps + lowResourceGaps + errorGaps;
        const currentCoverage = 77; // From report
        const projectedCoverage = currentCoverage + Math.round((totalPotentialImprovement / 1331) * 100);
        
        console.log('🎯 PROJECTED RESULTS:');
        console.log(`   Current Coverage: ${currentCoverage}%`);
        console.log(`   Potential Improvement: +${Math.round((totalPotentialImprovement / 1331) * 100)}% points`);
        console.log(`   Projected Coverage: ${Math.min(projectedCoverage, 95)}%`);
        console.log(`   Target: 90%+ Resource Intelligence coverage ✅`);
        
        console.log('\n⏱️ IMPLEMENTATION TIMELINE:');
        console.log('   Week 1: Script Intelligence Analyzer');
        console.log('   Week 2: Single Resource Enhancement');
        console.log('   Week 3: Low Resource Count Support');
        console.log('   Week 4: Error Handling & Testing');
        console.log('   Week 5: Integration & Validation');
    }
}

// Run the improvement analysis
async function main() {
    const improver = new ResourceIntelligenceImprover();
    await improver.analyzeAndImprove();
}

if (require.main === module) {
    main().catch(console.error);
}
