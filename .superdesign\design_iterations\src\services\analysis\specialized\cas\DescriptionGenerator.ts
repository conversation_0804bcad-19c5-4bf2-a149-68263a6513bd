/**
 * CAS Description Generator
 * 
 * Specialized component for generating human-readable descriptions of CAS parts.
 * Creates meaningful descriptions based on extracted CAS information.
 * 
 * Extracted from CASPartAnalyzer.ts as part of Phase 2 refactoring.
 */

import { CAS_CATEGORY_DISPLAY_NAMES, type CASPartInfo, type CASPartSummary } from './types';

/**
 * Handles generation of human-readable descriptions for CAS parts
 */
export class DescriptionGenerator {
    
    /**
     * Generates a comprehensive description for a CAS part
     * 
     * @param casPartInfo - CAS part information
     * @returns Human-readable description
     */
    public static generateCASDescription(casPartInfo: CASPartInfo): string {
        const parts: string[] = [];
        
        // Add category
        const categoryName = CAS_CATEGORY_DISPLAY_NAMES[casPartInfo.category] || 'Unknown';
        parts.push(categoryName);
        
        // Add subcategory if different from category
        if (casPartInfo.subcategory && casPartInfo.subcategory !== casPartInfo.category) {
            parts.push(`(${this.formatSubcategory(casPartInfo.subcategory)})`);
        }
        
        // Add age groups if specific
        if (casPartInfo.ageGroups.length > 0 && !this.isAllAges(casPartInfo.ageGroups)) {
            const ageDescription = this.formatAgeGroups(casPartInfo.ageGroups);
            parts.push(`for ${ageDescription}`);
        }
        
        // Add gender if specific
        if (casPartInfo.genders.length === 1 && casPartInfo.genders[0] !== 'unisex') {
            parts.push(`(${casPartInfo.genders[0]})`);
        }
        
        // Add clothing categories if relevant
        if (casPartInfo.isClothing && casPartInfo.clothingCategories.length > 0) {
            const clothingDescription = this.formatClothingCategories(casPartInfo.clothingCategories);
            if (clothingDescription !== 'everyday') { // Don't mention everyday as it's default
                parts.push(`- ${clothingDescription}`);
            }
        }
        
        return parts.join(' ');
    }
    
    /**
     * Formats subcategory for display
     * 
     * @param subcategory - Raw subcategory string
     * @returns Formatted subcategory
     */
    private static formatSubcategory(subcategory: string): string {
        return subcategory
            .replace(/_/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    }
    
    /**
     * Formats age groups for display
     * 
     * @param ageGroups - Array of age groups
     * @returns Formatted age groups string
     */
    private static formatAgeGroups(ageGroups: string[]): string {
        if (ageGroups.length === 0) {
            return 'all ages';
        }
        
        if (ageGroups.length === 1) {
            return this.formatAgeGroup(ageGroups[0]);
        }
        
        // Check for common age ranges
        const ageOrder = ['infant', 'toddler', 'child', 'teen', 'young_adult', 'adult', 'elder'];
        const sortedAges = ageGroups.sort((a, b) => ageOrder.indexOf(a) - ageOrder.indexOf(b));
        
        // Check for adult range (teen+)
        if (this.isAdultRange(sortedAges)) {
            return 'teens and adults';
        }
        
        // Check for child range
        if (this.isChildRange(sortedAges)) {
            return 'children and teens';
        }
        
        // Format as list
        const formattedAges = sortedAges.map(age => this.formatAgeGroup(age));
        if (formattedAges.length === 2) {
            return formattedAges.join(' and ');
        }
        
        return formattedAges.slice(0, -1).join(', ') + ', and ' + formattedAges[formattedAges.length - 1];
    }
    
    /**
     * Formats a single age group for display
     * 
     * @param ageGroup - Age group string
     * @returns Formatted age group
     */
    private static formatAgeGroup(ageGroup: string): string {
        const ageDisplayNames: Record<string, string> = {
            'infant': 'infants',
            'toddler': 'toddlers',
            'child': 'children',
            'teen': 'teens',
            'young_adult': 'young adults',
            'adult': 'adults',
            'elder': 'elders'
        };
        
        return ageDisplayNames[ageGroup] || ageGroup;
    }
    
    /**
     * Checks if age groups represent all ages
     * 
     * @param ageGroups - Array of age groups
     * @returns true if represents all or most ages
     */
    private static isAllAges(ageGroups: string[]): boolean {
        return ageGroups.length >= 5; // 5 or more age groups = "all ages"
    }
    
    /**
     * Checks if age groups represent adult range (teen+)
     * 
     * @param ageGroups - Array of age groups
     * @returns true if represents teen and adult ages
     */
    private static isAdultRange(ageGroups: string[]): boolean {
        const adultAges = ['teen', 'young_adult', 'adult', 'elder'];
        return ageGroups.length >= 3 && ageGroups.every(age => adultAges.includes(age));
    }
    
    /**
     * Checks if age groups represent child range
     * 
     * @param ageGroups - Array of age groups
     * @returns true if represents child and teen ages
     */
    private static isChildRange(ageGroups: string[]): boolean {
        const childAges = ['child', 'teen'];
        return ageGroups.length === 2 && ageGroups.every(age => childAges.includes(age));
    }
    
    /**
     * Formats clothing categories for display
     * 
     * @param clothingCategories - Array of clothing categories
     * @returns Formatted clothing categories string
     */
    private static formatClothingCategories(clothingCategories: string[]): string {
        if (clothingCategories.length === 0) {
            return 'everyday';
        }
        
        if (clothingCategories.length === 1) {
            return this.formatClothingCategory(clothingCategories[0]);
        }
        
        const formattedCategories = clothingCategories.map(cat => this.formatClothingCategory(cat));
        if (formattedCategories.length === 2) {
            return formattedCategories.join(' and ');
        }
        
        return formattedCategories.slice(0, -1).join(', ') + ', and ' + formattedCategories[formattedCategories.length - 1];
    }
    
    /**
     * Formats a single clothing category for display
     * 
     * @param clothingCategory - Clothing category string
     * @returns Formatted clothing category
     */
    private static formatClothingCategory(clothingCategory: string): string {
        return clothingCategory
            .replace(/_/g, ' ')
            .toLowerCase();
    }
    
    /**
     * Summarizes multiple CAS parts into a single description
     * 
     * @param casPartInfos - Array of CAS part information
     * @returns Summary description
     */
    public static summarizeCASParts(casPartInfos: CASPartInfo[]): CASPartSummary {
        if (casPartInfos.length === 0) {
            return {
                primaryCategory: 'Unknown CAS',
                subcategory: 'unknown',
                description: 'Unknown CAS content'
            };
        }
        
        // Find the most common category
        const categoryCount = new Map<string, number>();
        casPartInfos.forEach(info => {
            const category = info.category;
            categoryCount.set(category, (categoryCount.get(category) || 0) + 1);
        });
        
        const primaryCategory = Array.from(categoryCount.entries())
            .sort((a, b) => b[1] - a[1])[0][0];
        
        // Get the most specific subcategory
        const subcategories = casPartInfos
            .filter(info => info.category === primaryCategory)
            .map(info => info.subcategory);
        
        const subcategory = subcategories[0] || 'unknown';
        
        // Generate summary description
        const categoryDisplayName = CAS_CATEGORY_DISPLAY_NAMES[primaryCategory as keyof typeof CAS_CATEGORY_DISPLAY_NAMES] || 'Unknown CAS';
        const description = `${categoryDisplayName} content with ${casPartInfos.length} part(s)`;
        
        return {
            primaryCategory: categoryDisplayName,
            subcategory,
            description
        };
    }
}
