/**
 * Manifest Detection System
 * 
 * Detects and parses Llama-Logic style manifests and other manifest formats
 * for future-compatible metadata extraction.
 */

import { XmlResource } from '@s4tk/models';
// Note: js-yaml would need to be installed as a dependency
// For now, we'll implement a simple YAML parser or use a different approach
import { DEFAULT_METADATA_CONFIG, type MetadataExtractionConfig } from '../../../config/metadata-extraction.config';

export interface ManifestMetadata {
    readonly author?: string;
    readonly modName?: string;
    readonly version?: string;
    readonly description?: string;
    readonly downloadUrl?: string;
    readonly requirements?: readonly string[];
    readonly installationGuidelines?: string;
    readonly confidence: number;
    readonly source: 'manifest';
    readonly manifestType: 'llama-logic' | 'yaml' | 'json' | 'xml-comment' | 'custom';
    readonly manifestVersion?: string;
    readonly processingTime: number;
}

export interface DetectedManifest {
    readonly type: 'llama-logic' | 'yaml' | 'json' | 'xml-comment' | 'custom';
    readonly content: string;
    readonly resourceId?: string;
    readonly confidence: number;
}

/**
 * Manifest detection and parsing system
 */
export class ManifestDetector {
    private readonly config: MetadataExtractionConfig;
    
    constructor(config: Partial<MetadataExtractionConfig> = {}) {
        this.config = {
            ...DEFAULT_METADATA_CONFIG,
            ...config
        };
    }
    
    /**
     * Detects manifests in package resources
     */
    async detectManifests(resources: Map<string, Buffer>): Promise<DetectedManifest[]> {
        const manifests: DetectedManifest[] = [];
        
        for (const [resourceId, buffer] of resources) {
            try {
                const detected = await this.detectManifestInResource(resourceId, buffer);
                if (detected) {
                    manifests.push({
                        ...detected,
                        resourceId
                    });
                }
            } catch (error) {
                console.warn(`[ManifestDetector] Error detecting manifest in ${resourceId}:`, error);
            }
        }
        
        return manifests.sort((a, b) => b.confidence - a.confidence);
    }
    
    /**
     * Parses detected manifest to extract metadata
     */
    async parseManifest(manifest: DetectedManifest): Promise<ManifestMetadata | null> {
        const startTime = performance.now();
        
        try {
            let metadata: Omit<ManifestMetadata, 'processingTime'> | null = null;
            
            switch (manifest.type) {
                case 'llama-logic':
                    metadata = this.parseLlamaLogicManifest(manifest.content);
                    break;
                case 'yaml':
                    metadata = this.parseYamlManifest(manifest.content);
                    break;
                case 'json':
                    metadata = this.parseJsonManifest(manifest.content);
                    break;
                case 'xml-comment':
                    metadata = this.parseXmlCommentManifest(manifest.content);
                    break;
                case 'custom':
                    metadata = this.parseCustomManifest(manifest.content);
                    break;
            }
            
            const processingTime = performance.now() - startTime;
            
            if (metadata) {
                return {
                    ...metadata,
                    processingTime
                };
            }
            
            return null;
            
        } catch (error) {
            console.warn('[ManifestDetector] Manifest parsing failed:', error);
            return null;
        }
    }
    
    /**
     * Detects manifest in a single resource
     */
    private async detectManifestInResource(resourceId: string, buffer: Buffer): Promise<Omit<DetectedManifest, 'resourceId'> | null> {
        try {
            // Try to parse as XML resource first (most common for tuning)
            const xmlResource = XmlResource.from(buffer);
            const xmlContent = xmlResource.content;
            
            // Check for Llama-Logic manifest in XML
            if (this.isLlamaLogicManifest(xmlContent)) {
                return {
                    type: 'llama-logic',
                    content: xmlContent,
                    confidence: 95
                };
            }
            
            // Check for YAML manifest in XML comments
            const yamlInComment = this.extractYamlFromXmlComments(xmlContent);
            if (yamlInComment) {
                return {
                    type: 'yaml',
                    content: yamlInComment,
                    confidence: 85
                };
            }
            
            // Check for JSON manifest in XML comments
            const jsonInComment = this.extractJsonFromXmlComments(xmlContent);
            if (jsonInComment) {
                return {
                    type: 'json',
                    content: jsonInComment,
                    confidence: 80
                };
            }
            
            // Check for custom manifest patterns in XML comments
            const customManifest = this.extractCustomManifestFromXml(xmlContent);
            if (customManifest) {
                return {
                    type: 'xml-comment',
                    content: customManifest,
                    confidence: 70
                };
            }
            
        } catch (xmlError) {
            // If XML parsing fails, try as raw text
            try {
                const textContent = buffer.toString('utf-8');
                
                // Check for standalone YAML
                if (this.looksLikeYaml(textContent)) {
                    return {
                        type: 'yaml',
                        content: textContent,
                        confidence: 75
                    };
                }
                
                // Check for standalone JSON
                if (this.looksLikeJson(textContent)) {
                    return {
                        type: 'json',
                        content: textContent,
                        confidence: 70
                    };
                }
                
            } catch (textError) {
                // Ignore text parsing errors
            }
        }
        
        return null;
    }
    
    /**
     * Checks if XML content contains Llama-Logic manifest
     */
    private isLlamaLogicManifest(xmlContent: string): boolean {
        return xmlContent.includes('ModFileManifest') ||
               xmlContent.includes('GlobalManifest') ||
               (xmlContent.includes('manifest') && xmlContent.includes('author')) ||
               xmlContent.includes('LlamaLogic');
    }
    
    /**
     * Extracts YAML from XML comments
     */
    private extractYamlFromXmlComments(xmlContent: string): string | null {
        const commentRegex = /<!--\s*([\s\S]*?)\s*-->/g;
        let match;
        
        while ((match = commentRegex.exec(xmlContent)) !== null) {
            const comment = match[1].trim();
            if (this.looksLikeYaml(comment)) {
                return comment;
            }
        }
        
        return null;
    }
    
    /**
     * Extracts JSON from XML comments
     */
    private extractJsonFromXmlComments(xmlContent: string): string | null {
        const commentRegex = /<!--\s*([\s\S]*?)\s*-->/g;
        let match;
        
        while ((match = commentRegex.exec(xmlContent)) !== null) {
            const comment = match[1].trim();
            if (this.looksLikeJson(comment)) {
                return comment;
            }
        }
        
        return null;
    }
    
    /**
     * Extracts custom manifest patterns from XML
     */
    private extractCustomManifestFromXml(xmlContent: string): string | null {
        const patterns = [
            /<!--\s*MANIFEST\s*([\s\S]*?)\s*-->/i,
            /<!--\s*MOD\s*INFO\s*([\s\S]*?)\s*-->/i,
            /<!--\s*METADATA\s*([\s\S]*?)\s*-->/i
        ];
        
        for (const pattern of patterns) {
            const match = xmlContent.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        
        return null;
    }
    
    /**
     * Checks if content looks like YAML
     */
    private looksLikeYaml(content: string): boolean {
        return content.includes(':') &&
               (content.includes('\n') || content.includes('author:') || content.includes('version:')) &&
               !content.includes('{') &&
               !content.includes('<');
    }
    
    /**
     * Checks if content looks like JSON
     */
    private looksLikeJson(content: string): boolean {
        return content.trim().startsWith('{') && 
               content.trim().endsWith('}') &&
               content.includes('"');
    }
    
    /**
     * Parses Llama-Logic manifest
     */
    private parseLlamaLogicManifest(content: string): Omit<ManifestMetadata, 'processingTime'> | null {
        try {
            // Extract YAML from XML if needed
            let yamlContent = content;
            
            // If it's XML, extract the YAML content
            if (content.includes('<') && content.includes('>')) {
                const yamlMatch = content.match(/<!\[CDATA\[([\s\S]*?)\]\]>/);
                if (yamlMatch) {
                    yamlContent = yamlMatch[1];
                } else {
                    // Try to extract from text content
                    const textMatch = content.match(/>([^<]*(?:author|version|name)[^<]*)</);
                    if (textMatch) {
                        yamlContent = textMatch[1];
                    }
                }
            }
            
            const parsed = this.parseSimpleYaml(yamlContent);
            
            return {
                author: this.sanitizeString(parsed.author || parsed.creator),
                modName: this.sanitizeString(parsed.name || parsed.title || parsed.mod_name),
                version: this.sanitizeString(parsed.version),
                description: this.sanitizeString(parsed.description),
                downloadUrl: this.sanitizeString(parsed.download_url || parsed.url),
                requirements: Array.isArray(parsed.requirements) ? parsed.requirements.map(this.sanitizeString) : undefined,
                installationGuidelines: this.sanitizeString(parsed.installation || parsed.guidelines),
                confidence: 95,
                source: 'manifest',
                manifestType: 'llama-logic',
                manifestVersion: this.sanitizeString(parsed.manifest_version)
            };
            
        } catch (error) {
            console.warn('[ManifestDetector] Llama-Logic manifest parsing failed:', error);
            return null;
        }
    }
    
    /**
     * Parses YAML manifest
     */
    private parseYamlManifest(content: string): Omit<ManifestMetadata, 'processingTime'> | null {
        try {
            const parsed = this.parseSimpleYaml(content);
            
            return {
                author: this.sanitizeString(parsed.author || parsed.creator),
                modName: this.sanitizeString(parsed.name || parsed.title),
                version: this.sanitizeString(parsed.version),
                description: this.sanitizeString(parsed.description),
                downloadUrl: this.sanitizeString(parsed.url),
                confidence: 85,
                source: 'manifest',
                manifestType: 'yaml'
            };
            
        } catch (error) {
            return null;
        }
    }
    
    /**
     * Parses JSON manifest
     */
    private parseJsonManifest(content: string): Omit<ManifestMetadata, 'processingTime'> | null {
        try {
            const parsed = JSON.parse(content);
            
            return {
                author: this.sanitizeString(parsed.author || parsed.creator),
                modName: this.sanitizeString(parsed.name || parsed.title),
                version: this.sanitizeString(parsed.version),
                description: this.sanitizeString(parsed.description),
                downloadUrl: this.sanitizeString(parsed.url),
                confidence: 80,
                source: 'manifest',
                manifestType: 'json'
            };
            
        } catch (error) {
            return null;
        }
    }
    
    /**
     * Parses XML comment manifest
     */
    private parseXmlCommentManifest(content: string): Omit<ManifestMetadata, 'processingTime'> | null {
        const metadata: Partial<ManifestMetadata> = {
            confidence: 70,
            source: 'manifest',
            manifestType: 'xml-comment'
        };
        
        // Extract key-value pairs from comment
        const patterns = [
            { key: 'author', regex: /author\s*[:=]\s*([^\n\r]+)/i },
            { key: 'modName', regex: /(?:name|title)\s*[:=]\s*([^\n\r]+)/i },
            { key: 'version', regex: /version\s*[:=]\s*([^\n\r]+)/i },
            { key: 'description', regex: /description\s*[:=]\s*([^\n\r]+)/i }
        ];
        
        for (const pattern of patterns) {
            const match = content.match(pattern.regex);
            if (match && match[1]) {
                (metadata as any)[pattern.key] = this.sanitizeString(match[1]);
            }
        }
        
        // Only return if we found at least one field
        if (metadata.author || metadata.modName || metadata.version) {
            return metadata as Omit<ManifestMetadata, 'processingTime'>;
        }
        
        return null;
    }
    
    /**
     * Parses custom manifest format
     */
    private parseCustomManifest(content: string): Omit<ManifestMetadata, 'processingTime'> | null {
        // This is a fallback for custom formats
        return this.parseXmlCommentManifest(content);
    }
    
    /**
     * Simple YAML parser for basic key-value pairs
     */
    private parseSimpleYaml(content: string): any {
        const result: any = {};
        const lines = content.split('\n');

        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed || trimmed.startsWith('#')) continue;

            const colonIndex = trimmed.indexOf(':');
            if (colonIndex > 0) {
                const key = trimmed.substring(0, colonIndex).trim();
                const value = trimmed.substring(colonIndex + 1).trim();

                if (key && value) {
                    // Remove quotes if present
                    const cleanValue = value.replace(/^["']|["']$/g, '');
                    result[key] = cleanValue;
                }
            }
        }

        return result;
    }

    /**
     * Sanitizes string values
     */
    private sanitizeString(value: any): string | undefined {
        if (!value || typeof value !== 'string') {
            return undefined;
        }

        const sanitized = value.trim()
            .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
            .substring(0, 500);

        return sanitized.length > 0 ? sanitized : undefined;
    }
}
