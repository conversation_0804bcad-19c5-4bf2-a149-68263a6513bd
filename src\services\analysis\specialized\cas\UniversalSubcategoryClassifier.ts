/**
 * Universal Subcategory Classifier
 * 
 * Unified, configuration-driven classifier that handles ALL subcategory detection
 * across CAS and object categories. Replaces individual classifiers with a single,
 * scalable pattern-based approach.
 * 
 * Generalized from successful HairStyleClassifier methodology (v2.1.0)
 * Part of Unified Subcategory Detection System (v2.2.0)
 */

import { SubcategoryPatternConfig, SubcategoryPattern, PatternDetectionResult } from './SubcategoryPatternConfig';
import { HairClassificationInfo, HairLength, HairStyle, HairTexture } from './types';

/**
 * Universal classification result
 */
export interface UniversalClassificationResult {
    category: string;
    subcategories: string[];
    confidence: number;
    detectionMethod: 'filename' | 'resource_analysis' | 'hybrid';
    keywords: string[];
    metadata: Record<string, any>;
    tags: string[];
}

/**
 * Handles unified subcategory classification using pattern-based detection
 */
export class UniversalSubcategoryClassifier {
    
    /**
     * Classifies content using universal pattern matching
     * 
     * @param filename - The mod filename
     * @param category - Primary content category (hair, clothing, etc.)
     * @param resourceData - Optional resource data for additional analysis
     * @returns Universal classification result
     */
    public static classifySubcategories(
        filename: string, 
        category: string,
        resourceData?: any
    ): UniversalClassificationResult {
        const lowerFilename = filename.toLowerCase();
        const keywords = this.extractKeywords(lowerFilename);
        
        // Get patterns for the specified category
        const patterns = SubcategoryPatternConfig.getPatternsByCategory(category);
        
        // Detect all matching subcategories
        const detectionResults = this.detectSubcategories(lowerFilename, keywords, patterns);
        
        // Calculate overall confidence
        const confidence = this.calculateOverallConfidence(detectionResults, keywords);
        
        // Extract subcategories and metadata
        const subcategories = detectionResults.map(result => result.subcategory);
        const allTags = this.mergeTags(detectionResults);
        const metadata = this.buildMetadata(detectionResults, category);
        
        return {
            category,
            subcategories,
            confidence,
            detectionMethod: 'filename',
            keywords,
            metadata,
            tags: allTags
        };
    }

    /**
     * Specialized hair classification for backward compatibility
     * Maintains the same interface as HairStyleClassifier
     */
    public static classifyHairStyle(filename: string, resourceData?: any): HairClassificationInfo {
        const result = this.classifySubcategories(filename, 'hair', resourceData);
        
        // Extract hair-specific information from universal result
        const length = this.extractHairLength(result);
        const styles = this.extractHairStyles(result);
        const texture = this.extractHairTexture(result);
        const hasAccessories = this.detectHairAccessories(result);
        
        return {
            length,
            style: styles,
            texture,
            hasAccessories,
            confidence: result.confidence,
            detectionMethod: result.detectionMethod,
            keywords: result.keywords
        };
    }

    /**
     * Extracts relevant keywords from filename
     */
    private static extractKeywords(filename: string): string[] {
        // Remove common prefixes/suffixes and split into words
        const cleaned = filename
            .replace(/\.(package|ts4script)$/i, '')
            .replace(/^[^_]*_/, '') // Remove creator prefix
            .replace(/_/g, ' ')
            .toLowerCase();
        
        return cleaned.split(/[\s\-_]+/).filter(word => word.length > 2);
    }

    /**
     * Detects subcategories using pattern matching
     */
    private static detectSubcategories(
        filename: string, 
        keywords: string[], 
        patterns: SubcategoryPattern[]
    ): PatternDetectionResult[] {
        const allText = [filename, ...keywords].join(' ');
        const results: PatternDetectionResult[] = [];
        
        // Sort patterns by priority (higher priority first)
        const sortedPatterns = patterns.sort((a, b) => b.priority - a.priority);
        
        for (const pattern of sortedPatterns) {
            const matchedPatterns = pattern.patterns.filter(p => allText.includes(p));
            
            if (matchedPatterns.length > 0) {
                results.push({
                    category: pattern.category,
                    subcategory: pattern.subcategory,
                    confidence: pattern.confidence,
                    detectedPatterns: matchedPatterns,
                    tags: pattern.tags || []
                });
            }
        }
        
        return results;
    }

    /**
     * Calculates overall confidence based on detection results
     */
    private static calculateOverallConfidence(
        results: PatternDetectionResult[], 
        keywords: string[]
    ): number {
        if (results.length === 0) return 0.3; // Base confidence for filename analysis
        
        let confidence = 0.3; // Base confidence
        
        // Increase confidence based on successful detections
        const avgPatternConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;
        confidence += avgPatternConfidence * 0.4;
        
        // Increase confidence based on keyword quality
        if (keywords.length >= 3) confidence += 0.1;
        if (keywords.some(k => k.includes('hair') || k.includes('clothing') || k.includes('makeup'))) {
            confidence += 0.1;
        }
        
        // Bonus for multiple pattern matches
        if (results.length > 1) confidence += 0.1;
        
        return Math.min(confidence, 1.0);
    }

    /**
     * Merges tags from all detection results
     */
    private static mergeTags(results: PatternDetectionResult[]): string[] {
        const allTags = new Set<string>();
        
        for (const result of results) {
            result.tags.forEach(tag => allTags.add(tag));
        }
        
        return Array.from(allTags);
    }

    /**
     * Builds metadata object from detection results
     */
    private static buildMetadata(results: PatternDetectionResult[], category: string): Record<string, any> {
        const metadata: Record<string, any> = {
            category,
            detectionCount: results.length,
            patterns: results.map(r => ({
                subcategory: r.subcategory,
                confidence: r.confidence,
                patterns: r.detectedPatterns
            }))
        };
        
        // Add category-specific metadata
        if (category === 'hair') {
            metadata.hairSpecific = {
                hasLength: results.some(r => r.subcategory.includes('short') || r.subcategory.includes('long') || r.subcategory.includes('medium')),
                hasStyle: results.some(r => ['ponytail', 'bun', 'braids', 'loose'].includes(r.subcategory)),
                hasTexture: results.some(r => ['straight', 'wavy', 'curly', 'coily'].includes(r.subcategory)),
                hasAccessories: results.some(r => r.subcategory.includes('accessories'))
            };
        }
        
        return metadata;
    }

    /**
     * Extracts hair length from universal result (for backward compatibility)
     */
    private static extractHairLength(result: UniversalClassificationResult): HairLength {
        const lengthSubcategories = result.subcategories.filter(sub => 
            Object.values(HairLength).includes(sub as HairLength)
        );
        
        return lengthSubcategories.length > 0 ? lengthSubcategories[0] as HairLength : HairLength.UNKNOWN;
    }

    /**
     * Extracts hair styles from universal result (for backward compatibility)
     */
    private static extractHairStyles(result: UniversalClassificationResult): HairStyle[] {
        const styleSubcategories = result.subcategories.filter(sub => 
            Object.values(HairStyle).includes(sub as HairStyle)
        );
        
        return styleSubcategories.length > 0 ? 
            styleSubcategories as HairStyle[] : 
            [HairStyle.LOOSE];
    }

    /**
     * Extracts hair texture from universal result (for backward compatibility)
     */
    private static extractHairTexture(result: UniversalClassificationResult): HairTexture {
        const textureSubcategories = result.subcategories.filter(sub => 
            Object.values(HairTexture).includes(sub as HairTexture)
        );
        
        return textureSubcategories.length > 0 ? textureSubcategories[0] as HairTexture : HairTexture.UNKNOWN;
    }

    /**
     * Detects hair accessories from universal result (for backward compatibility)
     */
    private static detectHairAccessories(result: UniversalClassificationResult): boolean {
        return result.subcategories.some(sub => sub.includes('accessories')) ||
               result.tags.some(tag => tag === 'accessories');
    }

    /**
     * Gets human-readable description from universal classification
     */
    public static getDescription(result: UniversalClassificationResult): string {
        if (result.subcategories.length === 0) {
            return `${result.category} content`;
        }
        
        const parts = result.subcategories
            .map(sub => sub.replace('_', ' '))
            .filter(sub => sub !== 'unknown');
        
        if (result.tags.includes('accessories')) {
            parts.push('with accessories');
        }
        
        return parts.length > 0 ? 
            `${parts.join(', ')} ${result.category}` : 
            `${result.category} content`;
    }
}
