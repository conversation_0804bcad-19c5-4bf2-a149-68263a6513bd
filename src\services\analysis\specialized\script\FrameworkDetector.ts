/**
 * Framework Detector
 * 
 * Specialized component for detecting framework, library, cheat, and utility mods
 * based on filename patterns and content analysis.
 * 
 * Extracted from ScriptAnalyzer.ts as part of Phase 2 refactoring.
 */

import {
    FRAMEWORK_PATTERNS,
    LIBRARY_PATTERNS,
    CHEAT_PATTERNS,
    UTILITY_PATTERNS,
    FRAMEWORK_TYPE_MAPPING,
    ScriptModCategory,
    type ScriptModInfo
} from './types';

/**
 * Handles detection of framework, library, cheat, and utility mods
 */
export class FrameworkDetector {
    
    /**
     * Detects if a script is a framework mod
     * 
     * @param fileName - Script file name (lowercase)
     * @returns true if appears to be a framework mod
     */
    public static isFrameworkMod(fileName: string): boolean {
        return FRAMEWORK_PATTERNS.some(pattern => fileName.includes(pattern));
    }
    
    /**
     * Detects if a script is a library mod
     * 
     * @param fileName - Script file name (lowercase)
     * @returns true if appears to be a library mod
     */
    public static isLibraryMod(fileName: string): boolean {
        return LIBRARY_PATTERNS.some(pattern => fileName.includes(pattern));
    }
    
    /**
     * Detects if a script is a cheat mod
     * 
     * @param fileName - Script file name (lowercase)
     * @returns true if appears to be a cheat mod
     */
    public static isCheatMod(fileName: string): boolean {
        return CHEAT_PATTERNS.some(pattern => fileName.includes(pattern));
    }
    
    /**
     * Detects if a script is a utility mod
     * 
     * @param fileName - Script file name (lowercase)
     * @returns true if appears to be a utility mod
     */
    public static isUtilityMod(fileName: string): boolean {
        return UTILITY_PATTERNS.some(pattern => fileName.includes(pattern));
    }
    
    /**
     * Detects the specific framework type
     * 
     * @param fileName - Script file name (lowercase)
     * @returns Framework type identifier
     */
    public static detectFrameworkType(fileName: string): string {
        for (const [pattern, type] of Object.entries(FRAMEWORK_TYPE_MAPPING)) {
            if (fileName.includes(pattern)) {
                return type;
            }
        }
        return 'framework';
    }
    
    /**
     * Detects framework dependencies from content
     * 
     * @param content - Script content to analyze
     * @returns Array of detected framework dependencies
     */
    public static detectFrameworkDependencies(content: string): string[] {
        const dependencies: string[] = [];
        const lowerContent = content.toLowerCase();
        
        const frameworkSignatures = [
            { name: 'MC Command Center', patterns: ['mc_command_center', 'mccc', 'mc_'] },
            { name: 'XML Injector', patterns: ['xml_injector', 'xmlinjector'] },
            { name: 'UI Cheats Extension', patterns: ['ui_cheats', 'uicheats'] },
            { name: 'Basemental', patterns: ['basemental'] },
            { name: 'WickedWhims', patterns: ['wickedwhims', 'ww_'] },
            { name: 'Slice of Life', patterns: ['slice_of_life', 'sol_'] },
            { name: 'Meaningful Stories', patterns: ['meaningful_stories', 'ms_'] }
        ];
        
        frameworkSignatures.forEach(framework => {
            if (framework.patterns.some(pattern => lowerContent.includes(pattern))) {
                dependencies.push(framework.name);
            }
        });
        
        return dependencies;
    }
    
    /**
     * Determines script category and subcategory based on detection results
     * 
     * @param fileName - Script file name (lowercase)
     * @param scriptInfo - Script info object to populate
     */
    public static determineCategory(fileName: string, scriptInfo: ScriptModInfo): void {
        // Framework detection (highest priority)
        if (this.isFrameworkMod(fileName)) {
            scriptInfo.category = ScriptModCategory.FRAMEWORK;
            scriptInfo.isFramework = true;
            scriptInfo.subcategory = this.detectFrameworkType(fileName);
            return;
        }
        
        // Library detection
        if (this.isLibraryMod(fileName)) {
            scriptInfo.category = ScriptModCategory.LIBRARY;
            scriptInfo.isLibrary = true;
            scriptInfo.subcategory = 'script_library';
            return;
        }
        
        // Cheat detection
        if (this.isCheatMod(fileName)) {
            scriptInfo.category = ScriptModCategory.CHEAT;
            scriptInfo.subcategory = 'cheat_mod';
            return;
        }
        
        // Utility detection
        if (this.isUtilityMod(fileName)) {
            scriptInfo.category = ScriptModCategory.UTILITY;
            scriptInfo.subcategory = 'utility_mod';
            return;
        }
        
        // Default to gameplay (will be refined by other components)
        scriptInfo.category = ScriptModCategory.GAMEPLAY;
        scriptInfo.subcategory = 'gameplay_mod';
    }
    
    /**
     * Detects potential conflicts based on framework type
     * 
     * @param scriptInfo - Script info to analyze
     * @returns Array of potential conflict warnings
     */
    public static detectPotentialConflicts(scriptInfo: ScriptModInfo): string[] {
        const conflicts: string[] = [];
        
        // Framework-specific conflict detection
        if (scriptInfo.isFramework) {
            switch (scriptInfo.subcategory) {
                case 'mc_command_center':
                    conflicts.push('May conflict with other MCCC versions or command mods');
                    break;
                case 'ui_cheats':
                    conflicts.push('May conflict with other UI cheat extensions');
                    break;
                case 'xml_injector':
                    conflicts.push('May conflict with other XML injection frameworks');
                    break;
            }
        }
        
        // Category-based conflict detection
        if (scriptInfo.category === ScriptModCategory.CHEAT) {
            conflicts.push('May conflict with other cheat mods or game balance');
        }
        
        if (scriptInfo.category === ScriptModCategory.UTILITY) {
            conflicts.push('May conflict with similar utility mods');
        }
        
        return conflicts;
    }
    
    /**
     * Validates framework detection results
     * 
     * @param scriptInfo - Script info to validate
     * @returns Validation result
     */
    public static validateDetection(scriptInfo: ScriptModInfo): {
        isValid: boolean;
        warnings: string[];
        confidence: number;
    } {
        const warnings: string[] = [];
        let confidence = 100;
        
        // Check for conflicting flags
        if (scriptInfo.isFramework && scriptInfo.isLibrary) {
            warnings.push('Script marked as both framework and library');
            confidence -= 20;
        }
        
        // Check category consistency
        if (scriptInfo.isFramework && scriptInfo.category !== ScriptModCategory.FRAMEWORK) {
            warnings.push('Framework flag inconsistent with category');
            confidence -= 30;
        }
        
        if (scriptInfo.isLibrary && scriptInfo.category !== ScriptModCategory.LIBRARY) {
            warnings.push('Library flag inconsistent with category');
            confidence -= 30;
        }
        
        // Check subcategory validity
        if (!scriptInfo.subcategory || scriptInfo.subcategory === 'unknown') {
            warnings.push('Subcategory not properly determined');
            confidence -= 10;
        }
        
        return {
            isValid: warnings.length === 0,
            warnings,
            confidence: Math.max(0, confidence)
        };
    }
    
    /**
     * Gets human-readable framework type description
     * 
     * @param frameworkType - Framework type identifier
     * @returns Human-readable description
     */
    public static getFrameworkDescription(frameworkType: string): string {
        const descriptions: Record<string, string> = {
            'mc_command_center': 'MC Command Center - Comprehensive gameplay framework',
            'xml_injector': 'XML Injector - Tuning modification framework',
            'ui_cheats': 'UI Cheats Extension - Enhanced cheat interface',
            'framework': 'Generic Framework Mod'
        };
        
        return descriptions[frameworkType] || 'Unknown Framework Type';
    }
    
    /**
     * Determines installation priority based on framework type
     * 
     * @param scriptInfo - Script info to analyze
     * @returns Priority level (1-10, where 1 is highest priority)
     */
    public static getInstallationPriority(scriptInfo: ScriptModInfo): number {
        if (scriptInfo.isFramework) {
            switch (scriptInfo.subcategory) {
                case 'xml_injector': return 1; // Must be installed first
                case 'mc_command_center': return 2; // Core framework
                case 'ui_cheats': return 3; // UI framework
                default: return 4; // Other frameworks
            }
        }
        
        if (scriptInfo.isLibrary) {
            return 5; // Libraries before gameplay mods
        }
        
        switch (scriptInfo.category) {
            case ScriptModCategory.UTILITY: return 6;
            case ScriptModCategory.CHEAT: return 7;
            case ScriptModCategory.GAMEPLAY: return 8;
            default: return 9;
        }
    }
}
